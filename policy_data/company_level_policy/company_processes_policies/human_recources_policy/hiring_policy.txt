Политика найма

Основные принципы
Централизация и осознанность: обеспечить найм только под конкретные проекты, или задачи, утвержденные СЕО.
Прозрачность: все решения и обсуждения фиксируются в соответствующих каналах.
Эффективность: оперативное выполнение запросов для минимизации простоя.
Объективность: принятие решений на основе критериев, исключая личные предпочтения (желание лида встроить в проект не самого подходящего, а того кто свободен в команде).
Цели
Централизация и осознанность: обеспечить найм только под конкретные проекты, или задачи, утвержденные СЕО.
Оптимизация загрузки: снизить количество сотрудников на «бенче» 
Устранение пробелов в навыках: определить слабо подготовленных сотрудников и принять решение: обучать или увольнять.
Унификация профессиональных навыков: сократить разницу в уровне подготовки сотрудников в разных командах.
Создание эффективных команд проекта: подбирать сотрудников в проекты с учетом особенностей и потребностей проекта, необходимых профессиональных навыков и целей компании, избегая личные предпочтения в распределении загрузки.
Подготовительная работа
Актуализация данных: всегда владеть информацией о свободных сотрудниках.
Резюме сотрудников: каждый разработчик должен иметь актуальное резюме
Алгоритм работы
 Актуализация данных
Тим-координаторы регулярно обновляют информацию о свободных сотрудниках и резюме в базе данных.
 Верификация
Диана/эксперт по бенчу верифицирует данные.
 Запрос на добавление сотрудника
При необходимости добавить разработчика в уже работающий проект или стартовать новый:
тимлид отправляет запрос в канал «Внутренний найм».
запрос должен содержать:
обоснование (зачем/кому/в какой проект/пресейл нужен сотрудник).
детали проекта: название, подписанный уже контракт на проект или нет,  какая ожидается оплачевая загрузка сотрудника в проекте (фулл тайм, парт тайм, рейт, какой скоуп задач и ориентировочный срок), стек технологий, уровень (сеньорность).
Все обсуждения ведутся в треде запроса.
 Поиск кандидатов
HR ищет подходящих кандидатов внутри компании, основываясь на запросе.
HR  проверяет бенч и список скоро доступных сотрудников
HR передает резюме соответствующих кандидатов инициатору запроса.
 Оценка кандидатов
Выбор:
тимлид изучает резюме и выбирает кандидата
передаёт фамилии выбранных кандидатов HR
Собеседование:
представитель команды или заказчик проводит мини-собеседование для проверки кандидата.
 Назначение
Если кандидат подходит, он назначается в проект с согласованной даты.
Если нет, процесс начинается заново с другим кандидатом.
Если на бенче нет релевантных кандидатов, и в скоро доступных тоже - инициируется открытие внешней вакансии. 
 Внешний найм
Внешний найм возможен только под проект или задачи, одобренные СЕО.
Внешний найм открывается только если внутри нет подходящих кандидатов и никто не освобождается в ближайшее время. 
При запросе на внешний поиск, описываем подробную аргументацию почему наши сотрудники не подошли
Если внутренних кандидатов нет: вакансия описывается, утверждается инициатором и запускается в работу.
 Резюме результата
HR пишет итоговый отчёт в канале «Внутренний найм», например:
«Запрос выполнен: разработчик отправлен в команду ХХХХ».
«Запрос не выполнен: причина ХХХХХ».
