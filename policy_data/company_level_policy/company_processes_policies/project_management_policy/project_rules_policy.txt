Может показаться, что описываемый процесс это просто набор формальных правил и процедур, которым нужно следовать. Безусловно, правила и процедуры имеются, и да, им нужно следовать. Но процесс к этому не сводится.

Следование процессу - не самоцель, и следовать принципам важнее, чем выполнять формальные правила. Наш процесс разработки претендует на название "гибкого" (agile), то есть подразумевается, что процесс может и должен изменяться, подстраиваясь под конкретный проект. Однако без достаточной дисциплины "гибкость" быстро превращается в хаос и полное отсутствие какого либо процесса.

Гибкость - это не возможность игнорировать правила, а возможность изменять правила в случае необходимости. При этом изменение правил должно быть полностью осознано, обсуждено и принято всей командой. И новым, изменённым правилам, нужно следовать столь же неукоснительно.

Следуя правилам нужно всегда осознавать, для чего нужно это правило, какой принцип оно реализует и какую проблему решает. Особенно подвержены формальному подходу измерение идеального времени и оценка историй и задач. Не нужно просто "вносить данные" в трекер, если в трекере есть та или иная форма. Не нужно делать бессмысленную работу, вбивая произвольные числа (сделаю столько, сколько надо). Лучше вообще ничего не вводить, чем вводить "левые" данные. Лучше вообще не давать оценок, чем давать "левые" оценки.

Отсюда вывод: если вы не понимаете, зачем нужно следовать тому или иному правилу, нужно сначало разобраться в чём дело, спросить у коллег, у куратора, у авторов этого текста. Сначала понимание сути, потом - следование процессу.