1. Тексты историй соответствуют правилам

2. Текст задач соответствуют правилам

3. Количество законченных, но ещё не принятых историй не превышает двух

4. Количество историй "в процессе" не превышает количества инженеров в команде

5. Все истории оценены, кроме, возможно, незапланированных (unscheduled)

6. Происходит пересмотр оценок при изменении истории

7. Об изменении оценки истории уведомлён заказчик

8. Внесено время работы над задачами

9. Отправлен ежедневный отчёт

10. Минимум два раза в день делается коммит и пуш (а если несколько веток, то и мерж), с адекватными комментариями

11. Запускаются и проходят тесты (daily build)
