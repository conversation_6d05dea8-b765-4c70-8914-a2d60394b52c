В начале работы над проектом необходимо уделить особое внимание "первым шагам":

- подписан контракт

- выставлен первый счёт

- оплачен первый счёт

- техлид/тимлид с командой назначает дату начала работ

- сейл приглашает заказчика на кикофф-митинг

- тимлид/техлид проводит кикофф-митинг, знакомит заказчика с командой.

- сейл с помощью финансового отдела создаёт проект на трекере и убеждается, что у заказчика есть к нему доступ

- сейл переименовывает #presale-* канал в #project-*

- для заказчика создаётся пользователь в слаке и слак-канал проекта

- написана и оценена первая история

- закончена первая история

- принята первая история

- первый деплой на стейджинг


За отслеживание первых шагов проекта отвечают менеджер по продажам и менеджер проекта/тимлид.
Тут всё просто: нужно добиться, чтобы все эти шаги были выполнены чётко и без задержек.

После того, как проект стартовал и все первые шаги выполнены, внимание тимлида/менеджера проекта и заказчика нужно сфокусировать на первом релизе.