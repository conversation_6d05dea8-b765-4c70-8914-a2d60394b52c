Оценка историй необходима заказчику для планирования разработки процесса. Также оценка даёт заказчику обратную связь: наскольно сложны в реализации его требования. Если заказчик думал что "это" сделать легко и просто, а оценка - неделя, то это повод для обсуждения.

Первоначальная оценка дается приближенно и практически никода не совпадает с реально потраченным временем. Это надо понять, принять и объяснить заказчику. Однако регулярное большое превышение оценок не идет на пользу. Надо помнить, что оценка - это инструмент планирования, боязнь превышения некоторых заставляет экономить на хорошем коде, а тех, кто игнорируют превышения, делает черствыми и безразличными к планам клиента, что опускает нас на ступеньку ниже, так как мы не думаем об успехе продукта, а занимаемся только технической частью. Поэтому важно учиться давать оценки близкие к действительности. Вот список практик, позволяющих сделать оценку более точной:

Всегда давать оценку для любой работы. Только так можно прийти к точным оценкам.

Прояснять неопроделенности. Как правило, чем непонятнее задача, тем больше вероятность ее недооценить или переоценить. Например, непонятно описание - надо прояснить детали с клиентом, недостаточно технических знаний - надо попросить время на исследование (это такая же задача, как и все остальные).

Учитывать время для всех этапов работы над задачей согласно процессу проекта.



Для оценки историй предлагается использовать шкалу с ограниченным количеством значений:

- один час

- два часа

- один день

- два дня

- одна неделя

- две недели

Используя такую шкалу давать оценки проще. Например: "За день успеете?" - "Может и успеем, а может и нет" - "А за два?" - "За два дня точно успеем" - оценка два дня.

Также за счёт "огрубления" оценок автоматически добавляется "запас по времени".

Оценку истории и текущей задачи нужно обновлять каждый день. В конце дня все задачи, над которыми сегодня велась работа должны быть переоценены, то есть нужно ответить на вопрос: "Сколько ещё времени нужно, чтобы закончить эту задачу?". Варинты ответа либо "ноль", то есть задача закончена, либо одно из значений по шкале.


Также очень важно обновлять оценку истории при изменении текста истории. Небольшое изменение, добавленное в процессе работы над историей может увеличить сложность вдвое или запросто обнулить уже сделанную работу.

Если при этом изменяется общая оценка истории и дата окончания работы над историей смещается на один или более дней вперёд, то об этом обязательно надо написать заказчику. "Оценка истории изменилась. В процессе работы выяснилось, что..." То есть нужно не просто уведомить заказчика, но и объяснить почему это произошло. Это уведомление должно попасть в ежедневный отчёт.