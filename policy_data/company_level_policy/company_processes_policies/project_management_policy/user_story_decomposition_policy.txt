Перед тем, как начать работу над историей её надо разбить на задачи. Также нужно оценить каждую задачу. 

После того, как история полностью разбита на задачи, оценка истории вычисляется как сумма оценок задач.

Это будет уже более точная оценка, по сравнению с изначальной грубой оценкой всей истории.

Задачи должны быть описаны достаточно подробно, чтобы любой другой программист мог понять о чём идёт речь. Цель разбиения на задачи - составить план работы над историей.

Вот список основных этапов работы над задачей, составленный на основе процесса для нашего старого пректа CareerEvolution.

Можно выделить написание ручного теста еще до оценки - это очень ценная практика, потому что она великолепно помогает разрешить неопределенности, помогает детально представить, как будет работать приложение. 
Предварительный и пост рефактринг - это не отдельные задачи, это неотьемлемые части задачи.

1. Изучить описание задачи. Задать вопросы, если есть неяснысе места в описании.
2. Написать ручной приемочный тест. Это очень важный пункт. Он переводит изучение задачи в практическую плоскость, сразу видны неточности или недостающие детали в описании, проясняются зависимости, что помогает планировать работу над связанными задачами.
3. Оценить задачу. Надо учесть в оценке время для всех этапов работы над задачей.
4. Исследовать техническую реализацию задачи. Цель этого этапа - выбрать лучшую реализацию. Надо изучить само приложение, связанный код, реализацию похожих функций. Чтобы не делать шаблонно устаревшую реализацию, надо изучить новые подходы, проконсультироваться с коллегами, найти информацию по этой теме в интернет.
5. Предварительный рефакторинг. Иногда необходимо подготовить существующий код для интеграции новых функций или обобщить код для добавления аналогичных функций. Для этого надо запланировать предварительный рефакторинг. Если изменяемый код с "душком", то рефакторинг надо делать обязательно, не проходите мимо!
6. Работа над задачей. Используйте методологии с тестированием - TDD, BDD (test -> code).
7. Заключительный рефакторинг. После завершения технической реализации ее надо пересмотреть, если надо, улучшить, проверить имена новых методов и переменных, чтобы они были понятными и соответсвовали предметной области и соглашениям.
8. Ручное тестирование. Надо пройти свой ручной приемочный тест. Возможно, понадобится внести в него исправления и уточнения.