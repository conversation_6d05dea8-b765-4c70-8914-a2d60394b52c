services:
  postgres:
    container_name: app_information_flows_postgres
    image: postgres:16
    volumes:
      - app_information_flows_postgres_data:/var/lib/postgresql/data
    ports:
      - 47474:5432
    restart: always
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DATABASE}
  coding_agent:
    container_name: app_information_flows_coding_agent
    build:
      context: ./information_flows/mcp/coding_agent
    restart: always
    environment:
      - PORT=8001
      - PYTHON_EXECUTOR_PORT=8002
      - OPENAI_API_KEY=${OPENAI_API_KEY}
  clickup:
    container_name: app_information_flows_clickup
    build:
      context: ./information_flows/mcp/clickup
    restart: always
    environment:
      - PORT=8003
      - CLICKUP_API_TOKEN=${CLICKUP_API_TOKEN}
      - CLICKUP_LIST_ID=${CLICKUP_LIST_ID}
      - CLICKUP_TEAM_ID=${CLICKUP_TEAM_ID}
      - TIMEZONE=${TIMEZONE}
  api:
    container_name: app_information_flows_api
    build:
      context: .
    ports:
      - ${PORT}:${PORT}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: always
    env_file:
      - ${ENV_FILE}
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - CODING_AGENT_MCP_HOST=coding_agent
      - CODING_AGENT_MCP_PORT=8001
      - CLICKUP_MCP_HOST=clickup
      - CLICKUP_MCP_PORT=8003
    depends_on:
      - postgres
      - coding_agent
  ui:
    container_name: app_information_flows_ui
    build:
      context: ./information_flows/ui
      args:
        - BACKEND_HOST=${HOST}
        - BACKEND_PORT=${PORT}
    ports:
      - ${FRONTEND_PORT}:${FRONTEND_PORT}
    restart: always
    environment:
      - FRONTEND_PORT=${FRONTEND_PORT}
      - BACKEND_HOST=${HOST}
      - BACKEND_PORT=${PORT}
    depends_on:
      - api
volumes:
  app_information_flows_postgres_data:
