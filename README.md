# Information Flows

The project aims to make communication inside the company more efficient and productive by augmenting and enhancing the information flows with AI.

> #### Note
> To deploy the project with <PERSON><PERSON> Compose go to the [Deploy](#deploy) section.

> #### Note

> To learn more about the ideas and principles laying at the foundation of the project, visit our [Wiki](https://newgit.anadea.co/anadea/oyster/information-flows/-/wikis/home)!

<div align="center">
    <img src="./docs/agent_architecture.svg">
</div>

## Install

1. Create virtual environment:

```shell
uv venv .venv
```

2. Activate virtual environment:

```shell
source .venv/bin/activate
```

3. Install project dependencies:

```shell
uv sync
```

4. Create `.env` file and provide values for environment variables specified in `.env.example`.

5. Set up a system database server and specify `POSTGRES_DATABASE` and other `POSTGRES_*` environment variable parameters of this database to `.env`.

```shell
docker run --name information_flows_postgres -e POSTGRES_PASSWORD=<POSTGRES_PASSWORD> -p <POSTGRES_PORT>:5432 -d postgres
```

6. Run migrations for the system database with (from the project root):

```shell
alembic upgrade head
```

7. Set up a demo database for fake company data and add `DEMO_DATABASE_URL` of this database to `.env`.

8. Run mugrations for the data database with:

```shell
uv run --directory ./information_flows/agents/core_agent/mcp/text2pandas alembic upgrade head
```

9. Go INSIDE the `./information_flows/mcp/coding_agent`, and build an image and run a container according to its `README.md`.

10. Go INSIDE the `./information_flows/ui`, and install dependencies and start the frontend server according to its `README.md`.

## Run

#### Run the API server

```shell
uvicorn information_flows.api.main:app --host 0.0.0.0 --port 45456
```

#### Or with automatic reload on changes

```shell
uvicorn --reload information_flows.api.main:app --host 0.0.0.0 --port 45456
```

#### Run the UI

```shell
npm --prefix information_flows/ui run dev
```

#### Migrate database

1. Create empty migration:

```shell
alembic revision -m "<migration_name>"
```

2. Rollout latest migrations:

```shell
alembic upgrade head
```

3. Revert all migrations:

```shell
alembic downgrade base
```

## Deploy

1. Add `.env.prod` configuration file to the root of the project.

2. Set `OPENTELEMETRY_COLLECTOR_ENDPOINT` to empty value or remove it altogether, if you do not want to collect telemetry data. If you want to collect the telemetry to local infrastructure, then set the value to `http://host.docker.internal:4317`, and deploy the infrastructure (from `information_flows/telemetry`):

```shell
docker compose -p telemetry_information_flows up -d
```

If you do not want to use local infrastructure you can use the remote telemetry collector at `http://**************:4317` setting it as value of the variable. The Grafana dashboard is available at `http://**************:48484`.

3. Deploy the whole system orchestrated by Docker Compose:

```shell
bash deploy.sh
```

4. Open the WEB application on `localhost:FRONTEND_PORT` in your browser.

5. Shutdown all components of the system at once:

```shell
bash shutdown.sh
```

### Run tests

To run a test simulation or rigid scenario from `testing/core_agent` folder:

```shell
python -m unittest -v testing.core_agent.simulation_scenarios.duo_vacation.test
```

If you stop a test with `CTRL+C` then do not forget to delete employees from `employee` table to avoid integration conflicts. If a test stops on its own because of an error or successful completion then the table will be cleaned automatically.
