[project]
name = "information-flows"
version = "0.1.0"
description = "The project aims to make communication inside the company more efficient and productive by augmenting and enhancing the information flows with AI."
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.15.2",
    "asyncpg>=0.30.0",
    "fastapi>=0.115.12",
    "langchain-anthropic>=0.3.13",
    "langchain-community>=0.3.22",
    "langchain-core>=0.3.56",
    "langchain-mcp-adapters>=0.0.11",
    "langchain-openai>=0.3.14",
    "langgraph>=0.3.34",
    "langgraph-checkpoint-postgres>=2.0.21",
    "mcp>=1.8.0",
    "notebook>=7.4.1",
    "pandas>=2.2.3",
    "psycopg-pool>=3.2.6",
    "psycopg2-binary>=2.9.10",
    "psycopg[binary]>=3.2.7",
    "pydantic==2.11.3",
    "pydantic-ai>=0.2.4",
    "pydantic-settings>=2.9.1",
    "pytest-playwright>=0.7.0",
    "rapidfuzz>=3.13.0",
    "rich<14.0.0",
    "sqlalchemy-utils>=0.41.2",
    "sqlalchemy[asyncio]>=2.0.40",
    "sqlparse>=0.5.3",
    "termcolor>=3.1.0",
    "uvicorn>=0.34.2",
    "mlflow>=3.0.0rc1",
    "pip>=25.1.1",
    "aiofiles>=24.1.0",
    "playwright>=1.18.1",
    "pytest>=8.3.5",
    "websockets>=10.4",
    "opentelemetry-distro>=0.55b0",
    "opentelemetry-exporter-otlp>=1.34.0",
    "opentelemetry-exporter-otlp-proto-grpc>=1.34.0",
    "deepeval==3.2.0",
    "networkx>=3.5",
    "datamodel-code-generator>=0.31.2",
]

[tool.uv]
prerelease = "allow"

[dependency-groups]
eval = [
    "evaluate>=0.4.3",
    "hf-xet>=1.1.1",
    "textstat>=0.7.7",
    "torch>=2.7.0",
    "transformers>=4.51.3",
]

[tool.uv.workspace]
members = ["information_flows/mcp/coding_agent"]
