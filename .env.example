ENVIRONMENT=development

TIMEZONE=Europe/Kyiv

HOST=localhost
PORT=

POSTGRES_HOST=localhost
POSTGRES_PORT=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DATABASE=

SQL_DEMO_DATABASE=
SQL_MODEL_NAME=gpt-4o
SQL_FILE_PATH=information_flows/agents/core_agent/mcp/text2pandas/dml.sql

SEARCH_MODEL_NAME=gpt-4o-mini
POLICY_DIR=./policy_data

OPENAI_API_KEY=
ANTHROPIC_API_KEY=

CODING_AGENT_MCP_HOST=localhost
CODING_AGENT_MCP_PORT=49950

# Enable or disable MCP servers (set to false if MCP servers are not running)
ENABLE_MCP_SERVERS=false

# optional
MLFLOW_TRACKING_URI=http://localhost:5050

# optional
LANGSMITH_API_KEY=

# required, your_company_code, Slack abbreviation
TELEMETRY_CORRELATION_ID=

# go to readme for more details
OPENTELEMETRY_COLLECTOR_ENDPOINT=http://localhost:4317

CLICKUP_API_TOKEN=
CLICKUP_LIST_ID=901210656490
CLICKUP_TEAM_ID=20620930

# database will be automatically cleaned up after each test
ALLOW_TEST_CLEANUP=true
