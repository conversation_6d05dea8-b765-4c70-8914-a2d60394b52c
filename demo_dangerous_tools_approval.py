"""
Demo: Human-in-the-Loop for Dangerous Tool Execution

This demo shows how to implement approval requirements for dangerous tools
while maintaining parallel task execution for other tasks.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Set
from uuid import UUID, uuid4
from dataclasses import dataclass, field
import re
import logging

from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode

from information_flows.core.types import Message
from information_flows.core.agent import send_message
from information_flows.agents.core_agent.subsystems.multitasking import Task

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# ============================================================================
# 1. DANGEROUS TOOLS CONFIGURATION
# ============================================================================

@dataclass
class DangerousToolsConfig:
    """Configuration for tools requiring human approval."""
    
    # Exact tool names requiring approval
    dangerous_tools: Set[str] = field(default_factory=lambda: {
        # ClickUp operations
        "submit_vacation",
        "approve_task", 
        "create_task",
        
        # Database mutations
        "execute_sql_mutation",
        "delete_records",
        
        # Time tracking
        "track_time",
        "submit_timesheet"
    })
    
    # Regex patterns for dynamic matching
    dangerous_patterns: List[str] = field(default_factory=lambda: [
        r".*_create_.*",
        r".*_delete_.*", 
        r".*_mutate_.*",
        r".*_approve_.*"
    ])
    
    def is_dangerous(self, tool_name: str) -> bool:
        """Check if a tool requires approval."""
        # Check exact matches
        if tool_name in self.dangerous_tools:
            return True
            
        # Check patterns
        for pattern in self.dangerous_patterns:
            if re.match(pattern, tool_name):
                return True
                
        return False


# ============================================================================
# 2. EXAMPLE TOOLS (SAFE AND DANGEROUS)
# ============================================================================

@tool
async def get_vacation_balance(employee_id: str) -> str:
    """Get vacation balance for an employee (SAFE operation)."""
    # Simulate fetching data
    await asyncio.sleep(0.5)
    return f"Employee {employee_id} has 15 vacation days remaining."


@tool
async def submit_vacation(
    employee_id: str,
    start_date: str,
    end_date: str,
    reason: str = "Vacation"
) -> str:
    """Submit a vacation request (DANGEROUS - requires approval)."""
    # This would actually submit to ClickUp
    await asyncio.sleep(0.5)
    return f"✅ Vacation request submitted for {employee_id} from {start_date} to {end_date}"


@tool
async def list_tasks(employee_id: str) -> str:
    """List tasks for an employee (SAFE operation)."""
    await asyncio.sleep(0.5)
    return f"Employee {employee_id} has 5 active tasks."


@tool  
async def delete_task(task_id: str) -> str:
    """Delete a task (DANGEROUS - requires approval)."""
    await asyncio.sleep(0.5)
    return f"✅ Task {task_id} has been deleted."


# ============================================================================
# 3. APPROVAL STORAGE
# ============================================================================

@dataclass
class ApprovalRequest:
    """Represents a pending approval request."""
    id: UUID
    task_id: str
    tool_name: str
    tool_args: Dict
    status: str = "pending"  # pending, approved, rejected
    created_at: datetime = field(default_factory=datetime.utcnow)
    responded_at: Optional[datetime] = None
    rejection_reason: Optional[str] = None


class ApprovalStore:
    """Simple in-memory store for approval requests."""
    
    def __init__(self):
        self.approvals: Dict[UUID, ApprovalRequest] = {}
        
    async def create_approval(
        self, 
        task_id: str,
        tool_name: str,
        tool_args: Dict
    ) -> UUID:
        """Create a new approval request."""
        approval = ApprovalRequest(
            id=uuid4(),
            task_id=task_id,
            tool_name=tool_name,
            tool_args=tool_args
        )
        self.approvals[approval.id] = approval
        return approval.id
        
    async def get_approval(self, approval_id: UUID) -> Optional[ApprovalRequest]:
        """Get an approval request."""
        return self.approvals.get(approval_id)
        
    async def update_approval(
        self,
        approval_id: UUID,
        status: str,
        reason: Optional[str] = None
    ):
        """Update approval status."""
        approval = self.approvals.get(approval_id)
        if approval:
            approval.status = status
            approval.responded_at = datetime.utcnow()
            approval.rejection_reason = reason


# ============================================================================
# 4. APPROVAL-AWARE TOOL NODE
# ============================================================================

class ApprovalRequiredToolNode(ToolNode):
    """ToolNode that intercepts dangerous tool calls for approval."""
    
    def __init__(
        self,
        tools: List,
        dangerous_config: DangerousToolsConfig,
        approval_store: ApprovalStore,
        messages_key: str = "messages"
    ):
        super().__init__(tools, messages_key)
        self.dangerous_config = dangerous_config
        self.approval_store = approval_store
        
    async def __call__(self, state: Dict, config: Dict) -> Dict:
        """Intercept and check for dangerous tools."""
        messages = state.get(self.messages_key, [])
        if not messages:
            return state
            
        last_message = messages[-1]
        
        # Check tool calls
        for tool_call in getattr(last_message, 'tool_calls', []):
            tool_name = tool_call.get("name", "")
            
            if self.dangerous_config.is_dangerous(tool_name):
                logger.info(f"🔐 Dangerous tool detected: {tool_name}")
                # Request approval instead of executing
                return await self._request_approval(state, config, tool_call)
        
        # No dangerous tools - execute normally
        logger.info("✅ Safe tool execution")
        return await super().__call__(state, config)
        
    async def _request_approval(self, state: Dict, config: Dict, tool_call: Dict) -> Dict:
        """Request approval for dangerous tool."""
        task_id = state.get("current_task_id", "unknown")
        
        # Create approval request
        approval_id = await self.approval_store.create_approval(
            task_id=task_id,
            tool_name=tool_call["name"],
            tool_args=tool_call.get("args", {})
        )
        
        # Format approval message
        args_str = "\n".join([
            f"  - {k}: {v}" 
            for k, v in tool_call.get("args", {}).items()
        ])
        
        approval_message = f"""🔐 **Approval Required**

The assistant wants to execute a potentially dangerous action:

**Tool:** `{tool_call['name']}`
**Arguments:**
{args_str}

Please reply with:
- `/approve {approval_id}` to allow this action
- `/reject {approval_id} [reason]` to deny this action
"""
        
        # Return approval request message
        return {
            **state,
            "approval_pending": {
                "id": approval_id,
                "tool_call": tool_call,
                "message": approval_message
            }
        }


# ============================================================================
# 5. PARALLEL TASK EXECUTOR WITH APPROVAL HANDLING
# ============================================================================

class ParallelTaskExecutor:
    """Executes tasks in parallel with approval support."""
    
    def __init__(self, dangerous_config: DangerousToolsConfig):
        self.dangerous_config = dangerous_config
        self.approval_store = ApprovalStore()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.pending_approvals: Dict[UUID, Dict] = {}
        
    async def process_message(self, message: Message) -> None:
        """Process incoming message."""
        content = message.content.strip()
        
        # Check for approval commands
        if content.startswith("/approve "):
            await self._handle_approval_command(content, approved=True)
            return
            
        elif content.startswith("/reject "):
            await self._handle_approval_command(content, approved=False)
            return
            
        # Normal message - create tasks
        tasks = self._extract_tasks(content)
        
        for task in tasks:
            if task["id"] not in self.running_tasks:
                # Start task execution in background
                task_future = asyncio.create_task(
                    self._execute_task(task)
                )
                self.running_tasks[task["id"]] = task_future
                logger.info(f"▶️  Started task: {task['id']}")
                
    async def _execute_task(self, task: Dict) -> None:
        """Execute a single task."""
        try:
            logger.info(f"🔄 Executing task: {task['id']} - {task['description']}")
            
            # Simulate task execution with tool calls
            if "vacation" in task["description"].lower():
                # This will trigger approval
                tool_name = "submit_vacation"
                tool_args = {
                    "employee_id": "emp-123",
                    "start_date": "2024-01-15",
                    "end_date": "2024-01-19"
                }
            elif "balance" in task["description"].lower():
                # This is safe
                tool_name = "get_vacation_balance"
                tool_args = {"employee_id": "emp-123"}
            else:
                # Default safe operation
                tool_name = "list_tasks"
                tool_args = {"employee_id": "emp-123"}
                
            # Check if dangerous
            if self.dangerous_config.is_dangerous(tool_name):
                await self._request_task_approval(task, tool_name, tool_args)
            else:
                # Execute directly
                result = await self._execute_tool(tool_name, tool_args)
                logger.info(f"✅ Task {task['id']} completed: {result}")
                
        except Exception as e:
            logger.error(f"❌ Task {task['id']} failed: {e}")
        finally:
            self.running_tasks.pop(task["id"], None)
            
    async def _request_task_approval(
        self, 
        task: Dict, 
        tool_name: str, 
        tool_args: Dict
    ) -> None:
        """Request approval for dangerous tool in task."""
        approval_id = await self.approval_store.create_approval(
            task_id=task["id"],
            tool_name=tool_name,
            tool_args=tool_args
        )
        
        self.pending_approvals[approval_id] = {
            "task": task,
            "tool_name": tool_name,
            "tool_args": tool_args
        }
        
        args_str = "\n".join([f"  - {k}: {v}" for k, v in tool_args.items()])
        
        print(f"\n{'='*60}")
        print(f"🔐 APPROVAL REQUIRED for Task: {task['id']}")
        print(f"\nTool: {tool_name}")
        print(f"Arguments:\n{args_str}")
        print(f"\nTo approve: /approve {approval_id}")
        print(f"To reject: /reject {approval_id} [reason]")
        print(f"{'='*60}\n")
        
    async def _handle_approval_command(
        self, 
        command: str, 
        approved: bool
    ) -> None:
        """Handle approval/rejection command."""
        parts = command.split(maxsplit=2)
        if len(parts) < 2:
            return
            
        try:
            approval_id = UUID(parts[1])
            reason = parts[2] if len(parts) > 2 else None
        except ValueError:
            logger.error("Invalid approval ID")
            return
            
        # Get approval request
        approval = await self.approval_store.get_approval(approval_id)
        if not approval or approval.status != "pending":
            logger.error(f"Approval {approval_id} not found or already processed")
            return
            
        # Update status
        await self.approval_store.update_approval(
            approval_id,
            "approved" if approved else "rejected", 
            reason
        )
        
        if approved and approval_id in self.pending_approvals:
            # Resume execution
            pending = self.pending_approvals.pop(approval_id)
            result = await self._execute_tool(
                pending["tool_name"],
                pending["tool_args"]
            )
            logger.info(f"✅ After approval - Task {pending['task']['id']}: {result}")
        else:
            logger.info(f"❌ Tool execution rejected: {reason or 'No reason'}")
            
    async def _execute_tool(self, tool_name: str, tool_args: Dict) -> str:
        """Execute a tool by name."""
        tools = {
            "get_vacation_balance": get_vacation_balance,
            "submit_vacation": submit_vacation,
            "list_tasks": list_tasks,
            "delete_task": delete_task
        }
        
        tool_func = tools.get(tool_name)
        if tool_func:
            return await tool_func.ainvoke(tool_args)
        return f"Unknown tool: {tool_name}"
        
    def _extract_tasks(self, content: str) -> List[Dict]:
        """Extract tasks from message content."""
        tasks = []
        
        # Simple task extraction
        if "vacation" in content.lower() and "balance" in content.lower():
            tasks.append({
                "id": "task-1",
                "description": "Check vacation balance"
            })
            
        if "submit" in content.lower() and "vacation" in content.lower():
            tasks.append({
                "id": "task-2", 
                "description": "Submit vacation request"
            })
            
        if "list" in content.lower() and "tasks" in content.lower():
            tasks.append({
                "id": "task-3",
                "description": "List current tasks"
            })
            
        # Default if no specific tasks found
        if not tasks:
            tasks.append({
                "id": "task-default",
                "description": content
            })
            
        return tasks


# ============================================================================
# 6. DEMO SIMULATION
# ============================================================================

async def simulate_conversation():
    """Simulate a conversation with approval requirements."""
    
    # Initialize system
    dangerous_config = DangerousToolsConfig()
    executor = ParallelTaskExecutor(dangerous_config)
    
    print("\n" + "="*60)
    print("DEMO: Human-in-the-Loop for Dangerous Tools")
    print("="*60 + "\n")
    
    # Simulate user messages
    messages = [
        # This will trigger 2 tasks: one safe, one dangerous
        Message(
            content="Check my vacation balance and submit a vacation request for next week",
            sender_id=uuid4(),
            sender_role="user"
        ),
        # This is a safe operation that will run in parallel
        Message(
            content="Also list my current tasks",
            sender_id=uuid4(),
            sender_role="user"
        )
    ]
    
    # Process initial messages
    for msg in messages:
        print(f"👤 User: {msg.content}")
        await executor.process_message(msg)
        
    # Wait a bit to see pending approval
    await asyncio.sleep(1)
    
    # Simulate approval
    print("\n💬 User approving the vacation request...")
    approval_msg = Message(
        content="/approve " + str(list(executor.pending_approvals.keys())[0]),
        sender_id=uuid4(),
        sender_role="user"
    )
    await executor.process_message(approval_msg)
    
    # Wait for all tasks to complete
    await asyncio.sleep(2)
    
    print("\n" + "="*60)
    print("Demo completed! Key observations:")
    print("- Safe operations (check balance, list tasks) executed immediately")
    print("- Dangerous operation (submit vacation) required approval")
    print("- Other tasks continued while waiting for approval")
    print("="*60)


# ============================================================================
# 7. MAIN ENTRY POINT
# ============================================================================

if __name__ == "__main__":
    asyncio.run(simulate_conversation())
