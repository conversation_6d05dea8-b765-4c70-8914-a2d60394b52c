# UI

A small WEB application providing a GUI for users to interact with agents via chats. Each chat is backed by a WebSocket session to the server, thus providing two-way real-time communication. The application is implemented using the React framework.

## Install

Install dependencies (you need to have NodeJS already installed):

```shell
npm install
```

## Run

Run the application in the develpment mode (do not forget to start the main API server beforehand):

```shell
npm --prefix ./information_flows/ui run dev
```

## Deploy

All commands below must be executed from the inside of this folder.

1. Create `.env` file by example and set all necessary environment variables.

2. Build a Docker image:

```shell
docker build -t information_flows_ui .
```

3. Run a Docker container:

```shell
docker run -d --env-file .env --name information_flows_ui -p 52525:3000 information_flows_ui
```
