{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --host=0.0.0.0"}, "dependencies": {"@ant-design/x": "^1.3.0", "antd": "^5.25.3", "axios": "^1.9.0", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router": "^7.6.3", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}, "prettier": {"singleQuote": false, "tabWidth": 4, "printWidth": 100}}