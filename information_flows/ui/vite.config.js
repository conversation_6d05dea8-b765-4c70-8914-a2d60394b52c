import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig(({ mode }) => {
    process.env = { ...process.env, ...loadEnv(mode, process.cwd(), "") };

    return {
        plugins: [react()],
        preview: {
            port: process.env.FRONTEND_PORT,
        },
        define: {
            BACKEND_HOST: JSON.stringify(process.env.BACKEND_HOST),
            BACKEND_PORT: JSON.stringify(process.env.BACKEND_PORT),
        },
    };
});
