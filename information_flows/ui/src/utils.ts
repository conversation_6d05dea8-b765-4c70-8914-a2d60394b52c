/** Trigger download of the `data` object as a JSON file. */
export function downloadJSON(data: any, filename: string): void {
    const blob = new Blob([JSON.stringify(data)], { type: "application/json" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/** Open upload dialogue and return an uploaded file. */
export function uploadJSON(): Promise<{ filename: string; content: string } | null> {
    return new Promise((resolve, reject) => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "application/json";
        input.style.display = "none";
        document.body.appendChild(input);

        input.addEventListener(
            "change",
            async () => {
                try {
                    const file = input.files?.[0];
                    if (!file) {
                        resolve(null);
                    } else {
                        const content = await file.text();
                        resolve({ filename: file.name, content });
                    }
                } catch (err) {
                    reject(err);
                } finally {
                    document.body.removeChild(input);
                }
            },
            { once: true }
        );

        input.click();
    });
}

export function isToday(date: Date): boolean {
    const now = new Date();
    return (
        date.getDate() === now.getDate() &&
        date.getMonth() === now.getMonth() &&
        date.getFullYear() === now.getFullYear()
    );
}
