import { Outlet, NavLink } from "react-router";
import axios from "axios";
import { GitlabFilled } from "@ant-design/icons";
import { Tooltip } from "antd";

import "./App.css";

import settings from "./settings.ts";

axios.defaults.baseURL = settings.SERVER_BASE_URL;

const App = () => {
    return (
        <div className="app">
            <div className="header">
                <div className="header-logo">
                    <img src="logo-small.jpg" className="header-logo-image" />
                    <h1>Oyster | Information Flows</h1>
                    <h2>Experimental</h2>
                </div>
                <div className="header-page-links">
                    <NavLink
                        to="/"
                        className={({ isActive }) =>
                            isActive ? "header-page-link header-page-link-active" : "header-page-link"
                        }
                    >
                        Playground
                    </NavLink>
                    <NavLink
                        to="/evaluation"
                        className={({ isActive }) =>
                            isActive ? "header-page-link header-page-link-active" : "header-page-link"
                        }
                    >
                        Evaluation
                    </NavLink>
                </div>
                <div className="header-links">
                    <Tooltip title="GitLab">
                        <a
                            className="header-gitlab"
                            href="https://newgit.anadea.co/anadea/oyster/information-flows"
                        >
                            <GitlabFilled />
                        </a>
                    </Tooltip>
                </div>
            </div>
            <Outlet />
            <div className="footer">
                <p>An experimental environment for multi-agent communication and collaboration.</p>
                <p>{new Date().getFullYear()}</p>
            </div>
        </div>
    );
};

export default App;
