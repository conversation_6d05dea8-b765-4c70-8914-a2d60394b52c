import { useState, useEffect } from "react";
import axios from "axios";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

import { Button, Input, Divider, notification, Popover, Spin, Tooltip, Typography } from "antd";
import {
    DownloadOutlined,
    MenuOutlined,
    DeleteOutlined,
    DisconnectOutlined,
} from "@ant-design/icons";
import { MeasurementSession } from "./types";
import { isToday, downloadJSON } from "../utils";

import "./Sidebar.css";

dayjs.extend(duration);

async function fetchSessionHistory(): Promise<Array<MeasurementSession>> {
    const response = await axios.get("/measurement/list");
    return response.data.sessions.map((session) => {
        return MeasurementSession.parse(session);
    });
}

async function fetchActiveSession(): Promise<MeasurementSession | null> {
    const response = await axios.get("/measurement/active");
    const session = response.data.active_session;
    if (session) {
        return MeasurementSession.parse(session);
    }
    return null;
}

/** Convert a tag string into a valid identifier. */
function preprocessTag(tag: string): string {
    tag = tag.trim().toLowerCase();
    tag = tag.replace(/\s+/g, "_").replace(/[^a-zA-Z0-9$_]/g, "");
    if (/^\d/.test(tag)) {
        tag = "_" + tag;
    }
    return tag;
}

function Duration({ start }) {
    const [currentTime, setCurrentTime] = useState<Date>(new Date());
    const elapsed = dayjs.duration(dayjs(currentTime).diff(dayjs(start)));

    useEffect(() => {
        const interval = setInterval(() => setCurrentTime(new Date()), 500);
        return () => {
            clearInterval(interval);
        };
    }, []);

    return <span>{elapsed.format("HH:mm:ss")}</span>;
}

function Sidebar({ chatSessions, onDisconnectAll, onSnapshot }) {
    const [sessionTag, setSessionTag] = useState<string>("");
    const [activeSession, setActiveSession] = useState<MeasurementSession | null>(null);
    const [sessionHistory, setSessionHistory] = useState<Array<MeasurementSession>>([]);
    const [notify, contextHolder] = notification.useNotification();

    useEffect(() => {
        fetchActiveSession().then((session) => setActiveSession(session));
        fetchSessionHistory().then((sessions) => setSessionHistory(sessions));
    }, []);

    function handleSessionStart() {
        const tag = preprocessTag(sessionTag);
        if (tag.length < 5) {
            notify.error({
                message: "Failed to create a session!",
                description: "A tag must be at least 5 charachters long.",
            });
        } else {
            axios
                .post("/measurement/start", { tag: tag })
                .then(() => {
                    fetchActiveSession().then((session) => setActiveSession(session));
                    setSessionTag("");
                })
                .catch(() => {
                    notify.error({
                        message: "Measurement session failure!",
                        description: "Failed to start a measurement session! Try again later.",
                    });
                });
        }
    }

    function handleSessionStop() {
        axios.post("/measurement/stop").then(() => {
            setActiveSession(null);
            fetchSessionHistory().then((sessions) => setSessionHistory(sessions));
        });
    }

    function handleDownload(session: MeasurementSession) {
        const timestamp = dayjs(session.started_at).format("YYYY-MM-DDTHH-mm-ss");
        downloadJSON(session.snapshot, `${session.tag}-${timestamp}.json`);
    }

    function handleDeleteAll() {
        axios.delete("/measurement").then(() => setSessionHistory([]));
    }

    function handleDeleteSession(session: MeasurementSession) {
        axios.delete(`/measurement/${session.session_id}`).then(() => {
            fetchSessionHistory().then((sessions) => setSessionHistory(sessions));
        });
    }

    return (
        <div className="playground-sidebar">
            {contextHolder}
            {chatSessions.size > 1 && (
                <>
                    <div className="chat-pool-control">
                        <span className="sidebar-title">Overview</span>
                        <div className="chat-pool-control-counter">
                            {chatSessions.size} active chats
                        </div>
                        <div className="chat-pool-buttons">
                            <Tooltip title="Close all active chats at once. No data will be deleted.">
                                <Button
                                    onClick={onDisconnectAll}
                                    icon={<DisconnectOutlined />}
                                    className="chat-pool-control-button"
                                >
                                    Disconnect all
                                </Button>
                            </Tooltip>
                            <Tooltip title="Export a snapshot of all the chats in the environment as a JSON file.">
                                <Button
                                    onClick={onSnapshot}
                                    icon={<DownloadOutlined />}
                                    className="chat-pool-control-button"
                                >
                                    Chats snapshot
                                </Button>
                            </Tooltip>
                        </div>
                    </div>
                    <Divider />
                </>
            )}
            <div className="measurement">
                <span className="sidebar-title">Measurements</span>
                {!activeSession && (
                    <div className="measurement-section">
                        <span className="measurement-section-title">New session</span>
                        <Input
                            placeholder="Measurement tag, e.g. test_greeting"
                            className="measurement-start-input"
                            value={sessionTag}
                            onChange={(event) => setSessionTag(event.target.value)}
                        />
                        <Button type="primary" onClick={handleSessionStart}>
                            Start
                        </Button>
                    </div>
                )}
                {activeSession && (
                    <div className="measurement-section">
                        <span className="measurement-section-title">Active session</span>
                        <div className="measurement-active-session">
                            <Spin size="small" />
                            <Typography.Text
                                ellipsis={true}
                                className="measurement-active-session-title"
                            >
                                {activeSession.tag}
                            </Typography.Text>
                            <Button onClick={handleSessionStop}>Stop</Button>
                        </div>
                        <div className="measurement-active-session-duration">
                            <Duration start={activeSession.started_at} />
                        </div>
                    </div>
                )}
                {sessionHistory.length > 0 && (
                    <>
                        <Divider />
                        <div className="measurement-section">
                            <div className="measurement-history-header">
                                <span className="measurement-section-title">Previous sessions</span>
                                <Popover
                                    content={
                                        <div className="measurement-history-menu">
                                            <Button onClick={handleDeleteAll}>
                                                Delete all sessions
                                            </Button>
                                        </div>
                                    }
                                    trigger="click"
                                    placement="bottom"
                                >
                                    <Button type="text">
                                        <MenuOutlined />
                                    </Button>
                                </Popover>
                            </div>
                            <div className="measurement-history">
                                {sessionHistory
                                    .sort((a, b) => b.started_at.getTime() - a.started_at.getTime())
                                    .map((session, i) => (
                                        <div
                                            key={session.session_id}
                                            className="measurement-historical-session"
                                        >
                                            <Typography.Text
                                                ellipsis={true}
                                                className="measurement-historical-session-tag"
                                            >
                                                <Tooltip title={session.tag}>{session.tag}</Tooltip>
                                            </Typography.Text>
                                            <Tooltip
                                                title={`Duration: ${dayjs
                                                    .duration(
                                                        dayjs(session.updated_at).diff(
                                                            dayjs(session.started_at)
                                                        )
                                                    )
                                                    .format("HH:mm:ss")}`}
                                            >
                                                <span className="measurement-historical-session-timestamp">
                                                    {isToday(session.started_at)
                                                        ? session.started_at.toLocaleTimeString()
                                                        : session.started_at.toLocaleString()}
                                                </span>
                                            </Tooltip>
                                            <div className="measurement-historical-session-buttons">
                                                <Tooltip title="Download measurement result">
                                                    <Button
                                                        icon={<DownloadOutlined />}
                                                        type="text"
                                                        onClick={() => handleDownload(session)}
                                                    />
                                                </Tooltip>
                                                <Tooltip title="Delete measurement session">
                                                    <Button
                                                        icon={<DeleteOutlined />}
                                                        type="text"
                                                        onClick={() => handleDeleteSession(session)}
                                                    />
                                                </Tooltip>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}

export default Sidebar;
