import { useState, useEffect } from "react";
import { Button, Input, notification } from "antd";
import axios from "axios";

import "./PreferencesEditor.css";

function PreferenceEditor({ employee }) {
    const [preferences, setPreferences] = useState<string>("");
    const [latestSaved, setLatestSaved] = useState<string>(preferences);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [notify, contextHolder] = notification.useNotification();

    useEffect(() => {
        axios
            .get(`/employee/${employee.id}/preferences`)
            .then((response) => {
                const preferences = response.data.preferences ?? "";
                setPreferences(preferences);
                setLatestSaved(preferences);
            })
            .catch(() => {
                notify.error({
                    message: "Preferences failure!",
                    description: "Failed to load preferences from the server! Try again later.",
                });
            });
    }, []);

    function handleSavePreferences() {
        setIsSaving(true);
        const formattedPreferences = preferences.trim();
        const updatedPreferences = formattedPreferences.length > 1 ? formattedPreferences : null;
        axios
            .post(`/employee/${employee.id}/preferences`, {
                updated_preferences: updatedPreferences,
            })
            .then(() => {
                notify.info({
                    message: "Preferences saved!",
                    description:
                        "Updated preferences for your assistant have been successfully saved.",
                });
                setPreferences(formattedPreferences);
                setLatestSaved(formattedPreferences);
            })
            .catch(() => {
                notify.error({
                    message: "Preferences failure!",
                    description: "Failed to save updated preferences! Try again later.",
                });
            })
            .finally(() => {
                setIsSaving(false);
            });
    }

    return (
        <div className="preference-editor">
            {contextHolder}
            <Input.TextArea
                placeholder="Your preferences of assistant's behavior in different situations... "
                autoSize={{ minRows: 7, maxRows: 7 }}
                className="preference-editor-input"
                value={preferences}
                onChange={(event) => setPreferences(event.target.value)}
                disabled={isSaving}
            />
            <div className="preference-editor-controls">
                <Button
                    type="text"
                    className="preference-editor-clear"
                    onClick={() => setPreferences("")}
                >
                    Clear
                </Button>
                <div className="preference-editor-controls-right">
                    <div className="preference-editor-saved-status">
                        {preferences === latestSaved ? "Saved!" : "Not saved!"}
                    </div>
                    {!isSaving ? (
                        <Button
                            type="primary"
                            className="preference-editor-save"
                            onClick={handleSavePreferences}
                        >
                            Save
                        </Button>
                    ) : (
                        <Button disabled type="default" className="preference-editor-save">
                            Saving...
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
}

export default PreferenceEditor;
