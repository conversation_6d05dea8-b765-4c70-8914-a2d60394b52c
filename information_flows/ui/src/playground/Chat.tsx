import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>over, <PERSON><PERSON><PERSON>, Switch, Spin } from "antd";
import {
    UserOutlined,
    FunctionOutlined,
    MenuOutlined,
    EditOutlined,
    PlayCircleOutlined,
    VerticalAlignBottomOutlined,
} from "@ant-design/icons";
import { Sender, Bubble } from "@ant-design/x";
import Markdown from "react-markdown";
import axios from "axios";

import "./Chat.css";

import settings from "../settings.ts";
import { downloadJSON } from "../utils.ts";
import PreferenceEditor from "./PreferencesEditor.tsx";
import IntrospectionViewer from "./IntrospectionViewer.tsx";
import { Employee, ChatMessage, SessionMessage, AgentEvent } from "./types";
import { boolean } from "zod/v4";

type HeaderContent = "preferences" | "introspection";

/** Scroll down an element with the identifier. */
function scrollDown(identifier: string) {
    setTimeout(() => {
        const element = document.querySelector(identifier);
        if (element && element.scrollHeight > element.clientHeight) {
            element.scroll({ top: element.scrollHeight, behavior: "smooth" });
        }
    }, 500);
}

/** Load a chat message history of an employee from the server. */
export async function fetchMessages(employee: Employee): Promise<Array<ChatMessage>> {
    const response = await axios.get(`/employee/${employee.id}/assistant/history`);
    return response.data.messages.map((message) => {
        return {
            type: "chat",
            content: message.content,
            role: message.role,
        };
    });
}

/** Load a chronology of events emitted by an agent from the serbver. */
export async function fetchEvents(employee: Employee): Promise<Array<AgentEvent>> {
    const response = await axios.get(`/employee/${employee.id}/assistant/events`);
    return response.data.events.map((event) => {
        return AgentEvent.parse(event);
    });
}

function isAgentRunning(events: Array<AgentEvent>): "running" | "idle" {
    let isRunning = false;
    for (const event of events) {
        if (event.type === "agent_start") {
            isRunning = true;
        } else if (event.type === "agent_end" || event.type === "agent_error") {
            isRunning = false;
        }
    }
    return isRunning ? "running" : "idle";
}

function Chat({ employee, onDisconnect }) {
    const websocket = useRef<WebSocket>(null);
    const [inputMessage, setInputMessage] = useState<string>("");
    const [messages, setMessages] = useState<Array<ChatMessage>>([]);
    const [isHeaderOpen, setIsHeaderOpen] = useState<boolean>(false);
    const [headerContent, setHeaderContent] = useState<HeaderContent>("preferences");
    const [agentEvents, setAgentEvents] = useState<Array<AgentEvent>>([]);
    const [followIntrospection, setFollowIntrospection] = useState<boolean>(true);

    const agentStatus = isAgentRunning(agentEvents);

    useEffect(() => {
        websocket.current = new WebSocket(
            `${settings.WEBSOCKET_BASE_URL}/employee/${employee.id}/session`
        );

        websocket.current.onmessage = (event) => {
            const sessionMessage: SessionMessage = JSON.parse(event.data);
            if (sessionMessage.type == "chat") {
                setMessages((messages) => [...messages, sessionMessage]);
                scrollDown(`#chat-messages-${employee.id}`);
            } else {
                const agentEvent = AgentEvent.parse(sessionMessage.payload);
                setAgentEvents((events) =>
                    [...events, agentEvent].sort(
                        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
                    )
                );
                if (!!localStorage.getItem(`${employee.id}-follow-introspection`)) {
                    scrollDown(`#introspection-viewer-${employee.id}`);
                }
            }
        };

        fetchMessages(employee).then((messages) => {
            setMessages(messages);
            scrollDown(`#chat-messages-${employee.id}`);
        });

        fetchEvents(employee).then((events) => {
            setAgentEvents(events);
        });

        localStorage.setItem(`${employee.id}-follow-introspection`, "_");

        return () => {
            if (websocket.current?.readyState === WebSocket.OPEN) {
                websocket.current.close();
            }
        };
    }, []);

    function handleSendMessage() {
        setInputMessage("");
        const chatMessage: ChatMessage = { type: "chat", content: inputMessage, role: "user" };
        setMessages((messages) => [...messages, chatMessage]);
        websocket.current?.send(JSON.stringify(chatMessage));
        scrollDown(`#chat-messages-${employee.id}`);
    }

    function handleReset() {
        axios.post(`/employee/${employee.id}/assistant/reset`).then(() => {
            setMessages([]);
            setAgentEvents([]);
        });
    }

    /** Download the current chat history as a JSON file. */
    function handleExport() {
        const history = { messages: messages };
        downloadJSON(history, `${employee.code}_chat_history.json`);
    }

    /** Open or close the header panel with introspection or preferences panes. */
    function handleSwitchHeader(setContent: HeaderContent) {
        if (!isHeaderOpen || headerContent !== setContent) {
            setHeaderContent(setContent);
            setIsHeaderOpen(true);
        } else {
            setIsHeaderOpen(false);
        }
        scrollDown(`#chat-messages-${employee.id}`);
        scrollDown(`#introspection-viewer-${employee.id}`);
    }

    function handleFollowIntorspection() {
        const follow = !followIntrospection;
        if (follow) {
            localStorage.setItem(`${employee.id}-follow-introspection`, "_");
        } else {
            localStorage.removeItem(`${employee.id}-follow-introspection`);
        }
        setFollowIntrospection(follow);
    }

    const headerAgentStatus = (
        <div className="agent-events-viewer-agent-status">
            {agentStatus === "running" ? (
                <>
                    <Tooltip title="Auto scroll to the end">
                        <Button
                            type="text"
                            icon={<VerticalAlignBottomOutlined />}
                            style={
                                followIntrospection
                                    ? { backgroundColor: "lightgray" }
                                    : { backgroundColor: "transparent" }
                            }
                            onClick={handleFollowIntorspection}
                        />
                    </Tooltip>
                    <Spin size="small" />
                    <span>Agent is running</span>
                </>
            ) : (
                <>
                    <PlayCircleOutlined />
                    <span>Agent is idle</span>
                </>
            )}
        </div>
    );

    return (
        <div className="chat">
            <div className="chat-header">
                <h3 className="chat-header-name">
                    <span>{employee.name}</span>
                    <span>({employee.code})</span>
                </h3>
                <div className="chat-controls">
                    <Button onClick={() => onDisconnect(employee)} color="danger">
                        Disconnect
                    </Button>
                    <Popover
                        className="chat-menu"
                        content={
                            <div className="chat-menu">
                                {messages.length > 0 && (
                                    <Button onClick={handleExport}>Export chat as JSON</Button>
                                )}
                                <Tooltip
                                    title="Clear chat history, agent's memory, and reset the agent to a default state. The action cannot be undone."
                                    placement="bottom"
                                >
                                    <Button onClick={handleReset} variant="solid" color="danger">
                                        Reset assistant
                                    </Button>
                                </Tooltip>
                            </div>
                        }
                        trigger="click"
                        placement="bottom"
                    >
                        <Button>
                            <MenuOutlined />
                        </Button>
                    </Popover>
                </div>
            </div>
            <div className="chat-messages" id={`chat-messages-${employee.id}`}>
                {messages.map((message, i) => (
                    <Bubble
                        className="chat-bubble"
                        key={`${employee.code}-${i}`}
                        id={`${employee.code}-${i}`}
                        placement={message.role === "user" ? "end" : "start"}
                        variant={message.role === "user" ? "outlined" : "filled"}
                        content={<Markdown>{message.content}</Markdown>}
                        avatar={
                            message.role === "user"
                                ? {
                                      icon: <UserOutlined />,
                                      style: { backgroundColor: "#8cd47e" },
                                  }
                                : {
                                      icon: <FunctionOutlined />,
                                      style: { backgroundColor: "#99b7f8" },
                                  }
                        }
                        styles={{ content: { maxWidth: "80%" } }}
                    />
                ))}
                {!messages.length && (
                    <div className="empty-chat">
                        <div>No messages yet...</div>
                    </div>
                )}
            </div>
            {agentStatus === "running" && !isHeaderOpen && (
                <div className="chat-agent-status">
                    <Spin size="small" />
                    <span>Agent is running</span>
                </div>
            )}
            <Sender
                className="chat-input"
                placeholder="Type a message..."
                value={inputMessage}
                onChange={(value) => setInputMessage(value)}
                onSubmit={handleSendMessage}
                prefix={
                    <Tooltip title="Edit preferences">
                        <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => handleSwitchHeader("preferences")}
                            className="open-preferences"
                        />
                    </Tooltip>
                }
                header={
                    <Sender.Header
                        title={
                            <div className="preferences-editor-title">
                                {headerContent === "preferences" ? (
                                    "Preferences"
                                ) : (
                                    <div className="agent-events-viewer-header">
                                        <span>Introspection</span>
                                        {headerAgentStatus}
                                    </div>
                                )}
                            </div>
                        }
                        open={isHeaderOpen}
                        onOpenChange={setIsHeaderOpen}
                    >
                        {headerContent === "preferences" && (
                            <PreferenceEditor employee={employee} />
                        )}
                        {headerContent === "introspection" && (
                            <IntrospectionViewer employee={employee} agentEvents={agentEvents} />
                        )}
                    </Sender.Header>
                }
                footer={
                    <div className="chat-input-controls">
                        <span>Introspection</span>
                        <Switch
                            size="small"
                            value={isHeaderOpen && headerContent === "introspection"}
                            onClick={() => handleSwitchHeader("introspection")}
                        />
                    </div>
                }
            />
        </div>
    );
}

export default Chat;
