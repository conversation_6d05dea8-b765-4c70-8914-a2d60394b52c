.chat-pool {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 80px;
}

.chat {
    width: 600px;
    min-height: 500px;
    max-height: 500px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    border: var(--main-border);
    border-radius: 10px;
    box-shadow: var(--main-shadow);
    padding: 20px;
    break-inside: avoid;
    background-color: white;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: rgb(244, 254, 250);
    border: 1px solid rgb(204, 233, 220);
    border-radius: 5px;
    color: var(--text-black);
}

.chat-header-name {
    margin: 0;
    margin-left: 10px;
    font-size: 12pt;
}

.chat-header-name span:last-child {
    color: rgb(21, 127, 92);
    font-weight: bold;
    margin-left: 10px;
}

.chat-messages {
    flex-grow: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-right: 10px;
    padding-left: 10px;
}

.chat-bubble * {
    margin: 0;
}

.message-conent {
    white-space: pre-wrap;
}

.empty-chat {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgb(158, 154, 150);
    font-family: Roboto, Arial, Helvetica, sans-serif;
}

.chat {
    position: relative;
    bottom: -100px;
    opacity: 0;
    animation: slideIn ease-out forwards;
    transform: scale(0.8);
    animation-duration: 0.08s;
}

@keyframes slideIn {
    to {
        bottom: 0;
        opacity: 1;
        transform: scale(1);
    }
}

.chat-controls {
    display: flex;
    gap: 10px;
}

.chat-menu {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.open-preferences {
    color: rgb(75, 69, 60);
}

.preferences-editor-title {
    padding: 5px;
    color: rgb(137, 147, 157);
    font-size: 10.5pt;
    font-family: Roboto, Arial, Helvetica, sans-serif;
}

.chat-input-controls {
    display: flex;
    gap: 10px;
    font-family: Roboto, Arial, Helvetica, sans-serif;
    padding-left: 10px;
}

.chat-input-controls span {
    font-size: 10pt;
    color: var(--text-black);
}

.chat-agent-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 10px;
    font-size: 10pt;
    font-family: Roboto, Arial, Helvetica, sans-serif;
    color: rgb(137, 147, 157);
}
