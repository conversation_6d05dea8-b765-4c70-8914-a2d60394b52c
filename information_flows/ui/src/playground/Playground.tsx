import { useState, useEffect } from "react";
import { <PERSON>, Button } from "antd";
import axios from "axios";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";

import "./Playground.css";

import { downloadJSON } from "../utils.ts";
import Chat, { fetchMessages } from "./Chat.tsx";
import Sidebar from "./Sidebar.tsx";
import { Employee, ChatMessage } from "./types";

type ActiveSessions = Map<string, Employee>;

interface ChatSnapshot {
    employee: Employee;
    messages: Array<ChatMessage>;
}

interface EnvironmentSnapshot {
    chats: Array<ChatSnapshot>;
}

/** Persist the list of active sessions to the local storage to restore them after reload. */
class SessionStore {
    constructor() {
        const recentSessions = window.localStorage.getItem("recentSessions");
        if (recentSessions === null || !Array.isArray(JSON.parse(recentSessions))) {
            window.localStorage.setItem("recentSessions", JSON.stringify([]));
        }
    }

    public set(activeSessions: ActiveSessions): void {
        const employeeIds = Array.from(activeSessions.keys());
        window.localStorage.setItem("recentSessions", JSON.stringify(employeeIds));
    }

    public get(): Array<string> {
        const employeeIds = window.localStorage.getItem("recentSessions");
        return JSON.parse(employeeIds!);
    }
}

async function fetchEmployees(): Promise<Array<Employee>> {
    const response = await axios.get(`/employee/integration/list`);
    return response.data.integrations
        .map((employee) => {
            return {
                id: employee.local_employee_id,
                name: employee.external_employee_name,
                code: employee.external_employee_id,
            };
        })
        .sort((a, b) => a.name.localeCompare(b.name));
}

async function makeSnapshot(activeSessions: ActiveSessions): Promise<EnvironmentSnapshot> {
    const fetches: Map<Employee, Promise<Array<ChatMessage>>> = new Map();
    for (const employee of activeSessions.values()) {
        fetches.set(employee, fetchMessages(employee));
    }
    // fullfill all promises
    await Promise.all(fetches.values());
    const chats: Array<ChatSnapshot> = [];
    for (const [employee, fetch] of fetches.entries()) {
        const messages = await fetch; // immediately retrieve a value
        chats.push({ employee: employee, messages: messages });
    }
    return { chats: chats };
}

function Playground() {
    const [employees, setEmployees] = useState<Map<string, Employee>>(new Map());
    const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
    const [activeSessions, setActiveSessions] = useState<ActiveSessions>(new Map());
    const [showSidebar, setShowSidebar] = useState<boolean>(true);

    const sessionStore = new SessionStore();

    useEffect(() => {
        fetchEmployees().then((employees) => {
            const loadedEmployees = new Map(employees.map((employee) => [employee.id, employee]));
            setEmployees(loadedEmployees);
            const recentSessions: ActiveSessions = new Map();
            for (const employeeId of sessionStore.get()) {
                if (loadedEmployees.has(employeeId)) {
                    recentSessions.set(employeeId, loadedEmployees.get(employeeId)!);
                }
            }
            setActiveSessions(recentSessions);
            sessionStore.set(recentSessions);
        });
    }, []);

    function handleEmployeeSelect(id: string) {
        setSelectedEmployee(employees.get(id) ?? null);
    }

    /** Add a selected employee to the list of active sessions. */
    function handleConnect() {
        if (selectedEmployee && !activeSessions.has(selectedEmployee.id)) {
            setActiveSessions((sessions) => {
                sessions = new Map(sessions).set(selectedEmployee.id, selectedEmployee);
                sessionStore.set(sessions);
                setSelectedEmployee(null);
                return sessions;
            });
        }
    }

    /** Remove a disconnected employee from the list of active sessions. */
    function handleDisconnect(disconnectedEmployee: Employee) {
        setActiveSessions((sessions) => {
            sessions = new Map(sessions);
            sessions.delete(disconnectedEmployee.id);
            sessionStore.set(sessions);
            return sessions;
        });
    }

    /** Close active sessions of employees. */
    function handleDisconnectAll() {
        setActiveSessions(() => {
            const sessions = new Map();
            sessionStore.set(sessions);
            return sessions;
        });
    }

    /** Export all the chats in the environment as a JSON file. */
    function handleSnaphot() {
        makeSnapshot(activeSessions).then((snapshot) => {
            const now = new Date().toISOString();
            const withoutMilliseconds = now.split(".")[0];
            const timestamp = withoutMilliseconds.replaceAll(":", "-");
            downloadJSON(snapshot, `snapshot-${timestamp}.json`);
        });
    }

    return (
        <div className="playground">
            {showSidebar && (
                <Sidebar
                    chatSessions={activeSessions}
                    onDisconnectAll={handleDisconnectAll}
                    onSnapshot={handleSnaphot}
                />
            )}
            <Button
                className="sidebar-show-button"
                icon={showSidebar ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
                onClick={() => setShowSidebar(!showSidebar)}
            />
            <div className="playground-viewer">
                <div className="connection-form">
                    <Select
                        className="select-employee"
                        placeholder="Select an employee"
                        value={selectedEmployee?.id}
                        options={Array.from(employees.values())
                            .filter((employee) => !activeSessions.has(employee.id))
                            .map((employee) => {
                                return {
                                    value: employee.id,
                                    label: `${employee.name} (${employee.code})`,
                                };
                            })}
                        onChange={handleEmployeeSelect}
                    ></Select>
                    <Button className="connect-employee" type="primary" onClick={handleConnect}>
                        Connect
                    </Button>
                </div>
                <div className="chat-pool">
                    {Array.from(activeSessions.values()).map((employee) => (
                        <Chat
                            key={employee.id}
                            employee={employee}
                            onDisconnect={handleDisconnect}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
}

export default Playground;
