import { Spin, Tooltip } from "antd";
import { CheckCircleOutlined, AlertOutlined, ExclamationCircleOutlined } from "@ant-design/icons";

import "./IntrospectionViewer.css";

import { AgentEvent, parseEventValue } from "./types";
import { isToday } from "../utils";

function findActionEndEvent(
    startEventId: string,
    agentEvents: Array<AgentEvent>
): AgentEvent | undefined {
    return agentEvents.find(
        (targetEvent) =>
            targetEvent.parent?.id === startEventId && targetEvent.type === "action_end"
    );
}

function isFailedTurn(eventId: string, agentEvents: Array<AgentEvent>): boolean {
    const currentEventIndex = agentEvents.findIndex((event) => event.id === eventId);
    for (const event of agentEvents.slice(currentEventIndex + 1)) {
        if (event.type === "agent_error") {
            return true;
        } else if (event.type === "agent_end") {
            return false;
        } else {
            // iterate until `end` or `error` is encountered or none of these events at all
        }
    }
    return false;
}

function IntrospectionViewer({ employee, agentEvents }) {
    const eventItems = agentEvents
        .filter((event: AgentEvent) => !["action_end"].includes(event.type))
        .map((event: AgentEvent) => {
            // include date if the date is not today
            const eventTimestamp = isToday(event.timestamp)
                ? event.timestamp.toLocaleTimeString()
                : event.timestamp.toLocaleString();
            if (event.type === "action_start") {
                const endEvent = findActionEndEvent(event.id, agentEvents);
                const value = !endEvent ? event.value : endEvent.value ?? event.value;
                const [title, content] = parseEventValue(value);
                return (
                    <div key={event.id} className="agent-event agent-event-with-timestamp">
                        <div className="agent-event-body">
                            {!!endEvent ? (
                                <CheckCircleOutlined className="agent-event-status agent-event-action-completed-status" />
                            ) : isFailedTurn(event.id, agentEvents) ? (
                                <ExclamationCircleOutlined className="agent-event-status agent-event-action-error-status" />
                            ) : (
                                <Spin size="small" />
                            )}
                            <span className="agent-event-value">{content}</span>
                        </div>
                        <div className="agent-event-footer">
                            {title && <span className="agent-event-title">{title}</span>}
                            <span className="agent-event-timestamp">{eventTimestamp}</span>
                        </div>
                    </div>
                );
            } else if (event.type === "agent_start") {
                return (
                    <div key={event.id} className="agent-event agent-start-event">
                        <AlertOutlined className="agent-event-status agent-event-start-status" />
                        <span className="agent-event-value">Agent activated!</span>
                    </div>
                );
            } else if (event.type === "agent_end") {
                return (
                    <div key={event.id} className="agent-event agent-end-event">
                        <AlertOutlined className="agent-event-status agent-event-end-status" />
                        <span className="agent-event-value">Agent finished!</span>
                    </div>
                );
            } else if (event.type === "progress") {
                const [title, content] = parseEventValue(event.value);
                return (
                    <div key={event.id} className="agent-event agent-event-with-timestamp">
                        <div className="agent-event-body">
                            <CheckCircleOutlined className="agent-event-status agent-event-action-completed-status" />
                            <span className="agent-event-value">{content}</span>
                        </div>
                        <div className="agent-event-footer">
                            {title && <span className="agent-event-title">{title}</span>}
                            <span className="agent-event-timestamp">{eventTimestamp}</span>
                        </div>
                    </div>
                );
            } else if (event.type === "agent_error") {
                return (
                    <Tooltip
                        title={<pre>{parseEventValue(event.value)[1]}</pre>}
                        styles={{
                            body: {
                                width: "600px",
                                maxHeight: "350px",
                                overflowY: "scroll",
                                fontSize: "9pt",
                            },
                        }}
                    >
                        <div key={event.id} className="agent-event agent-error-event">
                            <ExclamationCircleOutlined className="agent-event-status agent-event-action-error-status" />
                            <span className="agent-event-value">Agent failed with an error!</span>
                        </div>
                    </Tooltip>
                );
            }
        });
    return (
        <div className="agent-events-viewer">
            {eventItems.length ? (
                <div className="agent-events-list" id={`introspection-viewer-${employee.id}`}>
                    {eventItems}
                </div>
            ) : (
                <div className="empty-introspection">No events yet</div>
            )}
        </div>
    );
}

export default IntrospectionViewer;
