import * as zod from "zod/v4";

export interface Employee {
    id: string;
    name: string;
    code: string;
}

export interface ChatMessage {
    type: "chat";
    content: string;
    role: "user" | "agent";
}

export interface SystemMessage {
    type: "system";
    payload: any;
}

export type SessionMessage = ChatMessage | SystemMessage;

export const AgentEventType = zod.literal([
    "agent_start",
    "agent_end",
    "action_start",
    "action_end",
    "progress",
    "agent_error",
]);

export type AgentEventType = zod.infer<typeof AgentEventType>;

export const ActionEvent = zod.object({
    title: zod.string(),
    content: zod.string(),
});

export type ActionEvent = zod.infer<typeof ActionEvent>;

export const AgentEvent = zod.object({
    id: zod.string(),
    agent_id: zod.string(),
    timestamp: zod.coerce.date(),
    type: AgentEventType,
    value: zod.nullable(zod.union([zod.string(), ActionEvent])),
    get parent() {
        return zod.nullable(AgentEvent);
    },
});

export type AgentEvent = zod.infer<typeof AgentEvent>;

/** Retrieve title and content of an event depending on the structure of its value. */
export function parseEventValue(
    value: null | string | ActionEvent
): [string | null, string | null] {
    let title: string | null = null,
        content: string | null = null;
    if (value !== null) {
        if (typeof value !== "string") {
            title = value.title;
            content = value.content;
        } else {
            content = value;
        }
    }
    return [title, content];
}

export const MeasurementSession = zod.object({
    session_id: zod.string(),
    tag: zod.string(),
    started_at: zod.coerce.date(),
    updated_at: zod.coerce.date(),
    snapshot: zod.any(),
});

export type MeasurementSession = zod.infer<typeof MeasurementSession>;
