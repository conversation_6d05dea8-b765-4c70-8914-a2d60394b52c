.preference-editor {
    display: flex;
    flex-direction: column;
    gap: 10px;
    font-family: Roboto, Arial, Helvetica, sans-serif;
    min-height: 210px;
    max-height: 210px;
}

.preference-editor-controls {
    display: flex;
    justify-content: space-between;
}

.preference-editor-controls-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.preference-editor-input {
    width: 100%;
    border: 0;
    outline: 0;
}

.preference-editor-input:focus,
.preference-editor-input:focus-within {
    box-shadow: none;
}

.preference-editor-clear {
    width: 120px;
    color: rgb(118, 129, 129);
    background-color: rgb(247, 247, 247);
}

.preference-editor-save {
    width: 120px;
}

.preference-editor-saved-status {
    font-size: 10pt;
    color: rgb(118, 129, 129);
}
