.playground-sidebar {
    display: flex;
    flex-direction: column;
    min-width: 350px;
    max-width: 350px;
    background-color: white;
    box-shadow: var(--main-shadow);
    border-right: var(--main-border);
    padding: 20px;
}

.sidebar-title {
    margin-bottom: 20px;
    color: rgb(102, 102, 102);
    font-weight: 400;
}

.chat-pool-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.chat-pool-buttons {
    display: flex;
    gap: 10px;
}

.chat-pool-control-button {
    flex-grow: 1;
    height: 35px;
    font-size: 10pt;
}

.chat-pool-control-counter {
    flex-grow: 1;
    font-size: 10.5pt;
    color: gray;
    padding: 8px 15px;
    border: 1px solid gainsboro;
}

.measurement {
    display: flex;
    flex-direction: column;
    font-family: Roboto, Arial, Helvetica, sans-serif;
}

.measurement-section-title {
    font-size: 11pt;
}

.measurement-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.measurement-active-session {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px 20px;
    background-color: aliceblue;
    border: 1px solid rgb(210, 229, 247);
    font-size: 10.5pt;
}

.measurement-active-session-title {
    flex-grow: 1;
}

.measurement-active-session-duration {
    flex-grow: 1;
    padding: 10px;
    text-align: center;
    background-color: rgb(220, 245, 240);
    border: 1px solid rgb(191, 235, 226);
}

.measurement-history {
    overflow-y: auto;
    max-height: 300px;
}

.measurement-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.measurement-history-menu {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.measurement-historical-session {
    display: flex;
    align-items: center;
    padding: 5px 5px;
    padding-left: 10px;
    font-size: 10pt;
    border-bottom: 1px solid rgb(238, 238, 238);
}

.measurement-historical-session-tag {
    flex-grow: 1;
}

.measurement-historical-session-timestamp {
    min-width: max-content;
    margin-right: 5px;
    font-size: 9pt;
    color: rgb(91, 91, 91);
}

.measurement-historical-session-buttons {
    display: flex;
    min-width: max-content;
}

.measurement-historical-session:nth-child(even) {
    background-color: rgb(250, 252, 255);
}

.measurement-historical-session:hover {
    background-color: rgb(230, 243, 255);
    border-bottom-color: rgb(147, 208, 255);
}
