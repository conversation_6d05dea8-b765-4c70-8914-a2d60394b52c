.agent-events-viewer {
    display: flex;
    flex-direction: column;
    min-height: 210px;
    max-height: 210px;
    background-image: linear-gradient(to right top, #c0cde1, #a9d1e0, #9bd3d1, #a3d2b7, #bdcc9b);
    border-radius: 8px;
    box-shadow: 0 0 5px 1px rgb(233, 233, 233);
    overflow: hidden;
    font-family: Roboto, Arial, Helvetica, sans-serif;
}

.agent-events-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Roboto, Arial, Helvetica, sans-serif;
    margin-right: 10px;
}

.agent-events-viewer-agent-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 10.5pt;
}

.agent-events-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow-y: auto;
    padding: 10px;
}

.agent-event {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-grow: 1;
    background-color: rgba(255, 255, 255, 0.808);
    border-radius: 5px;
    border-bottom-left-radius: 0;
    padding: 8px 12px;
    color: #191e1d;
}

.agent-event {
    position: relative;
    bottom: -100px;
    opacity: 0;
    animation: slideIn ease-out forwards;
    transform: scale(0.8);
    animation-duration: 0.08s;
}

@keyframes slideIn {
    to {
        bottom: 0;
        opacity: 1;
        transform: scale(1);
    }
}

.agent-event-with-timestamp {
    flex-direction: column;
    align-items: start;
}

.agent-event-body {
    display: flex;
    align-items: center;
    gap: 15px;
}

.agent-event-value {
    font-size: 10pt;
}

.agent-event-footer {
    width: 100%;
    display: flex;
    align-items: center;
}

.agent-event-title {
    min-width: max-content;
    font-size: 9pt;
    padding: 3px 6px;
    margin-left: 30px;
    border: 1px solid rgb(184, 237, 160);
    border-radius: 4px;
    background-color: #e1f4f0;
    color: #045741;
}

.agent-event-timestamp {
    width: 100%;
    text-align: right;
    font-size: 9pt;
    color: #045741;
}

.agent-event-status {
    font-family: monospace, Arial, Helvetica, sans-serif;
    font-size: 15pt;
    font-weight: bold;
}

.agent-event-action-completed-status {
    color: #048f6a;
}

.agent-event-action-error-status {
    color: rgb(199, 60, 60);
}
.agent-start-event {
    width: max-content;
    border: 2px solid white;
    background-color: #b8efe1;
    color: rgb(6, 58, 33);
    padding: 4px 12px;
}

.agent-start-event .agent-event-value {
    font-size: 9.5pt;
}

.agent-end-event {
    width: max-content;
    border: 2px solid white;
    background-color: #ffcca8;
    color: rgb(103, 33, 8);
    padding: 4px 12px;
}

.agent-end-event .agent-event-value {
    font-size: 9.5pt;
}

.agent-error-event {
    width: max-content;
    border: 2px solid white;
    background-color: #ffb6ac;
    color: rgb(75, 10, 6);
    padding: 4px 12px;
}

.empty-introspection {
    width: 100%;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgb(255, 255, 255);
    font-family: Roboto, Arial, Helvetica, sans-serif;
}
