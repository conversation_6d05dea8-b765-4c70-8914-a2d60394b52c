@import url("https://fonts.googleapis.com/css2?family=Arimo:ital,wght@0,400..700;1,400..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400..700;1,400..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Cookie&display=swap");

* {
    --main-shadow: 0 1px 4px 0.1px rgb(233, 233, 233);
    --main-border: 1px solid rgb(237, 237, 237);
    --text-black: rgb(27, 27, 27);
}

.app {
    display: flex;
    flex-direction: column;
    font-family: Arial, Helvetica, sans-serif;
    min-height: 100vh;
    background-color: rgb(249, 249, 249);
    font-family: "Arimo", Arial, Helvetica, sans-serif;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background-color: white;
    box-shadow: var(--main-shadow);
    border-bottom: var(--main-border);
}

.header-page-links {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-page-link {
    padding: 10px 15px;
    text-decoration: none;
    color: rgb(122, 107, 215);
    font-size: 11pt;
}

.header-page-link-active {
    border-bottom: 1.5px solid rgb(172, 163, 228);
}

.header-logo {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: 70px;
}

.header-logo-image {
    height: 50%;
}

.header-logo h1 {
    margin: 0;
    margin-left: 20px;
    font-size: 15pt;
    color: var(--text-black);
}

.header-logo h2 {
    padding: 5px 15px;
    font-size: 16pt;
    font-family: "Cookie", Arial, Helvetica, sans-serif;
    margin: 0;
    margin-left: 20px;
    color: rgb(149, 111, 244);
    background-color: rgb(244, 239, 255);
    border-radius: 5px;
    user-select: none;
}

.header-links {
    display: flex;
    align-items: center;
    height: 100%;
    margin-right: 70px;
}

.header-gitlab {
    display: block;
    padding: 10px;
    text-decoration: none;
    color: rgb(245, 106, 37);
    font-size: 15pt;
    border-bottom: 1.5px solid rgb(245, 106, 37);
}

.footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid rgb(225, 225, 225);
    background-color: rgb(249, 249, 249);
    z-index: 1;
}

.footer p {
    margin-left: 80px;
    margin-right: 80px;
    margin-top: 40px;
    margin-bottom: 40px;
    color: rgb(187, 187, 187);
    font-size: 10.5pt;
    font-weight: bold;
}
