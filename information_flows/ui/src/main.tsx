import ReactDOM from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router";
import "./index.css";
import App from "./App.tsx";
import Playground from "./playground/Playground.tsx";
import EvaluationViewer from "./evaluation/EvaluationViewer.tsx";

ReactDOM.createRoot(document.getElementById("root")).render(
    <BrowserRouter>
        <Routes>
            <Route path="/" element={<App />}>
                <Route index element={<Playground />} />
                <Route path="evaluation" element={<EvaluationViewer />} />
            </Route>
        </Routes>
    </BrowserRouter>
);
