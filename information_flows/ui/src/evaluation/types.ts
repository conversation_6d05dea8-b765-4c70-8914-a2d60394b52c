import * as zod from "zod/v4";

export const BehavioralAspect = zod.object({
    id: zod.string(),
    agent_id: zod.string(),
    behavior: zod.string(),
    reason: zod.string(),
    effect: zod.string(),
});

export const AspectOccurrence = zod.object({
    id: zod.string(),
    aspect_id: zod.string(),
    explanation: zod.string(),
    similarity: zod.number(),
    location: zod.array(zod.any()),
});

export const EvaluatedAspect = zod.object({
    aspect: BehavioralAspect,
    occurrences: zod.array(AspectOccurrence),
});

export const AspectEvaluationResult = zod.object({
    detected_aspects: zod.array(EvaluatedAspect),
    missed_aspects: zod.array(BehavioralAspect),
});

export const Deviation = zod.object({
    reasoning: zod.string(),
    significance: zod.literal(["low", "medium", "high"]),
});

export const DeviatedAgent = zod.object({
    id: zod.string(),
    name: zod.string(),
    deviations: zod.array(Deviation),
});

export const SimulationFinishReason = zod.literal([
    "success",
    "error",
    "idle",
    "deviation",
    "timeout",
]);

export type SimulationFinishReason = zod.infer<typeof SimulationFinishReason>;

export const SimulationResult = zod.object({
    filename: zod.string(),
    finish_reason: zod.nullable(SimulationFinishReason),
    aspect_evaluation: AspectEvaluationResult,
    scenario_deviations: zod.array(DeviatedAgent),
    raised_exception: zod.nullable(zod.string()),
});

export type SimulationResult = zod.infer<typeof SimulationResult>;
