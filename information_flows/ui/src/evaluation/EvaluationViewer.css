.evaluation-viewer {
    flex-grow: 1;
    display: flex;
    z-index: 0;
}

.upload-background {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.upload-evaluation {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 20px;
    user-select: none;
    width: 250px;
    height: 250px;
    font-size: 11pt;
    border-radius: 30px;
    border: 1.5px dashed rgb(205, 214, 234);
}

.upload-evaluation:hover {
    background-color: rgb(247, 249, 250);
}

.upload-evaluation:active {
    background-color: rgb(242, 246, 249);
}

.evaluation-result-background {
    flex: 1;
    display: flex;
    justify-content: center;
}

.evaluation-result {
    width: 700px;
    height: max-content;
    background-color: white;
    border: var(--main-border);
    box-shadow: var(--main-shadow);
    border-radius: 8px;
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 20px;
    font-family: Robot<PERSON>, Arial, Helvetica, sans-serif;
}

.evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11pt;
}

.evaluation-filename {
    padding: 10px 15px;
    background-color: rgb(250, 250, 250);
    border-radius: 5px;
    border: 1px solid rgb(238, 238, 238);
}

.evaluation-finish-reason {
    padding: 10px 15px;
    border-bottom: 2px solid rgb(221, 221, 221);
}

.evaluation-section {
    margin: 32px 0 0 0;
}

.section-title {
    font-size: 12pt;
    font-weight: 600;
    margin-bottom: 10px;
    margin-top: 18px;
    color: #546e7a;
    border-left: 3px solid #b6b6fb;
    padding-left: 10px;
    letter-spacing: 0.03em;
}

.section-table {
    display: flex;
    flex-direction: column;
    gap: 16px; /* space between aspect groups */
}

.aspect-group {
    border: 1px solid #e9eaf0;
    background: #f7f9fb;
    border-radius: 7px;
    padding: 16px;
    margin-bottom: 0px;
}

.aspect-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0px;
}

.aspect-cell.main {
    flex: 1;
    color: #133445;
}

.aspect-cell.main b {
    color: #6f73d6;
    font-size: 11pt;
}

.aspect-sub {
    font-size: 11pt;
    margin-top: 6px;
    margin-bottom: 4px;
}

.tag {
    font-weight: 600;
    color: #6f73d6;
}

.occurrence-row {
    margin-left: 24px;
    margin-top: 8px;
    padding: 10px 16px;
    border-left: 3px solid #b6b6fb;
    background: #fff;
    border-radius: 5px;
}

.occurrence-cell {
    font-size: 11pt;
}

.occurrence-cell b {
    color: #546e7a;
}

.occ-explanation {
    margin-top: 6px;
    font-size: 11pt;
    color: #455a64;
}

.aspect-group.missed {
    background: #f9e5e5;
    border: 1px solid #ebb6b6;
}
.aspect-cell.main.missed b {
    color: #d42e2e;
}
.deviation-section {
    border-radius: 6px;
    margin-bottom: 18px;
    margin-top: 18px;
    padding-bottom: 8px;
    font-size: 11pt;
}

.deviation-title {
    color: #a56d00;
    border-left: 3px solid #ffd666;
}

.deviated-agent-group {
    padding: 10px 20px;
    border: 1px solid #ffe7b7;
    border-radius: 5px;
    margin-bottom: 6px;
    background: #fff8e4;
}

.agent-row {
    font-weight: 600;
    color: #a96300;
    margin-bottom: 6px;
}

.agent-id-label {
    margin-right: 7px;
}

.agent-id {
    color: #c78800;
}

.deviation-row {
    margin-left: 12px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.deviation-significance {
    padding: 10px 10px;
    border-radius: 10px;
    color: #fff;
    font-size: 0.86em;
    margin-right: 8px;
}

.sig-low {
    background: #7bae53;
}

.sig-medium {
    background: #eba700;
}

.sig-high {
    background: #de1f1f;
}

.deviation-reasoning {
    color: #684800;
}

.exception-section {
    color: white;
    overflow: hidden;
}

.exception-section-body {
    padding: 20px;
    overflow: auto;
    border: 2px solid rgb(89, 37, 37);
    background-color: #252525;
}

.exception-title {
    color: #eb5f55;
    border-left: 3px solid #ff6d6d;
}
