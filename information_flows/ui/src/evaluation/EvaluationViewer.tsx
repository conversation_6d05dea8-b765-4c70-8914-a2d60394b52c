import { PlusOutlined } from "@ant-design/icons";
import { useState } from "react";

import { uploadJSON } from "../utils";
import { SimulationResult } from "./types";

import "./EvaluationViewer.css";

function EvaluationViewer() {
    const [uploadedMeasurement, setUploadedMeasurement] = useState<SimulationResult | null>(null);

    function handleUpload() {
        uploadJSON().then((data) => {
            if (data) {
                const { filename, content } = data;
                const result = SimulationResult.parse({ ...JSON.parse(content), filename });
                setUploadedMeasurement(result);
            }
        });
    }

    return (
        <div className="evaluation-viewer">
            {!uploadedMeasurement ? (
                <div className="upload-background">
                    <div className="upload-evaluation" onClick={handleUpload}>
                        <PlusOutlined />
                        <span>Upload evaluation result</span>
                    </div>
                </div>
            ) : (
                <div className="evaluation-result-background">
                    <div className="evaluation-result">
                        <div className="evaluation-header">
                            <span className="evaluation-filename">
                                {uploadedMeasurement.filename}
                            </span>
                            <span className="evaluation-finish-reason">
                                {uploadedMeasurement.finish_reason?.toUpperCase()}
                            </span>
                        </div>

                        {uploadedMeasurement.finish_reason === "deviation" &&
                            uploadedMeasurement.scenario_deviations &&
                            uploadedMeasurement.scenario_deviations.length > 0 && (
                                <div className="evaluation-section deviation-section">
                                    <div className="section-title deviation-title">
                                        Scenario Deviations
                                    </div>
                                    <div className="section-table">
                                        {uploadedMeasurement.scenario_deviations.map((agent) => (
                                            <div className="deviated-agent-group" key={agent.id}>
                                                <div className="agent-row">
                                                    <span className="agent-id-label">
                                                        Agent of {agent.name}
                                                    </span>
                                                </div>
                                                {agent.deviations.map((dev, idx) => (
                                                    <div className="deviation-row" key={idx}>
                                                        <span
                                                            className={`deviation-significance sig-${dev.significance}`}
                                                        >
                                                            {dev.significance}
                                                        </span>
                                                        <span className="deviation-reasoning">
                                                            {dev.reasoning}
                                                        </span>
                                                    </div>
                                                ))}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        <div className="evaluation-section">
                            <div className="section-title">Detected Aspects</div>
                            <div className="section-table">
                                {uploadedMeasurement.aspect_evaluation.detected_aspects.map(
                                    (evaluatedAspect, i) => (
                                        <div
                                            className="aspect-group"
                                            key={evaluatedAspect.aspect.id}
                                        >
                                            <div className="aspect-row">
                                                <div className="aspect-cell main">
                                                    <b>
                                                        {i + 1}. {evaluatedAspect.aspect.behavior}
                                                    </b>
                                                    <div className="aspect-sub">
                                                        <div>
                                                            <span className="tag">
                                                                Reason:&nbsp;
                                                            </span>
                                                            <span>
                                                                {evaluatedAspect.aspect.reason}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <span className="tag">
                                                                Effect:&nbsp;
                                                            </span>
                                                            <span>
                                                                {evaluatedAspect.aspect.effect}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {evaluatedAspect.occurrences.map((occ, j) => (
                                                <div className="occurrence-row" key={occ.id}>
                                                    <div className="occurrence-cell">
                                                        <b>Occurrence: {j + 1}</b>
                                                        <div>
                                                            <span className="tag">
                                                                Similarity: {occ.similarity}
                                                            </span>
                                                        </div>
                                                        <div className="occ-explanation">
                                                            {occ.explanation}
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )
                                )}
                            </div>
                        </div>

                        {uploadedMeasurement.aspect_evaluation.missed_aspects &&
                            uploadedMeasurement.aspect_evaluation.missed_aspects.length > 0 && (
                                <div className="evaluation-section">
                                    <div className="section-title">Missed Aspects</div>
                                    <div className="section-table">
                                        {uploadedMeasurement.aspect_evaluation.missed_aspects.map(
                                            (aspect, i) => (
                                                <div
                                                    className="aspect-group missed"
                                                    key={aspect.id}
                                                >
                                                    <div className="aspect-row">
                                                        <div className="aspect-cell main missed">
                                                            <b>
                                                                {i + 1}. {aspect.behavior}
                                                            </b>
                                                            <div className="aspect-sub">
                                                                <div>
                                                                    <span className="tag">
                                                                        Reason:&nbsp;
                                                                    </span>
                                                                    <span>{aspect.reason}</span>
                                                                </div>
                                                                <div>
                                                                    <span className="tag">
                                                                        Effect:&nbsp;
                                                                    </span>
                                                                    <span>{aspect.effect}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            )}
                        {uploadedMeasurement.raised_exception && (
                            <div className="evaluation-section exception-section">
                                <div className="section-title exception-title">Exceptions</div>
                                <div className="exception-section-body">
                                    <pre>{uploadedMeasurement.raised_exception}</pre>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}

export default EvaluationViewer;
