"""DTOs for chat API."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel

# Reuse Message from core types instead of duplicating
from information_flows.core.types import Message


class TaskDTO(BaseModel):
    """Task data transfer object."""
    
    id: UUID
    chat_id: UUID
    task_identifier: str
    status: str
    content: str  # JSON content
    created_at: datetime
    completed_at: Optional[datetime] = None


class ChatDTO(BaseModel):
    """Chat data transfer object."""
    
    id: UUID
    employee_id: UUID
    created_at: datetime
    updated_at: datetime
    message_count: int = 0
    task_count: int = 0


class ChatDetailDTO(BaseModel):
    """Detailed chat with messages and tasks."""
    
    id: UUID
    employee_id: UUID
    created_at: datetime
    updated_at: datetime
    messages: List[Message]
    tasks: List[TaskDTO]


class ChatListDTO(BaseModel):
    """List of chats."""
    
    chats: List[ChatDTO]
    total: int
