"""API handlers for chat endpoints."""

from typing import Optional
from uuid import UUI<PERSON>

from fastapi import APIRout<PERSON>, Depends, HTTPEx<PERSON>, Query
from sqlalchemy import func, select

from information_flows.api.chat.chat_service import ChatService
from information_flows.api.chat.dto import (
    ChatDTO,
    ChatDetailDTO,
    ChatListDTO,
    TaskDTO,
)
from information_flows.core.types import Message
from information_flows.database.chat import ChatModel, TaskModel
from information_flows.database.message import MessageModel
from information_flows.database.session import AsyncSessionLocal

router = APIRouter(prefix="/api/chats", tags=["chats"])


# Dependency to get ChatService instance
def get_chat_service() -> ChatService:
    """Get a ChatService instance with the session factory."""
    return ChatService(AsyncSessionLocal)


@router.get("/{employee_id}", response_model=ChatListDTO)
async def get_employee_chats(
    employee_id: UUID,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
) -> ChatListDTO:
    """Get all chats for an employee."""
    async with AsyncSessionLocal() as session:
        # Count total
        count_query = select(func.count()).select_from(ChatModel).where(
            ChatModel.employee_id == employee_id
        )
        total = await session.scalar(count_query)
        
        # Get chats with counts
        query = (
            select(
                ChatModel,
                func.count(TaskModel.id).label("task_count")
            )
            .outerjoin(TaskModel, ChatModel.id == TaskModel.chat_id)
            .where(ChatModel.employee_id == employee_id)
            .group_by(ChatModel.id)
            .order_by(ChatModel.updated_at.desc())
            .limit(limit)
            .offset(offset)
        )
        
        result = await session.execute(query)
        rows = result.all()
        
        chats = []
        for chat, task_count in rows:
            # Get message count separately
            message_count_query = (
                select(func.count())
                .select_from(MessageModel)
                .join(ChatModel.messages)
                .where(ChatModel.id == chat.id)
            )
            message_count = await session.scalar(message_count_query) or 0
            
            chats.append(
                ChatDTO(
                    id=chat.id,
                    employee_id=chat.employee_id,
                    created_at=chat.created_at,
                    updated_at=chat.updated_at,
                    message_count=message_count,
                    task_count=task_count or 0,
                )
            )
        
        return ChatListDTO(chats=chats, total=total or 0)


@router.get("/{employee_id}/{chat_id}", response_model=ChatDetailDTO)
async def get_chat_detail(
    employee_id: UUID,
    chat_id: UUID,
    chat_service: ChatService = Depends(get_chat_service),
) -> ChatDetailDTO:
    """Get detailed chat information including messages and tasks."""
    # Verify chat exists and belongs to employee
    chat = await chat_service.get_or_create_chat(employee_id, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    
    # Get messages
    message_models = await chat_service.get_chat_messages(chat_id)
    messages = [
        Message(
            id=msg.message_id,
            content=msg.content,
            sender_id=msg.sender_id,
            sender_role=msg.sender_role,
            receiver_id=msg.receiver_id,
            timestamp=msg.timestamp,
            chat_id=chat_id,
        )
        for msg in message_models
    ]
    
    # Get tasks
    tasks = await chat_service.get_chat_tasks(chat_id)
    task_dtos = [
        TaskDTO(
            id=task.id,
            chat_id=task.chat_id,
            task_identifier=task.task_identifier,
            status=task.status,
            content=task.content,
            created_at=task.created_at,
            completed_at=task.completed_at,
        )
        for task in tasks
    ]
    
    # Get chat details
    async with AsyncSessionLocal() as session:
        result = await session.execute(
            select(ChatModel).where(ChatModel.id == chat_id)
        )
        chat_model = result.scalar_one_or_none()
    
    if not chat_model:
        raise HTTPException(status_code=404, detail="Chat not found")
    
    return ChatDetailDTO(
        id=chat_model.id,
        employee_id=chat_model.employee_id,
        created_at=chat_model.created_at,
        updated_at=chat_model.updated_at,
        messages=messages,
        tasks=task_dtos,
    )


@router.get("/{employee_id}/{chat_id}/tasks", response_model=list[TaskDTO])
async def get_chat_tasks(
    employee_id: UUID,
    chat_id: UUID,
    status: Optional[str] = Query(None, regex="^(active|completed|queued|failed)$"),
    chat_service: ChatService = Depends(get_chat_service),
) -> list[TaskDTO]:
    """Get tasks for a specific chat."""
    tasks = await chat_service.get_chat_tasks(chat_id)
    
    # Filter by status if provided
    if status:
        tasks = [task for task in tasks if task.status == status]
    
    return [
        TaskDTO(
            id=task.id,
            chat_id=task.chat_id,
            task_identifier=task.task_identifier,
            status=task.status,
            content=task.content,
            created_at=task.created_at,
            completed_at=task.completed_at,
        )
        for task in tasks
    ]
