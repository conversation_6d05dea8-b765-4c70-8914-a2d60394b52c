"""Service for managing chats and tasks."""

import json
from datetime import datetime
from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy.orm import selectinload

from information_flows.agents.core_agent.subsystems.multitasking import Task
from information_flows.core.types import Message
from information_flows.database.chat import ChatModel, TaskModel, chat_message_association
from information_flows.database.message import MessageModel


class ChatService:
    """Service for managing chats and tasks."""

    def __init__(self, session_factory: async_sessionmaker[AsyncSession]):
        """Initialize the ChatService with a session factory.
        
        Args:
            session_factory: An async sessionmaker instance to create database sessions
        """
        self.session_factory = session_factory

    async def get_or_create_chat(
        self, employee_id: UUID, chat_id: Optional[UUID] = None
    ) -> UUID:
        """Get existing chat or create a new one."""
        async with self.session_factory() as session:
            if chat_id:
                # Check if chat exists
                result = await session.execute(
                    select(ChatModel).where(
                        ChatModel.id == chat_id,
                        ChatModel.employee_id == employee_id
                    )
                )
                chat = result.scalar_one_or_none()
                if chat:
                    return chat.id
            
            # Create new chat
            new_chat_id = chat_id or uuid4()
            chat = ChatModel(
                id=new_chat_id,
                employee_id=employee_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(chat)
            await session.commit()
            return new_chat_id

    async def add_message_to_chat(
        self, chat_id: UUID, message: Message
    ) -> None:
        """Add a message to a chat."""
        async with self.session_factory() as session:
            # First, ensure the message is persisted
            db_message = await session.get(MessageModel, message.id)
            if not db_message:
                # Create message in database if it doesn't exist
                db_message = MessageModel(
                    message_id=message.id,
                    content=str(message.content),
                    sender_id=message.sender_id,
                    sender_role=message.sender_role,
                    receiver_id=message.receiver_id,
                    timestamp=message.timestamp
                )
                session.add(db_message)
            
            # Add association
            await session.execute(
                chat_message_association.insert().values(
                    chat_id=chat_id,
                    message_id=message.id,
                    created_at=datetime.now()
                )
            )
            
            # Update chat's updated_at
            await session.execute(
                update(ChatModel)
                .where(ChatModel.id == chat_id)
                .values(updated_at=datetime.now())
            )
            
            await session.commit()

    async def create_task(
        self,
        chat_id: UUID,
        task: Task,
        status: str = "active"
    ) -> UUID:
        """Create a task in the database."""
        async with self.session_factory() as session:
            task_model = TaskModel(
                chat_id=chat_id,
                task_identifier=task.identifier,
                status=status,
                content=json.dumps(task.model_dump()),  # Serialize the entire task
                created_at=datetime.now()
            )
            session.add(task_model)
            await session.commit()
            return task_model.id

    async def update_task_status(
        self,
        task_id: UUID,
        status: str
    ) -> None:
        """Update the status of a task."""
        async with self.session_factory() as session:
            values = {"status": status}
            if status == "completed":
                values["completed_at"] = datetime.now().strftime(format="%m/%d/%Y %I:%M %p")
            
            await session.execute(
                update(TaskModel)
                .where(TaskModel.id == task_id)
                .values(**values)
            )
            await session.commit()

    async def get_chat_messages(
        self, chat_id: UUID
    ) -> List[MessageModel]:
        """Get all messages for a chat."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(MessageModel)
                .join(chat_message_association)
                .where(chat_message_association.c.chat_id == chat_id)
                .order_by(MessageModel.timestamp)
            )
            return result.scalars().all()

    async def get_chat_tasks(
        self, chat_id: UUID
    ) -> List[TaskModel]:
        """Get all tasks for a chat."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(TaskModel)
                .where(TaskModel.chat_id == chat_id)
                .order_by(TaskModel.created_at)
            )
            return result.scalars().all()

    async def get_employee_chats(
        self, employee_id: UUID
    ) -> List[ChatModel]:
        """Get all chats for an employee."""
        async with self.session_factory() as session:
            result = await session.execute(
                select(ChatModel)
                .where(ChatModel.employee_id == employee_id)
                .order_by(ChatModel.updated_at.desc())
                .options(selectinload(ChatModel.tasks))
            )
            return result.scalars().all()
