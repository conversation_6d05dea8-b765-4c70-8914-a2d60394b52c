from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from information_flows.api import setup
from information_flows.api.chat.handlers import router as chat_router
from information_flows.api.employee.handlers import router as employee_router
from information_flows.api.measurement.handlers import router as measurement_router

app = FastAPI(lifespan=setup.lifespan)
app.include_router(employee_router)
app.include_router(measurement_router)
app.include_router(chat_router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
