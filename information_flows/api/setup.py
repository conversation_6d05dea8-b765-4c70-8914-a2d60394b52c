from contextlib import asynccontextmanager
from typing import Annotated

from fastapi import Depends, FastAPI

from information_flows.api.employee.service import EmployeeService
from information_flows.api.employee.session import SessionService
from information_flows.api.measurement.service import MeasurementService
from information_flows.core.caching import DatabaseLLMCache
from information_flows.core.pool import <PERSON><PERSON><PERSON>
from information_flows.core.telemetry import initialize_telemetry
from information_flows.database.setup import (
    async_connection_pool,
    async_engine,
    sync_engine,
)

initialize_telemetry()

agent_pool = AgentPool(async_connection_pool, async_engine, redelivery=False)

employee_service = EmployeeService(async_engine, agent_pool)
EmployeeServiceDep = Annotated[EmployeeService, Depends(lambda: employee_service)]

session_service = SessionService(async_engine, agent_pool)
SessionServiceDep = Annotated[SessionService, Depends(lambda: session_service)]

measurement_service = MeasurementService(async_engine)
MeasurementServiceDep = Annotated[
    MeasurementService, Depends(lambda: measurement_service)
]


@asynccontextmanager
async def lifespan(app: FastAPI):
    await async_connection_pool.open()
    # DatabaseLLMCache.enable(sync_engine)
    agent_pool.start()
    employee_service.start()
    yield
    try:
        await agent_pool.shutdown()
    finally:
        employee_service.shutdown()
        # DatabaseLLMCache.disable()
        await async_connection_pool.close()
