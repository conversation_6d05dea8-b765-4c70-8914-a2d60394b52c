import asyncio
import json
import logging
import os
from typing import Optional
from uuid import UUID

import sqlalchemy as sa
from fastapi.exceptions import HTTPException
from sqlalchemy.exc import IntegrityError, NoResultFound
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from starlette import status

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.api.employee.dto import AgentEvent, ChatMessage, Integration
from information_flows.core.agent import AgentMCPConfig
from information_flows.core.pool import AgentPool
from information_flows.database.employee import EmployeeModel
from information_flows.database.event import EventModel
from information_flows.database.integration import IntegrationModel
from information_flows.database.message import MessageModel
from information_flows.database.relation import RelationModel
from information_flows.settings import settings

logger = logging.getLogger("employee_service")
logger.setLevel(logging.INFO)

logger.info(f"Running in the `{settings.ENVIRONMENT}` environment mode.")

# Configure MCP servers only if enabled
if settings.ENABLE_MCP_SERVERS:
    MCP_SERVERS: AgentMCPConfig = {
        "policy": {
            "command": "python",
            "args": ["-m", "information_flows.agents.core_agent.mcp.policy_adv.server"],
            "transport": "stdio",
            "env": dict(os.environ),
        },
        "clickup": {
            "url": f"http://{settings.CLICKUP_MCP_HOST}:{settings.CLICKUP_MCP_PORT}/sse",
            "transport": "sse",
        },
        "database": {
            "command": "python",
            "args": [
                "-m",
                "information_flows.agents.core_agent.mcp.text2pandas.server",
            ],
            "transport": "stdio",
            "env": dict(os.environ),
        },
        "coding_agent": {
            "url": f"http://{settings.CODING_AGENT_MCP_HOST}:{settings.CODING_AGENT_MCP_PORT}/sse",
            "transport": "sse",
        },
    }
else:
    MCP_SERVERS: AgentMCPConfig = {}
    logger.info("MCP servers are disabled. Running without external tool integrations.")


class EmployeeService:
    def __init__(
        self,
        engine: AsyncEngine,
        agent_pool: AgentPool,
        *,
        synchronizer_frequency: float = 1.0,
    ):
        self.engine = engine
        self.agent_pool = agent_pool
        self.synchronizer_kill_event = asyncio.Event()
        self.synchronizer_frequency = synchronizer_frequency

    def start(self) -> None:
        """Start background database-agent pool synchronization."""
        asyncio.create_task(self.synchronize_agents())

    def shutdown(self) -> None:
        """Stop background database-agent pool synchronization."""
        self.synchronizer_kill_event.set()

    async def create_employee(self, employee_id: Optional[UUID] = None) -> UUID:
        try:
            async with AsyncSession(self.engine) as session:
                async with session.begin():
                    employee = EmployeeModel(employee_id=employee_id)
                    session.add(employee)
                    await session.flush()
                    return employee.employee_id
        except IntegrityError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Employee already exists",
            )

    async def delete_employee(self, employee_id: UUID) -> None:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                employee = await session.execute(
                    sa.delete(EmployeeModel)
                    .where(EmployeeModel.employee_id == employee_id)
                    .returning(EmployeeModel)
                )
                if not employee.scalar():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No employee found with ID: {employee_id}",
                    )

    async def get_static_preferences(self, employee_id: UUID) -> str | None:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                result = await session.execute(
                    sa.select(EmployeeModel.static_preferences).where(
                        EmployeeModel.employee_id == employee_id
                    )
                )
                try:
                    return result.scalars().one()
                except NoResultFound:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No employee found with ID: {employee_id}",
                    )

    async def update_static_preferences(
        self, employee_id: UUID, updated: str | None
    ) -> None:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                updated_employee_id = await session.scalar(
                    sa.update(EmployeeModel)
                    .where(EmployeeModel.employee_id == employee_id)
                    .values(static_preferences=updated)
                    .returning(EmployeeModel.employee_id)
                )
                if not updated_employee_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No employee found with ID: {employee_id}",
                    )

    async def create_relation(
        self, from_employee_id: UUID, to_employee_id: UUID
    ) -> None:
        if from_employee_id == to_employee_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot create relation with itself",
            )
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                relation = await session.scalar(
                    sa.select(RelationModel).where(
                        RelationModel.from_employee_id == from_employee_id,
                        RelationModel.to_employee_id == to_employee_id,
                    )
                )
                if not relation:
                    relation = RelationModel(
                        from_employee_id=from_employee_id,
                        to_employee_id=to_employee_id,
                    )
                    session.add(relation)

                relation = await session.scalar(
                    sa.select(RelationModel).where(
                        RelationModel.from_employee_id == to_employee_id,
                        RelationModel.to_employee_id == from_employee_id,
                    )
                )
                if not relation:
                    relation = RelationModel(
                        from_employee_id=to_employee_id,
                        to_employee_id=from_employee_id,
                    )
                    session.add(relation)

    async def delete_relation(
        self, from_employee_id: UUID, to_employee_id: UUID
    ) -> None:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                await session.execute(
                    sa.delete(RelationModel).where(
                        RelationModel.from_employee_id == from_employee_id,
                        RelationModel.to_employee_id == to_employee_id,
                    )
                )
                await session.execute(
                    sa.delete(RelationModel).where(
                        RelationModel.from_employee_id == to_employee_id,
                        RelationModel.to_employee_id == from_employee_id,
                    )
                )

    async def create_integration(
        self,
        local_employee_id: UUID,
        external_employee_id: str,
        external_employee_name: str,
    ) -> None:
        try:
            async with AsyncSession(self.engine) as session:
                async with session.begin():
                    integration = IntegrationModel(
                        local_employee_id=local_employee_id,
                        external_employee_id=external_employee_id,
                        external_employee_name=external_employee_name,
                    )
                    session.add(integration)
        except IntegrityError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Integration already exists",
            )

    async def delete_integration(self, local_employee_id: UUID) -> None:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                integration = await session.execute(
                    sa.delete(IntegrationModel)
                    .where(IntegrationModel.local_employee_id == local_employee_id)
                    .returning(IntegrationModel)
                )
                if not integration.scalar():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No integration found for employee with ID: {local_employee_id}",
                    )

    async def list_integrations(self) -> list[Integration]:
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                integrations = await session.scalars(sa.select(IntegrationModel))
                return [
                    Integration(
                        external_employee_id=integration.external_employee_id,
                        external_employee_name=integration.external_employee_name,
                        local_employee_id=integration.local_employee_id,
                    )
                    for integration in integrations.all()
                ]

    async def synchronize_agents(self) -> None:
        """Ensure that all employees in the database have running assistants in the agent pool."""
        retry_count = 0
        max_retries = 10
        retry_delay = 2.0
        
        while not self.synchronizer_kill_event.is_set():
            try:
                async with AsyncSession(self.engine) as session:
                    async with session.begin():
                        result = await session.scalars(sa.select(EmployeeModel))
                        employees = [employee.employee_id for employee in result.all()]
                
                # Reset retry count on successful query
                retry_count = 0
                
                # spawn all missing agents
                for employee_id in employees:
                    if not self.agent_pool.has_running_agent(employee_id):
                        await self.agent_pool.spawn(
                            id=employee_id,
                            agent_factory=AgentFactory(
                                employee_id=employee_id,
                                specification_dir="./information_flows/agents/core_agent/specification",
                                mcp_servers=MCP_SERVERS,
                            ),
                        )
                # kill all orphaned agents (without employees)
                for agent_id in self.agent_pool.list_running_agents():
                    if agent_id not in employees:
                        await self.agent_pool.kill(agent_id)
            except Exception as e:
                retry_count += 1
                if retry_count <= max_retries:
                    logger.warning(
                        f"Error in synchronize_agents (attempt {retry_count}/{max_retries}): {e}. "
                        f"Retrying in {retry_delay} seconds..."
                    )
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"Failed to synchronize agents after {max_retries} attempts: {e}")
                    # Continue running but log the error
                    
            await asyncio.sleep(self.synchronizer_frequency)

    async def reset_assistant(self, employee_id: UUID) -> None:
        """Delete all agent state checkpoints and messages with the agent."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                employee = await session.scalar(
                    sa.select(EmployeeModel).where(
                        EmployeeModel.employee_id == employee_id
                    )
                )
                if not employee:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"No employee found with ID: {employee_id}",
                    )
                for table in ["checkpoints", "checkpoint_blobs", "checkpoint_writes"]:
                    await session.execute(
                        sa.text(
                            f"DELETE FROM {table} WHERE thread_id=:thread_id"
                        ).bindparams(thread_id=employee.thread_id)
                    )
                await session.execute(
                    sa.delete(MessageModel).where(
                        sa.or_(
                            MessageModel.sender_id == employee_id,
                            MessageModel.receiver_id == employee_id,
                        )
                    )
                )
                await session.execute(
                    sa.delete(EventModel).where(EventModel.agent_id == employee_id)
                )
                # await self.agent_pool.kill(employee_id)

    async def message_history(self, employee_id: UUID) -> list[ChatMessage]:
        """Retrieve a history of messages between a human and an agent."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                messages = await session.scalars(
                    sa.select(MessageModel)
                    .where(
                        MessageModel.sender_id == employee_id,
                        MessageModel.receiver_id == employee_id,
                    )
                    .order_by(sa.asc(MessageModel.timestamp))
                )
                return [
                    ChatMessage(content=message.content, role=message.sender_role)
                    for message in messages
                ]

    async def event_chronology(self, employee_id: UUID) -> list[AgentEvent]:
        """Retrieve a chronology of events emitted by an agent."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                events = await session.scalars(
                    sa.select(EventModel)
                    .where(EventModel.agent_id == employee_id)
                    .order_by(sa.asc(EventModel.timestamp))
                )
                return [
                    AgentEvent(
                        id=event.event_id,
                        agent_id=event.agent_id,
                        timestamp=event.timestamp,
                        type=event.type,
                        value=json.loads(event.value) if event.value else None,
                        parent=json.loads(event.parent) if event.parent else None,
                    )
                    for event in events
                ]
