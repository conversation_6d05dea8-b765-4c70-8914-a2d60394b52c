from datetime import datetime
from typing import Annotated, Any, Literal, Optional
from uuid import UUID

from pydantic import BaseModel, Field, StringConstraints, field_serializer

from information_flows.core.types import AgentEventType


class CreateRelationPayload(BaseModel):
    from_employee_id: UUID
    to_employee_id: UUID


class CreateEmployeeResponse(BaseModel):
    employee_id: UUID


class DeleteRelationPayload(BaseModel):
    from_employee_id: UUID
    to_employee_id: UUID


class GetPreferencesResponse(BaseModel):
    preferences: str | None


class UpdatePreferencesPayload(BaseModel):
    updated_preferences: Annotated[str, StringConstraints(min_length=1)] | None


class CreateIntegrationPayload(BaseModel):
    external_employee_id: str
    external_employee_name: str


class Integration(BaseModel):
    external_employee_id: str
    external_employee_name: str
    local_employee_id: UUID


class ListIntegrationsResponse(BaseModel):
    integrations: list[Integration]


class ChatMessage(BaseModel):
    type: Literal["chat"] = "chat"
    content: str
    role: Literal["user", "agent"]
    chat_id: Optional[UUID] = None


class SystemMessage(BaseModel):
    type: Literal["system"] = "system"
    payload: Any


class SessionMessage(BaseModel):
    value: ChatMessage | SystemMessage = Field(discriminator="type")

    @classmethod
    def validate(cls, data: Any) -> ChatMessage | SystemMessage:
        return SessionMessage(**{"value": data}).value


class MessageHistoryResponse(BaseModel):
    messages: list[ChatMessage]


class AgentEvent(BaseModel):
    id: UUID
    agent_id: UUID
    timestamp: datetime
    type: AgentEventType
    value: Optional[Any] = None
    parent: Optional["AgentEvent"] = None

    @field_serializer("timestamp", when_used="json")
    def serialize_timestamp(self, timestamp: datetime) -> float:
        return timestamp.timestamp() * 1000


class EventChronologyResponse(BaseModel):
    events: list[AgentEvent]
