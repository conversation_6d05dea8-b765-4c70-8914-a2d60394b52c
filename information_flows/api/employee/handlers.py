from uuid import UUID

from fastapi import APIRouter, WebSocket

from information_flows.api.employee.dto import (
    CreateEmployeeResponse,
    CreateIntegrationPayload,
    CreateRelationPayload,
    DeleteRelationPayload,
    EventChronologyResponse,
    GetPreferencesResponse,
    ListIntegrationsResponse,
    MessageHistoryResponse,
    UpdatePreferencesPayload,
)
from information_flows.api.setup import EmployeeServiceDep, SessionServiceDep

router = APIRouter(prefix="/employee")


@router.post("")
async def create_employee(employee_service: EmployeeServiceDep):
    id = await employee_service.create_employee()
    return CreateEmployeeResponse(employee_id=id)


@router.delete("/{employee_id}")
async def delete_employee(
    employee_id: UUID,
    employee_service: EmployeeServiceDep,
):
    await employee_service.delete_employee(employee_id)


@router.get("/{employee_id}/preferences")
async def get_preferences(
    employee_id: UUID,
    employee_service: EmployeeServiceDep,
):
    preferences = await employee_service.get_static_preferences(employee_id)
    return GetPreferencesResponse(preferences=preferences)


@router.post("/{employee_id}/preferences")
async def update_preferences(
    employee_id: UUID,
    data: UpdatePreferencesPayload,
    employee_service: EmployeeServiceDep,
):
    await employee_service.update_static_preferences(
        employee_id, data.updated_preferences
    )


@router.post("/relation")
async def create_relation(
    data: CreateRelationPayload, employee_service: EmployeeServiceDep
):
    """Idempotent request to connect assistants of two employees."""
    await employee_service.create_relation(data.from_employee_id, data.to_employee_id)


@router.delete("/relation")
async def delete_relation(
    data: DeleteRelationPayload, employee_service: EmployeeServiceDep
):
    """Idempotent request to remove a connection between assistants of two employees."""
    await employee_service.delete_relation(data.from_employee_id, data.to_employee_id)


@router.post("/{employee_id}/integration")
async def create_integration(
    employee_id: UUID,
    data: CreateIntegrationPayload,
    employee_service: EmployeeServiceDep,
):
    """Create an integration mapping from local employee entity to external one."""
    await employee_service.create_integration(
        employee_id, data.external_employee_id, data.external_employee_name
    )


@router.delete("/{employee_id}/integration")
async def delete_integration(employee_id: UUID, employee_service: EmployeeServiceDep):
    """Remove an integration mapping from local employee entity to external one."""
    await employee_service.delete_integration(employee_id)


@router.get("/integration/list")
async def list_integrations(
    employee_service: EmployeeServiceDep,
):
    integrations = await employee_service.list_integrations()
    return ListIntegrationsResponse(integrations=integrations)


@router.websocket("/{employee_id}/session")
async def session_handler(
    websocket: WebSocket, employee_id: UUID, session_service: SessionServiceDep
):
    await session_service.chat_session(employee_id, websocket)


@router.post("/{employee_id}/assistant/reset")
async def reset_assistant(employee_id: UUID, employee_service: EmployeeServiceDep):
    """Delete all checkpoints of an agent of the employee."""
    await employee_service.reset_assistant(employee_id)


@router.get("/{employee_id}/assistant/history")
async def message_history(employee_id: UUID, employee_service: EmployeeServiceDep):
    messages = await employee_service.message_history(employee_id)
    return MessageHistoryResponse(messages=messages)


@router.get("/{employee_id}/assistant/events")
async def event_chronology(employee_id: UUID, employee_service: EmployeeServiceDep):
    events = await employee_service.event_chronology(employee_id)
    return EventChronologyResponse(events=events)
