import logging
from uuid import <PERSON><PERSON>D

import sqlalchemy as sa
from fastapi import WebSocket, WebSocketException, status
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from information_flows.api.employee import dto
from information_flows.core.pool import AgentDiedMessage, AgentPool
from information_flows.core.types import AgentEvent, Message
from information_flows.database.employee import EmployeeModel

logger = logging.getLogger("session")
logger.setLevel(logging.INFO)


class SessionService:
    def __init__(self, engine: AsyncEngine, agent_pool: AgentPool):
        self.engine = engine
        self.agent_pool = agent_pool

    async def verify_user(self, employee_id: UUID) -> None:
        """Verify that an employee with the ID exists in the database."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                found = await session.scalar(
                    sa.select(True).where(EmployeeModel.employee_id == employee_id)
                )
        if not found:
            raise WebSocketException(
                code=status.WS_1008_POLICY_VIOLATION,
                reason=f"There is no employee with ID `{employee_id}` in the system",
            )

    def register_callbacks(self, employee_id: UUID, websocket: WebSocket):
        """Register callbacks to redirect messages and signals from the agent to a WebSocket client."""

        async def message_callback(message: Message | AgentDiedMessage):
            if not isinstance(message, AgentDiedMessage):
                chat_message = dto.ChatMessage(
                    content=message.content, 
                    role="agent",
                    chat_id=message.chat_id  # Include chat_id in response
                )
                await websocket.send_json(chat_message.model_dump(mode="json"))

        async def event_callback(event: AgentEvent):
            parent = event.parent.model_dump() if event.parent else None
            event = dto.AgentEvent(
                id=event.id,
                agent_id=event.agent_id,
                timestamp=event.timestamp,
                type=event.type,
                value=event.value,
                parent=parent,
            )
            system_message = dto.SystemMessage(payload=event)
            await websocket.send_json(system_message.model_dump(mode="json"))

        self.agent_pool.add_message_callback(employee_id, message_callback)
        self.agent_pool.add_event_callback(employee_id, event_callback)
        return message_callback, event_callback

    async def chat_session(self, employee_id: UUID, websocket: WebSocket) -> None:
        """Maintain a WebSocket session and exchange messages between a socket and the agent pool."""
        await websocket.accept()

        await self.verify_user(employee_id)

        message_cb, event_cb = self.register_callbacks(employee_id, websocket)
        logger.info(f"Open a new WS session for employee `{employee_id}`")
        try:
            async for data in websocket.iter_json():
                validated_msg = dto.SessionMessage.validate(data)
                if isinstance(validated_msg, dto.ChatMessage):
                    await self.agent_pool.put_message(
                        Message(
                            content=validated_msg.content,
                            receiver_id=employee_id,
                            sender_id=employee_id,
                            sender_role="user",
                            chat_id=validated_msg.chat_id,  # Pass chat_id from UI
                        )
                    )
                elif isinstance(validated_msg, dto.SystemMessage):
                    await self.agent_pool.send_signal(
                        agent_id=employee_id,
                        payload=validated_msg.payload,
                    )
        finally:
            self.agent_pool.remove_message_callback(employee_id, message_cb)
            self.agent_pool.remove_event_callback(employee_id, event_cb)
            logger.info(f"Close a WS session for employee `{employee_id}`")
