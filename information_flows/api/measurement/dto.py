from datetime import datetime
from typing import Annotated, Any, Optional
from uuid import UUID

from pydantic import BaseModel, StringConstraints, field_serializer


class StartSessionPayload(BaseModel):
    tag: Annotated[str, StringConstraints(min_length=5)]


class MeasurementSession(BaseModel):
    session_id: UUID
    tag: str
    started_at: datetime
    updated_at: datetime
    snapshot: Any

    @field_serializer("started_at", "updated_at", when_used="json")
    def serialize_timestamp(self, timestamp: datetime) -> float:
        return timestamp.timestamp() * 1000


class ActiveSessionResponse(BaseModel):
    active_session: Optional[MeasurementSession]


class ListSessionsResponse(BaseModel):
    sessions: list[MeasurementSession]
