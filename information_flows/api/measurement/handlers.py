from uuid import UUID

from fastapi import APIRouter

from information_flows.api.measurement.dto import (
    ActiveSessionResponse,
    ListSessionsResponse,
    StartSessionPayload,
)
from information_flows.api.setup import MeasurementServiceDep

router = APIRouter(prefix="/measurement")


@router.post("/start")
async def start_session(
    data: StartSessionPayload, measurement_service: MeasurementServiceDep
):
    await measurement_service.start_session(data.tag)


@router.post("/stop")
async def stop_session(measurement_service: MeasurementServiceDep):
    await measurement_service.stop_session()


@router.get("/active")
async def active_session(measurement_service: MeasurementServiceDep):
    active_session = await measurement_service.active_session()
    return ActiveSessionResponse(active_session=active_session)


@router.get("/list")
async def list_sessions(measurement_service: MeasurementServiceDep):
    sessions = await measurement_service.list_sessions()
    return ListSessionsResponse(sessions=sessions)


@router.delete("")
async def delete_sessions(measurement_service: MeasurementServiceDep):
    await measurement_service.delete_all_sessions()


@router.delete("/{session_id}")
async def delete_session(session_id: UUID, measurement_service: MeasurementServiceDep):
    await measurement_service.delete_session(session_id)
