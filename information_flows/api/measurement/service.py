import json
from typing import <PERSON><PERSON>
from uuid import UUID

import sqlalchemy as sa
from fastapi.exceptions import HTTPException
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession
from starlette import status

from information_flows.agents.core_agent.measurement import MeasurementResult
from information_flows.api.measurement import dto
from information_flows.core.measurement import measurement_session
from information_flows.database.measurement import MeasurementModel


class MeasurementService:
    def __init__(
        self,
        engine: AsyncEngine,
    ):
        self.engine = engine

    async def start_session(self, tag: str) -> None:
        """Start a new global measurement session."""
        if measurement_session.active_session:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"A global measurement session has already started",
            )
        await measurement_session.start(tag=tag, result_schema=MeasurementResult)

    async def stop_session(self) -> None:
        """Start the current active global measurement session."""
        if not measurement_session.active_session:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"A global measurement session has not started yet",
            )
        measurement_session.stop()

    async def active_session(self) -> Optional[dto.MeasurementSession]:
        """Return the current active global measurement session."""
        if not measurement_session.active_session:
            return None
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                session_id = measurement_session.active_session.session_id
                measurement = await session.scalar(
                    sa.select(MeasurementModel).where(
                        MeasurementModel.measurement_id == session_id
                    )
                )
                return dto.MeasurementSession(
                    session_id=measurement.measurement_id,
                    tag=measurement.tag,
                    started_at=measurement.started_at,
                    updated_at=measurement.updated_at,
                    snapshot=json.loads(measurement.snapshot),
                )

    async def list_sessions(self) -> list[dto.MeasurementSession]:
        """Return a history of non-active measurement sessions."""
        active_session_id = None
        if measurement_session.active_session:
            active_session_id = measurement_session.active_session.session_id
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                measurements = await session.scalars(
                    sa.select(MeasurementModel)
                    .where(MeasurementModel.measurement_id != active_session_id)
                    .order_by(sa.asc(MeasurementModel.started_at))
                )
                return [
                    dto.MeasurementSession(
                        session_id=measurement.measurement_id,
                        tag=measurement.tag,
                        started_at=measurement.started_at,
                        updated_at=measurement.updated_at,
                        snapshot=json.loads(measurement.snapshot),
                    )
                    for measurement in measurements
                ]

    async def delete_session(self, session_id: UUID) -> None:
        active_session_id = None
        if measurement_session.active_session:
            active_session_id = measurement_session.active_session.session_id
        if active_session_id == session_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete active session",
            )
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                await session.execute(
                    sa.delete(MeasurementModel).where(
                        MeasurementModel.measurement_id == session_id
                    )
                )

    async def delete_all_sessions(self) -> None:
        active_session_id = None
        if measurement_session.active_session:
            active_session_id = measurement_session.active_session.session_id
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                await session.execute(
                    sa.delete(MeasurementModel).where(
                        MeasurementModel.measurement_id != active_session_id
                    )
                )
