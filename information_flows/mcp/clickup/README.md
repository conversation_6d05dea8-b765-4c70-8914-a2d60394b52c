# ClickUp Agent

The ClickUp Agent can receive requests from employees and interact with the ClickUp API using various tools. It can create vacation requests, check task statuses, and more.

## Deploy

1. Build a Docker image:

```shell
docker build -t clickup .
```

2. Run a Docker container (the coding agent MCP on `8000` and the Python runtime MCP on `8001`):

```shell
docker run -d --env-file .env --name clickup -p 49440:8000 clickup
```
