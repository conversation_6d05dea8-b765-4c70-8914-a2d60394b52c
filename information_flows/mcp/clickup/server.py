import logging
from datetime import datetime

import pytz
from clickup_api import C<PERSON><PERSON>pAP<PERSON>
from fastapi import FastAPI
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from settings import settings

logging.basicConfig(level=logging.INFO)

app = FastAPI()
mcp = FastMCP("ClickUp")
app.mount("/", mcp.sse_app())

clickup_api = ClickUpAPI()


class Date(BaseModel):
    day: int = Field(description="Required format: 1-31")
    month: int = Field(description="Required format: 1-12")
    year: int = Field(description="Required format (4-digit): YYYY")


def date_to_unix_millis(date: Date, hour: int = 12) -> int:
    dt = datetime(year=date.year, month=date.month, day=date.day, hour=hour, minute=0)
    dt = pytz.timezone(settings.TIMEZONE).localize(dt)
    unix_ms = int(dt.timestamp() * 1000)
    logging.debug(f"[DATE] {date} => {dt} => {unix_ms}")
    return unix_ms


@mcp.tool()
async def list_vacations(user_input: str) -> str:
    """
    Retrieve all vacation tasks from ClickUp.

    Instructions for use:
    - Use this tool when you need to check planned or existing vacation tasks.
    - The `user_input` field is required but not used for filtering. You can pass any string (e.g., "show me vacations").

    Returns:
    - A newline-separated list of vacation task names with their corresponding IDs.
    - If an error occurs, an appropriate error message is returned.
    """
    logging.info("Received user_input: %s", user_input)
    try:
        data = await clickup_api.list_vacations()
        tasks = data.get("tasks", [])
        task_names = [
            f"{task.get('name', 'Unnamed Task')} (ID: {task.get('id', 'N/A')})"
            for task in tasks
        ]
        return "\n".join(task_names)
    except Exception as e:
        logging.exception("Error while fetching vacation tasks from ClickUp")
        return f"❌ Failed to list vacation tasks: {e}"


@mcp.tool()
async def approve_task(task_id: str, task_name: str) -> str:
    """
    ClickUp
    Use this tool if team lead want approve vacation
    Approve a vacation task by updating its status in ClickUp to 'approved'.

    Instructions for use:
    - Use this tool when a team lead wants to approve a vacation request.
    - `task_id`: the unique ClickUp ID of the task that needs approval.
    - `task_name`: a human-readable name for logging and display purposes.

    Returns:
    - A success message confirming the task has been approved.
    - Or an error message if the update failed.
    """
    logging.info(f"[TeamLead] Approving task '{task_name}' (ID: {task_id})")
    try:
        result = await clickup_api.update_task_status(
            task_id=task_id, status="approved"
        )
        if "error" in result:
            return f"❌ Failed to approve task '{task_name}': {result['error']}"
        return f"✅ Task '{task_name}' successfully approved."
    except Exception as e:
        logging.exception(f"Error while approving task '{task_name}'")
        return f"❌ Error while approving task: {e}"


@mcp.tool()
async def submit_vacation(
    name: str,
    start_date_unix: Date,
    due_date_unix: Date,
    requester_id: int,
    approver_id: int,
    content: str = "",
) -> str:
    """
    ClickUp
    Submits a vacation task to ClickUp with status 'planned' and assigns it to the approver.

    Parameters:
    - name: The task name in ClickUp (e.g., "Vacation for Kristina")
    - start_date_unix: Start date of vacation (structured Date)
    - due_date_unix: End date of vacation (structured Date)
    - requester_id: its clickup_id of the user requesting the vacation.
    - approver_id: its clickup_id of the user's team lead or manager who will approve the request.
    - content: Optional notes

    Notes:
    - This will create a ClickUp task with status 'planned'
    - The task is assigned to the approver
    """
    logging.info(f"[ClickUp] Submitting time off for: {name}")

    if not requester_id or not approver_id:
        logging.warning(
            f"Missing requester_id ({requester_id}) or approver_id ({approver_id})"
        )

    try:
        start_ms = date_to_unix_millis(start_date_unix, hour=12)
        due_ms = date_to_unix_millis(due_date_unix, hour=12)
    except Exception as e:
        logging.exception("Error converting date to unix millis")
        return f"❌ Invalid date format: {e}"

    try:
        logging.info(f"Using requester_id={requester_id}, approver_id={approver_id}")
        result = await clickup_api.create_vacation_task(
            name=name,
            start_date=start_ms,
            due_date=due_ms,
            requester_id=requester_id,
            approver_id=approver_id,
            description=content,
        )

        if "error" in result:
            return f"❌ Failed to submit time off: {result['error']}"

        task_id = result.get("id")
        logging.info(f"[ClickUp] Task created with ID: {task_id}")

        return f"✅ Vacation task successfully created with status 'planned'. Task ID: {task_id}"
    except Exception as e:
        logging.exception("Error while submitting time off task")
        return f"❌ Failed to submit time off: {e}"


@mcp.tool()
async def find_all_user_tasks(user_id: str) -> str:
    """
    Retrieve all tasks assigned to a user across all spaces in ClickUp.

    Instructions:
    - Use this tool to get a list of tasks a user is assigned to, regardless of which space the task belongs to.
    - This will iterate through all available spaces in the team and collect relevant tasks.

    Parameters:
    - user_id: The ClickUp user ID of the assignee.

    Returns:
    - A newline-separated list of task names with their corresponding IDs.
    - If no tasks are found, a message indicating so will be returned.
    - If an error occurs, an appropriate error message is returned.
    """
    logging.info(f"[ClickUp] Fetching tasks for user_id: {user_id}")
    try:
        result = await clickup_api.find_all_tasks_by_assignee(user_id)
        if "error" in result:
            return f"❌ {result['error']}"

        tasks = result.get("tasks", [])
        if not tasks:
            return "No tasks found for this user."

        return "\n".join([f"{task['name']} (ID: {task['id']})" for task in tasks])
    except Exception as e:
        logging.exception("Error fetching tasks from all spaces")
        return f"❌ Error: {e}"


if __name__ == "__main__":
    mcp.run(transport="stdio")
