import asyncio
import logging
from typing import Optional

import httpx

from settings import settings


class ClickUpAPI:
    BASE_URL = "https://api.clickup.com/api/v2"

    def __init__(self):
        self.token = settings.CLICKUP_API_TOKEN
        self.headers = {
            "Authorization": self.token,
            "Content-Type": "application/json",
        }
        self.semaphore = asyncio.Semaphore(3)

    async def _request(
        self,
        method: str,
        endpoint: str,
        *,
        json: Optional[dict] = None,
        params: Optional[dict] = None,
        retries: int = 5,
    ) -> dict:
        url = f"{self.BASE_URL}{endpoint}"

        for attempt in range(retries):
            async with self.semaphore:
                async with httpx.AsyncClient() as client:
                    try:
                        response = await client.request(
                            method=method,
                            url=url,
                            headers=self.headers,
                            json=json,
                            params=params,
                            timeout=15,
                        )
                        response.raise_for_status()
                        return response.json()
                    except httpx.HTTPStatusError as e:
                        if e.response.status_code == 429:
                            wait_time = 2**attempt
                            logging.warning(
                                f"Rate limit hit. Retrying in {wait_time}s... (Attempt {attempt+1})"
                            )
                            await asyncio.sleep(wait_time)
                            continue
                        logging.error(
                            f"[ClickUpAPI] HTTP {e.response.status_code}: {e.response.text}"
                        )
                        return {"error": e.response.text}
                    except httpx.RequestError as e:
                        logging.error(f"[ClickUpAPI] Request failed: {e}")
                        return {"error": str(e)}
        return {"error": "Rate limit reached. Retries exhausted."}

    async def create_vacation_task(
        self,
        name: str,
        start_date: int,
        due_date: int,
        requester_id: str,
        approver_id: int,
        description: str = "",
    ) -> dict:
        payload = {
            "name": name,
            "description": description,
            "start_date": str(start_date),
            "due_date": str(due_date),
            "status": "planned",
            "assignees": [approver_id],
            "group_assignees": [],
            "timezone": "Europe/Bucharest",
            "custom_fields": [
                {
                    "id": "1979e281-4565-44a5-9a8c-4ddfb95150a7",
                    "value": "79629113-053a-4ac9-8635-e0ebb5016c36",
                },
                {
                    "id": "fde2b51d-004c-4e61-a564-5cd6250d6584",
                    "value": {"add": [requester_id]},
                },
            ],
        }
        endpoint = f"/list/{settings.CLICKUP_LIST_ID}/task"
        return await self._request("POST", endpoint, json=payload)

    async def update_task_status(self, task_id: str, status: str) -> dict:
        payload = {"status": status}
        endpoint = f"/task/{task_id}"
        return await self._request("PUT", endpoint, json=payload)

    async def get_tasks_by_assignee(self, assignee_id: str) -> dict:
        endpoint = f"/team/{settings.CLICKUP_TEAM_ID}/task"
        params = {"assignees[]": assignee_id}
        return await self._request("GET", endpoint, params=params)

    async def find_all_tasks_by_assignee(self, assignee_id: str) -> dict:
        logging.info(f"Fetching all tasks assigned to user {assignee_id}")
        result = await self.get_tasks_by_assignee(assignee_id)
        if "error" in result:
            return {"error": result["error"]}

        return {"tasks": result.get("tasks", [])}
