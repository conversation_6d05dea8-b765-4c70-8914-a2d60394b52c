FROM python:3.12

WORKDIR /clickup

RUN apt-get update && apt-get install -y --no-install-recommends curl ca-certificates \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

ADD https://astral.sh/uv/install.sh /uv-installer.sh
RUN sh /uv-installer.sh && rm /uv-installer.sh

ENV PATH="/root/.local/bin/:$PATH"

COPY . /clickup

ENV PYTHONPATH="/clickup:/clickup/mcp:$PYTHONPATH"

RUN uv pip install -r /clickup/pyproject.toml --system

CMD ["bash", "start.sh"]

