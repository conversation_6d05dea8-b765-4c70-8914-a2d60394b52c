import asyncio

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent


async def main():
    async with MultiServerMCPClient(
        {
            "coding_agent": {
                "url": "http://localhost:49950/sse",
                "transport": "sse",
            },
        }
    ) as client:
        agent = create_react_agent("openai:gpt-4o-mini", client.get_tools())
        response = await agent.ainvoke(
            {
                "messages": [
                    {
                        "role": "user",
                        "content": "How many days between 2000-01-01 and 2025-03-18?",
                    }
                ]
            }
        )
        print(response["messages"][-1].content)


if __name__ == "__main__":
    asyncio.run(main())
