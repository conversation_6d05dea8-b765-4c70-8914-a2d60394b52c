import logging

from fastapi import Fast<PERSON><PERSON>
from mcp.server.fastmcp import FastMC<PERSON>
from pydantic_ai import Agent
from pydantic_ai.mcp import MCPServerHTTP
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from settings import settings
from utils import load_system_prompt

logging.basicConfig(level=logging.INFO)

app = FastAPI()

mcp = FastMCP("Policy")
app.mount("/", mcp.sse_app())

executor_mcp_server = MCPServerHTTP(
    url=f"http://localhost:{settings.PYTHON_EXECUTOR_PORT}/sse"
)

provider = OpenAIProvider(api_key=settings.OPENAI_API_KEY)
model = OpenAIModel(settings.model, provider=provider)
agent = Agent(
    model,
    system_prompt=load_system_prompt(),
    mcp_servers=[executor_mcp_server],
)


@mcp.tool()
async def ask_coding_agent(problem: str) -> str:
    """Ask a coding agent to help you solve a `problem`. The coding agent is best at
    performing precise and accurate calculations with numbers and dates, and solving
    complex tasks by writing and executing Python code in a secure environment.
    Describe your problem in ordinary natural language and intelligible manner.

    For example:
    **Problem**: What day of the week is the July 18 of the current year?
    **Expected agent behavior**: The agent uses Python `datetime` module to find
    the current date and year, then resolves the `July 18` to absolute timestamp
    and determines the required day of the week."""
    logging.info(f"Running an agent on a problem: {problem}")
    async with agent.run_mcp_servers():
        result = await agent.run(problem)
    logging.info(f"Result: {result.output}")
    return result.output
