# Coding Agent

A coding agent exposed as MCP server that can accept requests in natural language from other agents and execute Python code to solve tasks.

## Deploy

All commands below must be executed from the inside of this folder.

1. Create `.env` file by example and set all necessary environment variables.

2. Build a Docker image:

```shell
docker build -t coding_agent .
```

3. Run a Docker container (the coding agent MCP on `8000` and the Python runtime MCP on `8001`):

```shell
docker run -d --env-file .env --name coding_agent -p 49950:8000 -p 49960:8001 coding_agent
```
