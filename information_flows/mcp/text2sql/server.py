import logging

from mcp.server.fastmcp import FastMCP

import information_flows.mcp.text2sql.pipeline as pipeline

logging.basicConfig(level=logging.INFO)

mcp = FastMCP("Database")


@mcp.tool()
async def ask_company_database(query: str) -> str:
    """Ask a question about data in the company's database. The database can
    accept queries in natural language. When you are interested in some details
    about a particular employee or project, pose a question and ask the database.
    To know what kind of information is available here, just ask the database."""
    try:
        logging.info(f"Using `ask_company_database` tool from `Database` MCP server")

        t2s_pipeline = pipeline.Text2SQLPipeline()
        answers = await t2s_pipeline.run([query])

        return answers[0]
    except Exception:
        logging.error("Failed to ask the database", exc_info=True)
        return "The database failed to return a response."


if __name__ == "__main__":
    mcp.run(transport="stdio")
