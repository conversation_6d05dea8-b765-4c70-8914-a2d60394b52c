import argparse
import asyncio
import datetime as dt
import logging
import os

import dotenv

import information_flows.agents.core_agent.mcp.text2sql.decomposer as decomposer
import information_flows.agents.core_agent.mcp.text2sql.output_models as output_models
import information_flows.agents.core_agent.mcp.text2sql.pipeline as pipeline
import information_flows.agents.core_agent.mcp.text2sql.summarizer as summarizer
import information_flows.agents.core_agent.mcp.text2sql.text2sql as text2sql
import information_flows.agents.core_agent.mcp.text2sql.utils as utils
import information_flows.core.mlflow as mlflow

dotenv.load_dotenv()

logger = logging.getLogger("text2sql_main")


async def main():
    parser = argparse.ArgumentParser(description="Text2SQL Agent")
    parser.add_argument(
        "queries", nargs="+", type=str, help="User queries to convert into SQL"
    )
    parser.add_argument(
        "--log",
        default=True,
        action="store_true",
        help="Log agents, promtps and traces to M<PERSON>low",
    )
    parser.add_argument("--decompose", default=True, action="store_true")
    parser.add_argument("--summarize", default=True, action="store_true")
    parser.add_argument("--debug", default=False, action="store_true")
    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)

    t2s_pipeline = pipeline.Text2SQLPipeline()

    if args.log:
        experiment_name = "core_agent_test"
        tracker = mlflow.Tracking(experiment_name)
        current_datetime = dt.datetime.now().isoformat()
        code_paths = [
            os.path.abspath(utils.__file__),
            os.path.abspath(output_models.__file__),
            os.path.abspath(decomposer.__file__),
            os.path.abspath(text2sql.__file__),
            os.path.abspath(summarizer.__file__),
        ]
        model_name = t2s_pipeline.__class__.__name__
        tracker.log_agent(
            t2s_pipeline,
            model_name,
            run_name=f"{current_datetime}-{model_name}",
            tags={"model": os.getenv("MODEL_NAME")},
            log_system_metrics=True,
            code_paths=code_paths,
        )

    results = await t2s_pipeline.run(args.queries, args.decompose, args.summarize)
    if args.summarize:
        logger.info(
            "`--summarize` is set `True`, the pipeline will provide answers based on retrieved data."
        )
    else:
        logger.info(
            "`--summarize` is set `False`, the pipeline will provide SQL queries to retrieve data."
        )

    for input, result in zip(args.queries, results):
        print("=" * 10)
        print(f"User query: {input}")
        print(f"Model output: {result}")
        print("=" * 10)


if __name__ == "__main__":
    asyncio.run(main())
