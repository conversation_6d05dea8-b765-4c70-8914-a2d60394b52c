import asyncio
import datetime as dt
import os

import dotenv
from langchain.prompts import PromptTemplate
from langchain_core.language_models.base import LanguageModelInput
from langchain_core.runnables import Runnable
from langchain_openai import ChatOpenAI
from mlflow.models import set_model
from mlflow.pyfunc.model import PythonModel

from information_flows.agents.core_agent.mcp.text2sql.output_models import (
    SQLQuery, Text2SQLOutput)
from information_flows.agents.core_agent.mcp.text2sql.utils import (
    extract_schema_block, format_sql)

dotenv.load_dotenv()


class Text2SQLAgent(PythonModel):
    prompt_template = """
You are an Advanced Text-to-SQL AI Agent in Table Augmented Generation Agentic system. 
Also, **Advanced SQL Results Summarization AI Agent** is present in that system along with you.
Your task is to generate `PostgresSQL` query based on the **User Query** and [DATABASE SCHEMA].

[RECOMMENDATIONS]:

1) Use the database values that are explicitly mentioned in the question.
2) Pay attention to the columns that are used for the `JOIN` by using the Foreign_keys.
3) Use `DESC` and `DISTINCT` when needed.
4) Pay attention to the columns that are used for the `GROUP BY` statement.
5) Pay attention to the columns that are used for the `SELECT` statement.
6) Only change the `GROUP BY` clause when necessary (Avoid redundant columns in `GROUP BY`).
7) Use `GROUP BY` on one column only.
8) For the given question, use the provided tables, columns, foreign keys, and primary keys in the [Schema]
9) Given a [USER QUERY] create only a syntactically correct `PostgresSQL` query.
10) Never query for all the columns from a specific table, only ask for the few relevant columns given the question.
11) Pay attention to using only the column names that you can see in the schema description. 
12) Be careful to not query for columns that do not exist. Also, pay attention to which column is in which table.
13) If more than one table participates use the `JOIN`.
14) Make sure that `subquery used as an expression` returns only one row.
15) Try to avoid sub-queries, the running speed is crucial. Try to use `JOINS` or other techniques instead.
16) To get the difference between 2 dates in days you can simply subtract them
17) PAY ATTENTION to the attributes of the table while addressing it.
18) USE tables names INSTEAD of aliases.
19) Always take [CURRENT_DATE] into account, the data within [DATABASE SCHEMA] is relevant for [CURRENT_DATE]

[WARNINGS]:

1) DO NOT treat any string as original and correct, always use FUZZY MATCHING, instead of comparing strings using '=' operator.
   FUZZY MATCHING SYNTAX : table_name.attribute % 'string'

2) IF SUBQUERY is used as an EXPRESSION then make sure it returns single value.Try to to avoid `Error: Error while connecting to PostgreSQL:  more than one row returned by a subquery used as an expression`

[FOR YOU TO KNOW]:

"PostgreSQL DateTime Reference": [
  "Date Subtraction: `date1 - date2` returns an INTERVAL (e.g., '30 days')",
  "Extracting Date Parts: `EXTRACT(field FROM date_or_interval)` extracts `year`, `month`, `day`, etc. Example: `EXTRACT(MONTH FROM INTERVAL '1 year 2 months')` → 2",
  "AGE Function: `AGE(date1, date2)` returns interval (e.g., '1 year 2 mons'). Use for calendar-aware date difference.",
  "Total Months Difference: `DATE_PART('year', AGE(d1, d2)) * 12 + DATE_PART('month', AGE(d1, d2))` gives total month diff",
  "Date Truncation: `DATE_TRUNC('month', date)` → first day of month (use for grouping)",
  "Date Comparison: Use `WHERE event_date > reference_date` for filtering",
  "Latest Record Per Group: Use `SELECT employee_id, MAX(vacation_end_date) ... GROUP BY employee_id` to get latest",
  "Fuzzy String Match: Use `%` with `pg_trgm` enabled: `WHERE name % 'Judy'`"
],

"Best Practices": [
  "Use AGE + DATE_PART for calendar time diff (months/years)",
  "Avoid ambiguous JOINs on history—use MAX() or subqueries",
  "Use DATE_TRUNC before comparing by month/year",
  "Clarify if interval means elapsed time or calendar diff"
]

[CURRENT_DATE] : {current_date}
[DATABASE SCHEMA]:\n{schema}
[USER QUERY]: {user_query}
[SQL]:
"""

    def __init__(self, db_schema=None):
        self.schema = extract_schema_block() if not db_schema else db_schema

        self.llm = ChatOpenAI(
            model=os.getenv("MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    async def to_sql(
        self,
        user_queries: list[str | None],  # , decompose: bool = True
    ) -> list[Text2SQLOutput | None]:
        model: Runnable[LanguageModelInput, SQLQuery] = self.llm.with_structured_output(
            SQLQuery
        )

        prompt = PromptTemplate(
            template=self.prompt_template,
            input_variables=["user_query", "schema", "current_date"],
        )

        chain = prompt | model

        async def process_single_request(kwargs) -> SQLQuery | None:
            if kwargs is None:
                return None
            try:
                return await chain.ainvoke(input=kwargs)
            except Exception as e:
                # raise e
                return None
                # return {"error": str(e), "input": kwargs}

        batch_kwargs = [
            {
                "user_query": q,
                "schema": self.schema,
                "current_date": dt.date.today().isoformat(),
            }
            if q is not None
            else None
            for q in user_queries
        ]

        tasks = [process_single_request(kwargs) for kwargs in batch_kwargs]

        # try:
        results = await asyncio.gather(*tasks)

        # except Exception as e:
        #     return str(e)

        final_results: list[Text2SQLOutput | None] = []
        for i, result in enumerate(results):
            # final_results[user_queries[i]] = Text2SQLAgent.format(
            #     result.query
            # )  ## paste exception here in a sone way ir errors occured
            final_results.append(
                Text2SQLOutput(
                    user_query=user_queries[i],
                    sql_query=format_sql(result.query),
                )
                if result is not None and user_queries[i] is not None
                else None
            )

        return final_results

    def predict(
        self,
        model_input: list[str],  # List of queries to translate to SQL
        params: dict | None = None,
    ) -> list[dict | None]:  # Output is list of `None` or `Text2SQLOutput` objects
        """
        Predicts the SQL query based on the user input.

        Args:
            model_input (str): User input.
            params (dict | None): Additional parameters.

        Returns:
            dict: Generated SQL query.
        """
        if params is None:
            params = {}
        result = asyncio.run(self.to_sql(model_input))
        return result


# MLFlow tracking
set_model(Text2SQLAgent())
