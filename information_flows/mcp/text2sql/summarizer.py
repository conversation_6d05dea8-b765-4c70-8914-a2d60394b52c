import asyncio
import os

import dotenv
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from mlflow.models import set_model
from mlflow.pyfunc.model import PythonModel

from information_flows.agents.core_agent.mcp.text2sql.output_models import (
    SummarizationOutput, SummaryAnswer, Text2SQLOutput)
from information_flows.agents.core_agent.mcp.text2sql.utils import (
    df_to_text, run_multiple_queries)

dotenv.load_dotenv()


class SummarizationAgent(PythonModel):
    prompt_template = """
You are an Advanced SQL Results Summarization AI Agent in Table Augmented Generation Agentic system.
Also, **Advanced Text-to-SQL AI Agent** is present in that system along with you.
Your task is to summarize the **SQL Query Result** provided by **Advanced Text-to-SQL AI Agent** according to **User Query**.

NOTE:

- **Advanced Text-to-SQL AI Agent** decomposed **User Query** to **SQL Query**, snd after running that query we obtained **SQL Query Result**, which you have to summarize.

- Take into account that **SQL Query Result** is an output from **Advanced Text-to-SQL AI Agent**, so treat it as a final and perfect result, simply extract the information the users asks for in **User Query** 

- **User Query** can contain filters and data which is not present in **SQL Query Result** and that is OK, your task is to understand how to process **User Query** according the **SQL Query Result**.

**SQL Query** : 
{sql_query}

**SQL Query Result** : 
{sql_query_result}

**User Query** : {user_query}"""

    def __init__(self):
        model_name = os.getenv("MODEL_NAME")
        if model_name is None:
            raise ValueError("Specify `MODEL_NAME` in .env")
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            stream_usage=False,
        )

    async def _summarize_requests(
        self, sql_queries: list[str | None]
    ) -> list[str | None]:
        results = await run_multiple_queries(queries=sql_queries)

        # TODO: why .head(100) ?
        results = [
            df_to_text(df.head(100)) if df is not None else None for df in results
        ]

        return results

    async def get_answers(
        self, t2s_output: list[Text2SQLOutput | None]
    ) -> list[SummarizationOutput]:
        sql_queries = list(
            map(lambda x: x["sql_query"] if x is not None else None, t2s_output)
        )

        contexts = await self._summarize_requests(sql_queries)

        model = self.llm.with_structured_output(SummaryAnswer)

        prompt = PromptTemplate(
            template=self.prompt_template,
            input_variables=["user_query", "sql_query", "sql_query_result"],
        )

        chain = prompt | model

        async def process_single_request(kwargs) -> SummaryAnswer:
            try:
                return await chain.ainvoke(input=kwargs)
            except Exception as e:
                return SummaryAnswer(answer=str(e))

        batch_kwargs = [
            {
                "user_query": t2s_output[i]["user_query"]
                if t2s_output[i] is not None
                else None,
                "sql_query": sql_queries[i],
                "sql_query_result": context,
            }
            for i, context in enumerate(contexts)
        ]

        tasks = [process_single_request(kwargs) for kwargs in batch_kwargs]
        results = await asyncio.gather(*tasks)

        final_results = []

        for i in range(len(results)):
            final_results.append(
                SummarizationOutput(
                    user_query=t2s_output[i]["user_query"],
                    answer=results[i].answer,
                )
            )

        return final_results

    def predict(
        self,
        model_input: list[dict[str, str]],  # list of `Text2SQLOutput` objects
        params: dict | None = None,
    ) -> list[dict[str, str]]:  # list of `SummarizationOutput`
        return asyncio.run(self.get_answers(model_input))

    def track(self):
        set_model(self)


# MLFlow tracking
set_model(SummarizationAgent())
