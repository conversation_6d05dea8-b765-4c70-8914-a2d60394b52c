DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END $$;

CREATE EXTENSION IF NOT EXISTS pg_trgm;

--[SCHEMA]
-- Table: employees_table
CREATE TABLE employees_table (
    employee_id SERIAL PRIMARY KEY,
    employee_email_address TEXT,
    employee_name TEXT,
    vacation_days_collected INTEGER DEFAULT 0
);

-- Table: projects_table
CREATE TABLE projects_table (
    project_id SERIAL PRIMARY KEY,
    project_name TEXT,
    project_start_date DATE NOT NULL
);

-- Table: tasks_table
CREATE TABLE tasks_table (
    task_id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    employee_id INTEGER NOT NULL,
    task_description TEXT NOT NULL,
    task_creation_date DATE NOT NULL,
    task_deadline DATE NOT NULL,
    task_status TEXT NOT NULL
);

-- Table: vacations_history_table
CREATE TABLE vacations_history_table (
    vacation_id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL,
    vacation_start_date DATE NOT NULL,
    vacation_end_date DATE NOT NULL,
    vacation_type TEXT NOT NULL
);

-- Constraints for employees_table
ALTER TABLE employees_table
ADD CONSTRAINT chk_vacation_days_range
CHECK (vacation_days_collected >= 0 AND vacation_days_collected <= 25);

-- Constraints for tasks_table
ALTER TABLE tasks_table
ADD CONSTRAINT chk_task_dates CHECK (task_creation_date <= task_deadline);

ALTER TABLE tasks_table
ADD CONSTRAINT chk_task_status CHECK (task_status IN ('completed', 'in_progress'));

ALTER TABLE tasks_table
ADD CONSTRAINT fk_task_project FOREIGN KEY (project_id) REFERENCES projects_table(project_id),
ADD CONSTRAINT fk_task_assignee FOREIGN KEY (employee_id) REFERENCES employees_table(employee_id);

-- Constraints for vacations_history_table
ALTER TABLE vacations_history_table
ADD CONSTRAINT chk_vacation_dates CHECK (vacation_start_date <= vacation_end_date);

ALTER TABLE vacations_history_table
ADD CONSTRAINT chk_vacation_type CHECK (vacation_type IN ('payed', 'unpaid'));

ALTER TABLE vacations_history_table
ADD CONSTRAINT fk_vacation_employee FOREIGN KEY (employee_id) REFERENCES employees_table(employee_id);
--[SCHEMA]

-- Projects
INSERT INTO projects_table (project_name, project_start_date) VALUES
('AI Research Platform', '2023-04-01'),
('Mobile App Redesign', '2023-04-10'),
('E-commerce Analytics Tool', '2023-04-10');


-- Employees
INSERT INTO employees_table (employee_email_address, employee_name, vacation_days_collected) VALUES
('<EMAIL>', 'Alice Johnson', 20),
('<EMAIL>', 'Bob Smith', 15),
('<EMAIL>', 'Carol White', 12),
('<EMAIL>', 'Dave Brown', 25),
('<EMAIL>', 'Eve Black', 8),
('<EMAIL>', 'Frank Lee', 17),
('<EMAIL>', 'Grace Kim', 21),
('<EMAIL>', 'Heidi Cruz', 23),
('<EMAIL>', 'Ivan Gold', 19),
('<EMAIL>', 'Judy Green', 10);

-- Tasks (at least 3–5 tasks per employee, using valid dates)
-- Format: task_description, project_id, employee_id, start_date, deadline, task_status
INSERT INTO tasks_table (project_id, employee_id, task_description, task_creation_date, task_deadline, task_status) VALUES
-- Alice (Project 1)
(1,  1, 'Build prototype model', '2024-07-01', '2024-07-15', 'completed'),
(1,  1, 'Test data integration', '2024-07-16', '2024-07-30', 'completed'),
(1,  1, 'Prepare research documentation', '2024-08-01', '2024-08-10', 'in_progress'),

-- Bob (Project 1)
(1,  2, 'Conduct feature analysis', '2024-08-01', '2024-08-20', 'completed'),
(1,  2, 'Refactor training code', '2024-08-21', '2024-08-30', 'completed'),
(1,  2, 'Deploy model to cloud', '2024-09-01', '2024-09-15', 'in_progress'),

-- Carol (Project 2)
(2,  3, 'UI sketch proposal', '2024-07-10', '2024-07-20', 'completed'),
(2,  3, 'Wireframe adjustments', '2024-07-21', '2024-08-01', 'completed'),
(2,  3, 'Design final screens', '2024-08-05', '2024-08-25', 'in_progress'),

-- Dave (Project 2)
(2,  4, 'Color palette definition', '2024-04-10', '2024-04-30', 'completed'),
(2,  4, 'UX testing', '2024-07-01', '2024-07-20', 'completed'),
(2,  4, 'Mobile responsiveness fixes', '2024-07-25', '2024-08-15', 'in_progress'),

-- Eve (Project 2)
(2,  5, 'Android QA testing', '2024-07-01', '2024-07-10', 'completed'),
(2,  5, 'Bug reporting', '2024-07-11', '2024-07-20', 'completed'),
(2,  5, 'Regression test suite', '2024-07-25', '2024-08-05', 'in_progress'),

-- Frank (Project 3)
(3, 6, 'Backend dashboard setup', '2024-07-15', '2024-07-30', 'completed'),
(3, 6, 'API endpoint integration', '2024-08-01', '2024-08-15', 'completed'),
(3, 6, 'Testing and documentation', '2024-08-16', '2024-08-30', 'in_progress'),

-- Grace (Project 3)
(3, 7, 'Client analytics page', '2024-07-01', '2024-07-15', 'completed'),
(3, 7, 'Report generation scripts', '2024-07-16', '2024-07-30', 'completed'),
(3, 7, 'SQL optimization', '2024-08-01', '2024-08-20', 'in_progress'),

-- Heidi (Project 3)
(3, 8, 'Data pipeline setup', '2024-04-10', '2024-04-30', 'completed'),
(3, 8, 'ETL improvements', '2024-07-01', '2024-07-20', 'completed'),
(3, 8, 'Monitoring tools', '2024-07-25', '2024-08-10', 'in_progress'),

-- Ivan (Project 3)
(3, 9, 'KPI dashboard development', '2024-07-01', '2024-07-20', 'completed'),
(3, 9, 'User behavior logging', '2024-07-21', '2024-08-10', 'completed'),
(3, 9, 'Analytics funnel setup', '2024-08-15', '2024-09-01', 'in_progress'),

-- Judy (Project 1)
(1, 10, 'Automated retraining script', '2024-04-01', '2024-04-15', 'completed'),
(1, 10, 'Model registry integration', '2024-04-20', '2024-07-10', 'completed'),
(1, 10, 'Alerting for data drift', '2024-07-15', '2024-07-30', 'in_progress');

-- vacations (with at least 4 months between and paid within collected days)
-- vacations (with at least 4 months between and paid within collected days, start at least 30 days from today)
INSERT INTO vacations_history_table (employee_id, vacation_start_date, vacation_end_date, vacation_type) VALUES

-- Alice (20 days)
(1, '2024-04-10', '2024-04-19', 'payed'),

-- Bob (15 days)
(2,  '2024-04-20', '2024-04-26', 'payed'),

-- Carol (12 days)
(3,  '2024-04-15', '2024-04-22', 'unpaid'),

-- Dave (25 days)
(4,  '2024-04-10', '2024-04-24', 'payed'),

-- Eve (8 days)
(5,  '2024-04-10', '2024-04-15', 'payed'),

-- Frank (17 days)
(6,  '2024-04-20', '2024-04-30', 'payed'),

-- Grace (21 days)
(7, '2024-04-05', '2024-04-15', 'payed'),

-- Heidi (23 days)
(8, '2024-04-10', '2024-04-22', 'payed'),

-- Ivan (19 days)
(9, '2024-04-05', '2024-04-15', 'payed'),

-- Judy (10 days)
(10, '2024-04-10', '2024-04-17', 'payed');



WITH

-- Rule 1: Paid vacation exceeds collected days
rule_1 AS (
    SELECT 'rule_1_vacation_days' AS rule_name, COUNT(*) > 0 AS failed
    FROM vacations_history_table VH
    JOIN employees_table EM ON VH.employee_id = EM.employee_id
    WHERE VH.vacation_type = 'payed'
      AND (VH.vacation_end_date - VH.vacation_start_date + 1) > EM.vacation_days_collected
),

-- Rule 2: Invalid vacation type
rule_2 AS (
    SELECT 'rule_2_invalid_type' AS rule_name, COUNT(*) > 0 AS failed
    FROM vacations_history_table
    WHERE vacation_type NOT IN ('payed', 'unpaid')
),

-- Rule 3: < 4 months between vacations
rule_3 AS (
    SELECT 'rule_3_4_months_gap' AS rule_name, COUNT(*) > 0 AS failed
    FROM vacations_history_table VH
    JOIN vacations_history_table VH2 ON VH.employee_id = VH2.employee_id AND VH.vacation_id <> VH2.vacation_id
    WHERE VH2.vacation_end_date < VH.vacation_start_date
      AND VH.vacation_start_date < VH2.vacation_end_date + INTERVAL '4 months'
),


-- Rule 5: Vacation overlaps recent project
rule_5 AS (
   SELECT 'rule_5_recent_project_overlap' AS rule_name, COUNT(*) > 0 AS failed
FROM vacations_history_table VH
JOIN employees_table E on VH.employee_id = E.employee_id
JOIN tasks_table T ON T.employee_id = E.employee_id
JOIN projects_table PR ON PR.project_id = T.project_id
WHERE T.task_creation_date >= CURRENT_DATE - INTERVAL '4 weeks'
  AND VH.vacation_start_date BETWEEN T.task_creation_date AND T.task_deadline
),

-- Rule 6: Project start date is not after any task/vacation start date
rule_6 AS (
    SELECT 'rule_6_invalid_project_start_date' AS rule_name, COUNT(*) > 0 AS failed
    FROM projects_table PR
    LEFT JOIN tasks_table T ON PR.project_id = T.project_id
	LEFT JOIN employees_table E ON E.employee_id = T.employee_id
    LEFT JOIN vacations_history_table VH ON E.employee_id = VH.employee_id
    WHERE 
      (T.task_id IS NOT NULL AND T.task_creation_date < PR.project_start_date)
      OR
      (VH.vacation_id IS NOT NULL AND VH.vacation_start_date < PR.project_start_date)
),

-- Rule 7: All dates are at least one month in the past
rule_7 AS (
    SELECT 'rule_7_future_date_violation' AS rule_name, COUNT(*) > 0 AS failed
    FROM (
        SELECT project_start_date AS date_value FROM projects_table
        UNION ALL
        SELECT task_creation_date FROM tasks_table
        UNION ALL
        SELECT task_deadline FROM tasks_table
        UNION ALL
        SELECT vacation_start_date FROM vacations_history_table
        UNION ALL
        SELECT vacation_end_date FROM vacations_history_table
    ) AS all_dates
    WHERE date_value > CURRENT_DATE - INTERVAL '1 month'
)


-- Final UNION to show all rule failures
SELECT rule_name, NOT failed AS passed FROM rule_1 WHERE failed
UNION
SELECT rule_name, NOT failed FROM rule_2 WHERE failed
UNION
SELECT rule_name, NOT failed FROM rule_3 WHERE failed
UNION
SELECT rule_name, NOT failed FROM rule_5 WHERE failed
UNION 
SELECT rule_name, NOT failed FROM rule_6 WHERE failed


UNION ALL

-- Final result: overall check
SELECT 'ALL_RULES_PASSED' AS rule_name,
       NOT EXISTS (
           SELECT 1 FROM rule_1 WHERE failed
           UNION SELECT 1 FROM rule_2 WHERE failed
           UNION SELECT 1 FROM rule_3 WHERE failed
           UNION SELECT 1 FROM rule_5 WHERE failed
           UNION SELECT 1 FROM rule_6 WHERE failed
       ) AS passed;