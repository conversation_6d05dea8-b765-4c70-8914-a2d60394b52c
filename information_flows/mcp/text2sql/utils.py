import asyncio
import json
import os
from typing import TypedDict

import asyncpg
import dotenv
import pandas as pd
import sqlparse

dotenv.load_dotenv()


class QueryOutput(TypedDict):
    name: str
    df: pd.DataFrame


def extract_schema_block(sql_file_path: str = os.getenv("SQL_FILE_PATH")) -> str:
    """
    Extracts the block of SQL between --[SCHEMA] markers from the given file.

    Args:
        sql_file_path (str): Path to the .sql file.

    Returns:
        str: Extracted SQL schema block with all \n preserved.
    """
    in_schema = False
    schema_lines = []

    with open(sql_file_path, "r", encoding="utf-8") as file:
        for line in file:
            if "--[SCHEMA]" in line:
                if not in_schema:
                    in_schema = True
                else:
                    # Second --[SCHEMA] ends the block
                    break
            elif in_schema:
                schema_lines.append(line)

    return "".join(schema_lines)


def format_sql(sql: str):
    return sqlparse.format(sql, reindent=True, keyword_case="upper")


async def run_query(sql: str | None, pool: asyncpg.Pool) -> pd.DataFrame | None:
    if sql is None:
        return None
    async with pool.acquire() as conn:
        try:
            records = await conn.fetch(sql)
            df = (
                pd.DataFrame(records, columns=records[0].keys())
                if records
                else pd.DataFrame()
            )
            return df
        except Exception as e:
            print(f"[{sql}] failed: {e}")
            # return name, pd.DataFrame({"error": [str(e)]})  ### handle sql errors
            return None


async def run_multiple_queries(queries: list[str | None]) -> list[pd.DataFrame | None]:
    """
    Run multiple SQL queries concurrently and return results as pandas DataFrames.

    Args:
        queries: A dict mapping query names to SQL strings.

    Returns:
        A dict mapping query names to pandas DataFrames.
    """
    async with asyncpg.create_pool(
        os.getenv("DEMO_DATABASE_URL"),
        # user="postgres",
        # host="127.0.0.1",
        # port="47472",
        # database=os.getenv("DB_NAME"),
        min_size=1,
        max_size=len(queries),
    ) as pool:
        tasks = [run_query(sql, pool) for sql in queries]
        results = await asyncio.gather(*tasks)
    return results
    # return {name: df for name, df in results}


def convert_df_json(df):
    return "\n".join([json.dumps(r) for r in df.to_dict(orient="records")])


def convert_df_csv(df):
    return df.to_csv(index=True, header=True)


def df_to_text(df):
    return f"\n\nSQL Query Result contains {df.shape[0]} records : \n\n{convert_df_csv(df)}"
