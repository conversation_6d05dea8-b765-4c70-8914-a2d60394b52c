from typing import TypedDict

from pydantic import BaseModel, Field


class DecomposerOutput(TypedDict):
    user_query: str
    strategy: str


class DecomposedQuery(BaseModel):
    paraphrased_user_query: str = Field(
        default_factory="",
        description="Paraphrased and better structured user query.",
    )

    user_query_analysis: str = Field(
        default_factory="",
        description="The detailed analysis of the user query according to the database schema.",
    )

    tables_to_use: list[str] = Field(
        default_factory=[],
        description="List of tables which you would need tp write a query",
    )

    subqueries_avoiding_strategy: str = Field(
        default_factory="",
        description="The strategy on how to avoid subqueries in sql request to handle that request.",
    )

    informative_output_format: str = Field(
        default_factory="",
        description="The instructions how to make the output informative and understandable.",
    )


class Text2SQLOutput(TypedDict):
    user_query: str
    sql_query: str


class SQLQuery(BaseModel):
    """
    SQLQuery model to represent a SQL query.
    """

    query: str = Field(description="The SQL query string.")

    # explanation: str = Field(description="The explanation if the strategy chosen and sql itself.")


class SummaryAnswer(BaseModel):
    answer: str = Field(
        None,
        description="The answer to user question based on **SQL Query Result**  provided.",
    )


class SummarizationOutput(TypedDict):
    user_query: str
    answer: str
