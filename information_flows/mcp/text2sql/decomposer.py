import asyncio
import datetime as dt
import os

import dotenv
from langchain.prompts import PromptTemplate
from langchain_core.language_models.base import LanguageModelInput
from langchain_core.runnables import Runnable
from langchain_openai import ChatOpenAI
from mlflow.models import set_model
from mlflow.pyfunc.model import PythonModel

from information_flows.agents.core_agent.mcp.text2sql.output_models import (
    DecomposedQuery, DecomposerOutput)
from information_flows.agents.core_agent.mcp.text2sql.utils import \
    extract_schema_block

dotenv.load_dotenv()


class DecompositionAgent(PythonModel):
    prompt_template = """
You are an Advanced User Query Decomposer AI Agent in Table Augmented Generation Agentic system.
Your task is to decompose and analyse the [User Query] according to [Database Schema].

[NOTE]:

- paraphrased_user_query - is paraphrased version of a user query with proper formulation and better structured.

- user_query_analysis - is your strategy and reasoning on how would you generate an sql to handle [User Query]. 
  It should not be too long but contain your strategy on how would you convert [User Query] to sql query.
  The goal is to do everything in one run and avoid subqueries as much as possible.

- subqueries_avoiding_strategy - it is your thoughts on how to avoid using subqueries if you would have to generate sql query.
  If it is impossible to avoid subqueries than explain where and you to use it.

- informative_output_format - is your instructions how to make the output informative and understandable for other llm to summarize.
  The goal is to determine which data to return in the main SELECT statement to make it as informative as possible.

- Always take [CURRENT_DATE] into account, the data within [DATABASE SCHEMA] is relevant for [CURRENT_DATE]

[WARNINGS]:

[CURRENT_DATE] : {current_date}
[USER QUERY]: {user_query}
[DATABASE SCHEMA]:\n{schema}"""

    def __init__(self, schema: str):
        self.schema = schema

        self.llm = ChatOpenAI(
            model=os.getenv("MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    @staticmethod
    def get_strategy(
        analysis,
        tables,
        subqueries_avoiding_strategy,
        informative_output_format,
        paraphrased_user_query,
    ):
        return f"""

The user asked some question and it was redundant and too complex, here is a break down on how to generate sql for his request :

**Paraphrased Query** : {paraphrased_user_query}

**Analysis** : {analysis}

**You have to use these tables in the final SQL query** : {tables}

**Here are some instructions on subqueries usage in the final SQL query**: {subqueries_avoiding_strategy}

**Output Instructions** : {informative_output_format}
"""

    async def decompose(self, user_queries: list[str]) -> list[DecomposerOutput | None]:
        model: Runnable[LanguageModelInput, DecomposedQuery] = (
            self.llm.with_structured_output(DecomposedQuery)
        )

        prompt = PromptTemplate(
            template=self.prompt_template,
            input_variables=["user_query", "schema", "current_date"],
        )

        chain = prompt | model

        async def process_single_request(kwargs) -> DecomposedQuery | None:
            try:
                return await chain.ainvoke(input=kwargs)
            except Exception as e:
                # raise e
                return None
                # return {"error": str(e), "input": kwargs}

        batch_kwargs = [
            {
                "user_query": q,
                "schema": self.schema,
                "current_date": dt.date.today().isoformat(),
            }
            for q in user_queries
        ]

        tasks = [process_single_request(kwargs) for kwargs in batch_kwargs]
        results = await asyncio.gather(*tasks)

        final_results: list[DecomposerOutput | None] = []
        for query, result in zip(user_queries, results):
            final_results.append(
                DecomposerOutput(
                    user_query=query,
                    strategy=DecompositionAgent.get_strategy(
                        analysis=result.user_query_analysis,
                        tables=result.tables_to_use,
                        subqueries_avoiding_strategy=result.subqueries_avoiding_strategy,
                        informative_output_format=result.informative_output_format,
                        paraphrased_user_query=result.paraphrased_user_query,
                    ),
                )
                if result is not None
                else None
            )
        return final_results

    def predict(
        self,
        model_input: list[str],  # list of user queries to decompose
        params: dict | None = None,  # additional params
    ) -> list[dict | None]:  # list of `None` or `DecomposerOutput` objects
        return asyncio.run(self.decompose(model_input))


# MLFlow tracking
set_model(DecompositionAgent(extract_schema_block()))
