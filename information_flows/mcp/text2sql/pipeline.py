import argparse
import asyncio
import datetime as dt
import logging
import os

import dotenv
from mlflow.pyfunc.model import PythonModel

import information_flows.agents.core_agent.mcp.text2sql.decomposer as decomposer
import information_flows.agents.core_agent.mcp.text2sql.output_models as output_models
import information_flows.agents.core_agent.mcp.text2sql.summarizer as summarizer
import information_flows.agents.core_agent.mcp.text2sql.text2sql as text2sql
import information_flows.agents.core_agent.mcp.text2sql.utils as utils
import information_flows.core.mlflow as mlflow

dotenv.load_dotenv()

logger = logging.getLogger("agent")


class Text2SQLPipeline(PythonModel):
    async def run(
        self,
        user_queries: list[str],
        decompose: bool = True,
        summarize: bool = True,
    ) -> list[str]:
        logger.info("Executing `database_qa_pipeline`...")
        t2s_input: list[str | None] = user_queries
        db_schema = utils.extract_schema_block()
        logger.debug(f"DB Schema: {db_schema}")
        logger.debug(f"User queries: {user_queries}")

        if decompose:
            decomposer_agent = decomposer.DecompositionAgent(db_schema)
            decomposed_queries = await decomposer_agent.decompose(user_queries)
            t2s_input = list(
                map(
                    lambda x: x["strategy"] if x is not None else None,
                    decomposed_queries,
                )
            )
            logger.debug(f"Decomposed queries: {decomposed_queries}")

        sql_generator = text2sql.Text2SQLAgent(db_schema)
        sql_queries = await sql_generator.to_sql(t2s_input)
        logger.debug(f"Generated SQL queries: {sql_queries}")

        if summarize:
            summarizer_agent = summarizer.SummarizationAgent()
            summaries = await summarizer_agent.get_answers(sql_queries)
            logger.debug(f"Generated summaries: {summaries}")
            return list(map(lambda x: x["answer"], summaries))

        return list(map(lambda x: x["sql_query"] if x is not None else "", sql_queries))

    def predict(self, model_input: list[str], params: dict | None) -> list[str]:
        if params is None:
            params = {}
        return asyncio.run(
            self.run(
                model_input,
                params.get("decompose", True),
                params.get("summarize", True),
            )
        )
