import asyncio
import datetime as dt
import logging
import os

import mlflow
import mlflow.entities
import pandas as pd
import psycopg2

import information_flows.agents.core_agent.mcp.text2sql.decomposer as decomposer
import information_flows.agents.core_agent.mcp.text2sql.output_models as output_models
import information_flows.agents.core_agent.mcp.text2sql.pipeline as pipeline
import information_flows.agents.core_agent.mcp.text2sql.summarizer as summarizer
import information_flows.agents.core_agent.mcp.text2sql.text2sql as text2sql
import information_flows.agents.core_agent.mcp.text2sql.utils as utils
import information_flows.core.mlflow as mlflow

logger = logging.Logger("t2s_eval")

DEV_SET = "information_flows/agents/core_agent/mcp/text2sql/eval/dev-v1.json"


def run_query(query: str):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(
            user="postgres",
            host="127.0.0.1",
            port="47472",
            dbname="football",
            password="SbnYAYTMCQyUHc9cAeykfho5weRPepft7qKDR5Mmefm2y2Xy",
            options="-c search_path=public",
        )

        cursor = connection.cursor()
        cursor.execute(query)

        if cursor.rowcount == 0 and cursor.description:
            logger.warning("SQL query returned 0 rows")
            return None, [desc[0] for desc in cursor.description]

        elif cursor.rowcount > 0 and cursor.description:
            return (cursor.fetchall(), [desc[0] for desc in cursor.description])

    except (Exception, psycopg2.Error) as error:
        logger.error(f"Error when executing query: {error}")
        logger.error(f"Query: ```{query}```")
        pass
    finally:
        if connection is not None:
            if cursor is not None:
                cursor.close()
            connection.close()
    return None, None


# Init data
logger.info("Loading benchamrking data...")
observed = pd.read_json(DEV_SET)
observed = observed[~observed["id"].isin([961, 377])]


# Init model
logger.info("Initializing Text2SQL pipeline...")
t2s_pipeline = pipeline.Text2SQLPipeline()
decompose = True
summarize = False

# Log model
logger.info("Logging pipeline...")
experiment_name = "test_eval_pipeline"
tracker = mlflow.Tracking(experiment_name)
current_datetime = dt.datetime.now().isoformat()

code_paths = [
    os.path.abspath(utils.__file__),
    os.path.abspath(output_models.__file__),
    os.path.abspath(decomposer.__file__),
    os.path.abspath(text2sql.__file__),
    os.path.abspath(summarizer.__file__),
]
model_name = t2s_pipeline.__class__.__name__

logged_agent_output = tracker.log_agent(
    t2s_pipeline,
    model_name,
    run_name=f"{current_datetime}-{model_name}",
    tags={
        "model": os.getenv("MODEL_NAME"),
    },
    log_system_metrics=True,
    code_paths=code_paths,
)
model_info = logged_agent_output["model_info"]


predicted = asyncio.run(
    t2s_pipeline.run(observed.question.to_list(), decompose, summarize)
)

logger.info("Evaluating pipeline...")
eval_result = []
for i, (observed_idx, r) in enumerate(observed.iterrows()):
    prediction = predicted[i]
    result = {
        "metric": 0,
        "level": r.hardness,
        "row_id": observed_idx,
        "user_query": r.question,
        "observed_query": r.query,
        "predicted_query": "",
        "error": None,
    }
    if len(prediction) == 0:
        eval_result.append(result)
        continue

    result["predicted_query"] = prediction

    try:
        observed_q, desc_o = run_query(query=r.query)
        predicted_q, desc_p = run_query(result["predicted_query"])
        print(predicted_q)
        exit()

        if observed_q is None or predicted_q is None:
            eval_result.append(result)
            continue

        observed_r = pd.DataFrame(data=observed_q, columns=desc_o)
        predicted_r = pd.DataFrame(data=predicted_q, columns=desc_p)
        if observed_r.equals(predicted_r):
            result["metric"] = 1
    except Exception as e:
        result["error"] = str(e)
        eval_result.append(result)

model_type = "question-answering"
evaluators = "default"


def evaluate():
    _results = mlflow.evaluate(
        model_info.model_uri,
        observed,
        targets="query",
        predictions="sql_query",
        model_type=model_type,
        evaluators=evaluators,
    )
    logger.info("Storing evaluation results...")
    mlflow.log_metric(
        "result_match",
        sum(map(lambda x: x["metric"], eval_result)),
        model_id=model_info.model_id,
    )


run = logged_agent_output["run"]
if run.info.status != mlflow.entities.RunStatus.RUNNING:
    with mlflow.start_run(logged_agent_output["run"].info.run_id):
        evaluate()
else:
    evaluate()
