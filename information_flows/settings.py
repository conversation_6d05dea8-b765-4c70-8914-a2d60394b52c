from typing import Literal
from uuid import uuid4

from pydantic import ConfigDict, Field, computed_field
from pydantic_settings import BaseSettings

type Environment = Literal["development", "production"]

db_url_pattern = "{scheme}://{username}:{password}@{host}:{port}/{database}"


class Settings(BaseSettings):
    model_config = ConfigDict(extra="ignore")

    ENVIRONMENT: Environment = "development"

    TIMEZONE: str

    HOST: str
    PORT: str

    OPENAI_API_KEY: str
    ANTHROPIC_API_KEY: str | None = None

    LANGSMITH_API_KEY: str | None = None

    POSTGRES_HOST: str
    POSTGRES_PORT: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DATABASE: str

    CODING_AGENT_MCP_HOST: str
    CODING_AGENT_MCP_PORT: str

    CLICKUP_MCP_HOST: str
    CLICKUP_MCP_PORT: str

    # Enable or disable MCP servers
    ENABLE_MCP_SERVERS: bool = True

    TELEMETRY_CORRELATION_ID: str = Field(default_factory=lambda: str(uuid4()))

    OPENTELEMETRY_COLLECTOR_ENDPOINT: str | None = None

    MLFLOW_TRACKING_URI: str | None = None

    ALLOW_TEST_CLEANUP: bool = False

    @computed_field
    @property
    def database_url(self) -> str:
        return db_url_pattern.format(
            scheme="postgresql",
            username=self.POSTGRES_USER,
            password=self.POSTGRES_PASSWORD,
            host=self.POSTGRES_HOST,
            port=self.POSTGRES_PORT,
            database=self.POSTGRES_DATABASE,
        )

    @computed_field
    @property
    def async_database_url(self) -> str:
        return db_url_pattern.format(
            scheme="postgresql+asyncpg",
            username=self.POSTGRES_USER,
            password=self.POSTGRES_PASSWORD,
            host=self.POSTGRES_HOST,
            port=self.POSTGRES_PORT,
            database=self.POSTGRES_DATABASE,
        )


settings = Settings(_env_file=".env")
