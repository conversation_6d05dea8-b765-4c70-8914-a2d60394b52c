services:
  otel-collector:
    container_name: telemetry_information_flows_otel_collector
    image: otel/opentelemetry-collector:latest
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    command: [ "--config=/etc/otel-collector-config.yaml" ]
    ports:
      - 4318:4318 # otlp http
      - 4317:4317 # otlp grpc
    depends_on:
      - tempo
  tempo:
    container_name: telemetry_information_flows_tempo
    image: grafana/tempo:latest
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./tempo.yaml:/etc/tempo.yaml
      - tempo_data:/var/tempo
    ports:
      - "3200" # tempo
      - "4317" # otlp grpc
  grafana:
    container_name: telemetry_information_flows_grafana
    image: grafana/grafana-enterprise
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "48484:3000"
    depends_on:
      - tempo

volumes:
  tempo_data:
  grafana_data:
