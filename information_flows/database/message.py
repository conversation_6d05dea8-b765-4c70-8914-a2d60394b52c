from datetime import datetime
from uuid import UUID, uuid4

import sqlalchemy as sa

from information_flows.database.base import Base


class MessageModel(Base):
    __tablename__ = "message"

    message_id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    content: str = sa.Column(sa.Text, nullable=False)
    receiver_id: UUID = sa.Column(
        sa.Uuid,
        sa.Foreign<PERSON>ey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=False,
    )
    sender_id: UUID = sa.Column(
        sa.Uuid,
        sa.ForeignKey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=False,
    )
    sender_role: str = sa.Column(sa.String(length=256), nullable=False)
    timestamp: datetime = sa.Column(
        sa.DateTime, nullable=False, server_default=sa.func.now()
    )
