from datetime import datetime
from uuid import UUID, uuid4

import sqlalchemy as sa

from information_flows.database.base import Base


class MeasurementModel(Base):
    __tablename__ = "measurement"

    measurement_id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    tag: str = sa.Column(sa.String, nullable=False)
    started_at: datetime = sa.Column(sa.DateTime(timezone=True), nullable=False)
    updated_at: datetime = sa.Column(sa.DateTime(timezone=True), nullable=False)
    snapshot: str = sa.Column(sa.Text, nullable=False)
