from psycopg_pool import AsyncConnectionPool, ConnectionPool
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine

from information_flows.settings import settings

sync_engine = create_engine(
    settings.database_url,
    pool_size=10,
    max_overflow=10,
    pool_timeout=1 * 60 * 60,
    pool_pre_ping=True,
    pool_recycle=5 * 60,
    connect_args={
        "keepalives": 1,
        "keepalives_idle": 30,
        "keepalives_interval": 10,
        "keepalives_count": 5,
    },
)

async_engine = create_async_engine(
    settings.async_database_url,
    pool_size=10,
    max_overflow=10,
    pool_timeout=1 * 60 * 60,
    pool_pre_ping=True,
    pool_recycle=5 * 60,
)

# sync_connection_pool = ConnectionPool(
#     settings.database_url,
#     max_size=10,
#     kwargs={
#         "autocommit": True,
#         "prepare_threshold": 0,
#     },
#     open=False,
# )

async_connection_pool = AsyncConnectionPool(
    settings.database_url,
    max_size=10,
    kwargs={
        "autocommit": True,
        "prepare_threshold": 0,
    },
    open=False,
)
