from uuid import UUID

import sqlalchemy as sa

from information_flows.database.base import Base


class RelationModel(Base):
    """A way two employees are connected in, e.g. `teamlead` and `team member` or
    `teammate`. Defines a possibility of communication between any two agents."""

    __tablename__ = "relation"

    from_employee_id: UUID = sa.Column(
        sa.<PERSON>uid,
        sa.<PERSON>("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        primary_key=True,
    )
    to_employee_id: UUID = sa.Column(
        sa.Uuid,
        sa.Foreign<PERSON>ey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        primary_key=True,
    )