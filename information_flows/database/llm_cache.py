from datetime import datetime
from uuid import UUID, uuid4

import sqlalchemy as sa

from information_flows.database.base import Base


class LLMCache(Base):
    __tablename__ = "llm_cache"

    cache_id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    prompt: str = sa.Column(sa.String, nullable=False)
    prompt_hash: str = sa.Column(sa.String, nullable=False, index=True)
    llm: str = sa.Column(sa.String, nullable=False)
    index: int = sa.Column(sa.Integer, nullable=False)
    response: bytes = sa.Column(sa.LargeBinary, nullable=False)
    created_at: datetime = sa.Column(
        sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()
    )
