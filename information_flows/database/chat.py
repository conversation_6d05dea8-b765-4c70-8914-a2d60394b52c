from datetime import datetime
from uuid import UUID, uuid4

import sqlalchemy as sa

from information_flows.database.base import Base


# Many-to-many association table for Chat <-> Message
chat_message_association = sa.Table(
    "chat_message",
    Base.metadata,
    sa.Column("chat_id", sa.Uuid, sa.<PERSON>ey("chat.id"), primary_key=True),
    sa.Column("message_id", sa.Uuid, sa.Foreign<PERSON>ey("message.message_id"), primary_key=True),
    sa.Column("created_at", sa.DateTime, nullable=False, server_default=sa.func.now()),
)


class ChatModel(Base):
    __tablename__ = "chat"

    id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    employee_id: UUID = sa.Column(
        sa.Uuid,
        sa.ForeignKey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=False
    )
    created_at: datetime = sa.Column(
        sa.DateTime, nullable=False, server_default=sa.func.now()
    )
    updated_at: datetime = sa.Column(
        sa.DateTime, nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()
    )


class TaskModel(Base):
    __tablename__ = "task"
    
    id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    chat_id: UUID = sa.Column(
        sa.Uuid,
        sa.ForeignKey("chat.id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=False
    )
    task_identifier: str = sa.Column(sa.String(255), nullable=False)
    status: str = sa.Column(
        sa.Enum("active", "completed", "queued", "failed", name="task_status"),
        nullable=False,
        server_default="active"
    )
    content: str = sa.Column(sa.Text, nullable=False)  # JSON serialized task data
    created_at: datetime = sa.Column(
        sa.DateTime, nullable=False, server_default=sa.func.now()
    )
    completed_at: datetime | None = sa.Column(sa.DateTime, nullable=True)
