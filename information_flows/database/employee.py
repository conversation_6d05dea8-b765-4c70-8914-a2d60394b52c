from uuid import UUID, uuid4

import sqlalchemy as sa
from sqlalchemy.orm import Mapped, relationship

from information_flows.database.base import Base
from information_flows.database.relation import RelationModel


class EmployeeModel(Base):
    __tablename__ = "employee"

    employee_id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    thread_id: str = sa.Column(
        sa.String,
        sa.Computed("employee_id::varchar", persisted=True),
        nullable=False,
        unique=True,
    )
    static_preferences: str | None = sa.Column(sa.Text, nullable=True)

    relations: Mapped[list[RelationModel]] = relationship(
        foreign_keys=[RelationModel.from_employee_id]
    )
