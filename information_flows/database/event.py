from datetime import datetime
from uuid import UUID, uuid4

import sqlalchemy as sa

from information_flows.database.base import Base


class EventModel(Base):
    __tablename__ = "event"

    event_id: UUID = sa.Column(sa.Uuid, primary_key=True, default=uuid4)
    agent_id: UUID = sa.Column(
        sa.Uuid,
        sa.Foreign<PERSON>ey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        nullable=False,
    )
    timestamp: datetime = sa.Column(
        sa.DateTime, nullable=False, server_default=sa.func.now()
    )
    type: str = sa.Column(sa.String, nullable=False)
    value: str | None = sa.Column(sa.Text, nullable=True)
    parent: str | None = sa.Column(sa.Text, nullable=True)
