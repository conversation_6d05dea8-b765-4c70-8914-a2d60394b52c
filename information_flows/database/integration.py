from uuid import UUID

import sqlalchemy as sa

from information_flows.database.base import Base


class IntegrationModel(Base):
    __tablename__ = "integration"

    local_employee_id: UUID = sa.Column(
        sa.Uuid,
        sa.ForeignKey("employee.employee_id", ondelete="CASCADE", onupdate="CASCADE"),
        primary_key=True,
    )
    external_employee_id: str = sa.Column(sa.String, nullable=False, unique=True)
    external_employee_name: str = sa.Column(sa.String, nullable=False)
