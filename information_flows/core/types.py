from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Literal, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class Message(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    timestamp: datetime = Field(default_factory=datetime.now)
    content: Any
    sender_id: UUID
    sender_role: Literal["user", "agent"]
    receiver_id: UUID
    chat_id: Optional[UUID] = None  # Optional chat_id for grouping messages


@dataclass(order=True)
class MessageWithPriority:
    priority: int
    message: Message = field(compare=False)


AgentEventType = Literal[
    "agent_start",
    "agent_end",
    "action_start",
    "action_end",
    "progress",
    "agent_error",
]


class AgentEvent(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    agent_id: UUID
    timestamp: datetime = Field(default_factory=datetime.now)
    type: AgentEventType
    value: Optional[Any] = None
    parent: Optional["AgentEvent"] = None
