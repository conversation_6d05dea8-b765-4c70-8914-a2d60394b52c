import hashlib
import logging
import pickle
from typing import Optional

import sqlalchemy as sa
from langchain.globals import set_llm_cache
from langchain_core.caches import RETURN_VAL_TYPE, BaseCache
from sqlalchemy.engine.base import Engine
from sqlalchemy.orm import Session

from information_flows.database.llm_cache import LLMCache

logger = logging.getLogger("llm_cache")
logger.setLevel(logging.INFO)


class DatabaseLLMCache(BaseCache):
    """A generic SQL database LLM response cache."""

    def __init__(self, engine: Engine):
        self.engine = engine

    @staticmethod
    def compute_hash(value: str) -> str:
        return hashlib.sha256(value.encode("utf-8")).hexdigest()

    def lookup(self, prompt: str, llm_string: str) -> Optional[RETURN_VAL_TYPE]:
        """Lookup response for prompt and llm_string."""
        prompt_hash = self.compute_hash(prompt)

        with Session(self.engine) as session:
            rows = session.execute(
                sa.select(LLMCache.response)
                .where(LLMCache.prompt_hash == prompt_hash)
                .where(LLMCache.llm == llm_string)
                .order_by(LLMCache.index)
            ).all()

        if rows:
            try:
                generations = []
                for row in rows:
                    generations.append(pickle.loads(row[0]))
                return generations
            except Exception:
                # if cannot deserialize then cache a new response
                logging.error("Cannot deserialize model response", exc_info=True)
                return None

    def update(self, prompt: str, llm_string: str, return_val: RETURN_VAL_TYPE) -> None:
        """Cache response for prompt and llm_string."""
        prompt_hash = self.compute_hash(prompt)

        items = [
            LLMCache(
                prompt=prompt,
                prompt_hash=prompt_hash,
                llm=llm_string,
                response=pickle.dumps(generation),
                index=i,
            )
            for i, generation in enumerate(return_val)
        ]

        with Session(self.engine) as session, session.begin():
            for item in items:
                session.merge(item)

    def clear(self, **kwargs) -> None:
        """Clear all cached responses."""
        with Session(self.engine) as session:
            session.execute(sa.delete(LLMCache))

    @staticmethod
    def enable(engine: Engine) -> None:
        set_llm_cache(DatabaseLLMCache(engine))

    @staticmethod
    def disable() -> None:
        set_llm_cache(None)
