import asyncio
import logging
from collections import defaultdict
from typing import Any, Callable, Literal
from uuid import UUID

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg_pool import AsyncConnectionPool
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from information_flows.core.agent import BaseAgentFactory
from information_flows.core.executor import (
    KILL_AGENT_MESSAGE,
    AgentDiedMessage,
    AgentExecutor,
    EventCallback,
)
from information_flows.core.measurement import measurement_session
from information_flows.core.telemetry import Telemetry
from information_flows.core.types import Message
from information_flows.core.utils import execute_async
from information_flows.database.message import MessageModel

logger = logging.getLogger("pool")
logger.setLevel(logging.INFO)


class AgentPoolError(Exception):
    """Base exception class for agent pool errors."""


class AgentPool:
    message_queue: asyncio.Queue[Message]
    agent_registry: dict[UUI<PERSON>, AgentExecutor]
    agent_tasks: dict[UUID, asyncio.Task]
    message_callback_registry: dict[UUID, list[Callable[[Message], None]]]

    def __init__(
        self,
        connection_pool: AsyncConnectionPool,
        engine: AsyncEngine,
        *,
        verbose: bool = False,
        redelivery: bool = False,
    ):
        self.checkpointer = AsyncPostgresSaver(connection_pool)
        self.engine = engine
        self.verbose = verbose
        self.redelivery = redelivery
        self.message_queue = asyncio.Queue()
        self.agent_registry = {}
        self.agent_tasks = {}
        self.agent_lock = asyncio.Lock()
        self.message_callback_registry = defaultdict(list)
        self.event_callback_registry = defaultdict(list)
        self.router_kill_event = asyncio.Event()
        self.telemetry = Telemetry("agent_pool")

    async def spawn(self, id: UUID, agent_factory: BaseAgentFactory) -> None:
        """Run an agent graph for execution in an agent pool."""
        executor = AgentExecutor(
            agent_id=id,
            checkpointer=self.checkpointer,
            agent_factory=agent_factory,
            out_queue=self.message_queue,
            event_callbacks=self.event_callback_registry[id],
            engine=self.engine,
        )
        task = asyncio.create_task(executor.run())
        self.agent_registry[id] = executor
        self.agent_tasks[id] = task
        logger.info(f"Spawning an agent with ID: {id}")

    async def kill(self, id: UUID, *, wait: bool = False) -> None:
        """Kill a running agent executor and remove it from the pool."""
        async with self.agent_lock:
            await self.agent_registry[id].put_message(KILL_AGENT_MESSAGE)
            self.agent_registry.pop(id)
            task = self.agent_tasks.pop(id)
            logger.info(f"Killing an agent with ID: {id}")
        if wait:
            await task

    def has_running_agent(self, id: UUID) -> bool:
        """Check whether the agent with ID is running in the agent pool."""
        return (
            id in self.agent_registry
            and id in self.agent_tasks
            and not self.agent_tasks[id].done()
        )

    def get_running_agent(self, id: UUID) -> AgentExecutor:
        """Return an agent executor of an agent with ID."""
        if self.has_running_agent(id):
            return self.agent_registry[id]
        raise AgentPoolError(f"no running agent with ID: {id}")

    def has_subscribed_user(self, id: UUID) -> bool:
        """Check whether the user with ID has registered callbacks."""
        return (
            id in self.message_callback_registry and self.message_callback_registry[id]
        )

    def list_running_agents(self) -> list[UUID]:
        running = []
        for agent_id in self.agent_registry.keys():
            if self.has_running_agent(agent_id):
                running.append(agent_id)
        return running

    def add_message_callback(
        self, user_id: UUID, callback: Callable[[Message], None]
    ) -> None:
        self.message_callback_registry[user_id].append(callback)

    def remove_message_callback(
        self, user_id: UUID, callback: Callable[[Message], None]
    ) -> None:
        self.message_callback_registry[user_id].remove(callback)

    async def put_message(self, message: Message) -> None:
        """Put a message into an agent pool to be delivered to an agent."""
        await self.message_queue.put(message)

    def add_event_callback(self, agent_id: UUID, callback: EventCallback) -> None:
        self.event_callback_registry[agent_id].append(callback)

    def remove_event_callback(self, agent_id: UUID, callback: EventCallback) -> None:
        self.event_callback_registry[agent_id].remove(callback)

    async def send_signal(self, agent_id: UUID, payload: Any) -> None:
        """Send a signal to a specific agent with ID."""
        if not self.has_running_agent(agent_id):
            raise AgentPoolError(f"no running agent with ID: {agent_id}")
        await self.agent_registry[agent_id].signal_queue.put(payload)

    def start(self) -> None:
        """Start message router in the background."""
        asyncio.create_task(self.route())

    async def persist_message(self, message: Message) -> None:
        """Persist a message to the database."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                session.add(
                    MessageModel(
                        content=message.content,
                        receiver_id=message.receiver_id,
                        sender_id=message.sender_id,
                        sender_role=message.sender_role,
                        timestamp=message.timestamp,
                    )
                )
        # notify an active session about a new message
        measurement_session.record_message(message)

    async def route_to_agent(self, message: Message) -> None:
        if self.has_running_agent(message.receiver_id):
            async with self.agent_lock:
                await self.persist_message(message)
                await self.agent_registry[message.receiver_id].put_message(message)
        else:
            if self.redelivery:
                # reschedule delivery of the message
                await self.message_queue.put(message)
            logger.warning(f"There is no running agent with ID: {message.receiver_id}")

    async def route_to_user(self, message: Message) -> None:
        if self.has_subscribed_user(message.receiver_id):
            await self.persist_message(message)
            for callback in self.message_callback_registry[message.receiver_id]:
                asyncio.create_task(execute_async(callback, message))
        else:
            if self.redelivery:
                # reschedule delivery of the message
                await self.message_queue.put(message)
            logger.warning(f"No callbacks registered for ID: {message.receiver_id}")

    async def route(self) -> None:
        """Asynchronously route messages between agents and users."""
        while not self.router_kill_event.is_set():
            message = await self.message_queue.get()
            with self.telemetry.tracer.start_as_current_span("route_message") as span:
                if isinstance(message, AgentDiedMessage):
                    self.message_queue.task_done()
                    for callbacks in self.message_callback_registry.values():
                        for callback in callbacks:
                            await callback(message)
                    continue
                span.set_attribute("message", message.model_dump_json())
                if message.sender_id == message.receiver_id:
                    if message.sender_role == "user":
                        await self.route_to_agent(message)
                    else:
                        await self.route_to_user(message)
                else:
                    if message.sender_role == "agent":
                        await self.route_to_agent(message)
                    else:
                        logger.warning(
                            f"Sending a message from user `{message.sender_id}` to a wrong assistant `{message.receiver_id}`"
                        )
                if self.verbose:
                    logger.info(
                        f"Route message: [{message.sender_role}] {message.sender_id} -> {message.receiver_id}"
                    )
                self.message_queue.task_done()

    async def shutdown(self) -> dict[UUID, None | Exception]:
        """Kill all running agent tasks waiting until they are completed."""
        tasks: dict[UUID, asyncio.Task] = {}
        for id, task in list(self.agent_tasks.items()):
            logger.info(f"Killing a running agent with ID: {id}")
            await self.kill(id, wait=False)
            tasks[id] = task
        self.router_kill_event.set()
        # collect results or exceptions of agents
        results = await asyncio.gather(*tasks.values(), return_exceptions=True)
        return {id: result for id, result in zip(tasks.keys(), results)}
