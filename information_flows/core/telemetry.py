import argparse
import base64
import json

from langchain_core.messages import AIMessage
from opentelemetry import context, propagate, trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor
from pydantic import TypeAdapter

from information_flows.core.agent import AgentMCPConfig
from information_flows.settings import settings

INSTRUMENTING_MODULE_NAME = "opentelemetry"


def initialize_telemetry():
    """Configure global telemetry collection (call at application startup)."""
    resource = Resource.create(attributes={SERVICE_NAME: "information_flows_api"})
    tracer_provider = TracerProvider(resource=resource)
    if settings.OPENTELEMETRY_COLLECTOR_ENDPOINT:
        processor = SimpleSpanProcessor(
            OTLPSpanExporter(endpoint=settings.OPENTELEMETRY_COLLECTOR_ENDPOINT)
        )
        tracer_provider.add_span_processor(processor)
    trace.set_tracer_provider(tracer_provider)


class Telemetry:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.resource = Resource.create(
            attributes={
                SERVICE_NAME: service_name,
                "telemetry_correlation_id": settings.TELEMETRY_CORRELATION_ID,
            }
        )
        self.tracer = self.init_tracer()

    def init_tracer(self) -> trace.Tracer:
        tracer_provider = TracerProvider(resource=self.resource)
        if settings.OPENTELEMETRY_COLLECTOR_ENDPOINT:
            exporter = OTLPSpanExporter(
                endpoint=settings.OPENTELEMETRY_COLLECTOR_ENDPOINT
            )
            processor = SimpleSpanProcessor(exporter)
            tracer_provider.add_span_processor(processor)
        return tracer_provider.get_tracer(INSTRUMENTING_MODULE_NAME)


def telemetry_serialize_state[T](state: T, state_type: type[T]) -> str:
    adapter = TypeAdapter(state_type)
    return adapter.dump_json(state, warnings=False).decode("utf-8")


def telemetry_serialize_tools(ai_message: AIMessage) -> str:
    """Convert a list of tool calls into a span attribute string."""
    actions = []
    for tool_call in ai_message.tool_calls:
        actions.append({"name": tool_call["name"], "args": tool_call["args"]})
    return json.dumps(actions)


def inject_tracing_context(mcp_config: AgentMCPConfig) -> None:
    """Inject current tracing context into agent MCP configuration. Must be called inside span context."""
    context_carrier = {}
    propagate.inject(context_carrier)
    json_serialized = json.dumps(context_carrier).encode("utf-8")
    base64_encoded = base64.b64encode(json_serialized).decode("utf-8")
    for connection in mcp_config.values():
        if connection["transport"] == "stdio":
            tracing_args = ["--tracing_context", base64_encoded]
            if "args" not in connection:
                connection["args"] = []
            connection["args"].extend(tracing_args)
        elif connection["transport"] == "sse":
            tracing_headers = {"x-tracing-context": base64_encoded}
            if "headers" not in connection:
                connection["headers"] = {}
            connection["headers"].update(tracing_headers)


def extract_tracing_context_stdio() -> context.Context:
    """Extract current tracing context of `stdio` MCP from process arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--tracing_context")
    args = parser.parse_args()
    tracing_context = json.loads(base64.b64decode(args.tracing_context))
    return propagate.extract(tracing_context)
