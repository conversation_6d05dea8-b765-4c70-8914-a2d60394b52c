import asyncio
import os
import traceback
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Callable, Optional, get_args, get_origin
from uuid import UUID, uuid4

import aiofiles
import sqlalchemy as sa
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from information_flows.core.types import Message
from information_flows.core.utils import execute_async
from information_flows.database.integration import IntegrationModel
from information_flows.database.measurement import MeasurementModel
from information_flows.database.setup import async_engine
from information_flows.settings import settings

MEASUREMENT_DIR = "measurements"


# a format of filename of measurement files
measurement_filename_format = "{tag}-{timestamp}.json"


class MeasurementError(Exception):
    """Base exception class for measurement errors."""


class BaseMeasurement(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    timestamp: datetime = Field(default_factory=datetime.now)
    type: str

    def __hash__(self):
        return hash(self.id)

    def __eq__(self, other: "BaseMeasurement"):
        return self.id == other.id


class BaseAgentTrajectory(BaseModel):
    id: UUID
    agent_id: UUID
    started_at: datetime
    finished_at: datetime | None = None
    exception: str | None = None
    measurements: list[BaseMeasurement]

    def __hash__(self):
        return hash(self.id)

    def __eq__(self, other: "BaseAgentTrajectory"):
        return self.id == other.id


class MeasuredAgent(BaseModel):
    agent_id: UUID
    external_employee_name: str
    external_employee_id: str

    def __hash__(self):
        return hash(self.agent_id)

    def __eq__(self, other: "MeasuredAgent"):
        return self.agent_id == other.agent_id


class BaseMeasurementResult[T: BaseAgentTrajectory](BaseModel):
    started_at: datetime
    updated_at: datetime
    agents: list[MeasuredAgent]
    trajectories: list[T]
    messages: list[Message]

    @classmethod
    def load[R: "BaseMeasurementResult"](
        cls: type[R], *, tag: Optional[str] = None, path: str | Path = None
    ) -> R:
        """Load measurement result from a file `path` or the **latest** file with the `tag`."""
        if tag and path:
            raise MeasurementError("only one of `tag` and `path` can be provided")
        if tag:
            # find path of the latest file with `tag`
            filepaths = []
            for entry in os.scandir(MEASUREMENT_DIR):
                if entry.is_file() and entry.name.startswith(tag):
                    filepaths.append(entry.path)
            if not filepaths:
                raise FileNotFoundError(f"no file found with tag `{tag}`")
            path = sorted(filepaths)[-1]
        with open(path) as file:
            content = file.read()
        return cls.model_validate_json(content)

    async def save(self, filename: str | Path) -> None:
        """Persist a current state of all traced trajectories into a file."""
        async with aiofiles.open(filename, "w") as file:
            await file.write(self.model_dump_json(indent=4))

    def select_agent(self, agent_id: UUID) -> MeasuredAgent:
        for agent in self.agents:
            if agent.agent_id == agent_id:
                return agent
        raise ValueError(f"agent with `agent_id` did not participate in the session")

    def agent_of(self, external_employee_id: str) -> MeasuredAgent:
        """Return an agent of the given employee if it participated in the session."""
        for agent in self.agents:
            if agent.external_employee_id == external_employee_id:
                return agent
        raise ValueError(
            f"employee with `external_employee_id` did not participate in the session"
        )

    def agent_trajectories(self, agent: MeasuredAgent) -> list[T]:
        """Return a chronologically sorted list of agent trajectories."""
        trajectories = []
        for trajectory in self.trajectories:
            if trajectory.agent_id == agent.agent_id:
                trajectories.append(trajectory)
        return sorted(trajectories, key=lambda t: t.started_at)


type MeasurementUpdateCallback = Callable[[BaseMeasurement, BaseMeasurementResult], None]


class MeasurementSession[T: BaseMeasurementResult]:
    """A global system session collecting trajectories of all running agents."""

    tag: str
    """A tag or name of the meaurement. Should be unique Python identifier."""
    agents: dict[UUID, MeasuredAgent]
    result_schema: type[T]
    tracers: list["TrajectoryTracer"]

    def __init__(
        self,
        tag: str,
        result_schema: type[T],
        *,
        engine: AsyncEngine,
        persist_to_file: bool = False,
    ):
        self.session_id = None
        self.tag = tag
        self.result_schema = result_schema
        self.engine = engine
        self.persist_lock = asyncio.Lock()
        self.persist_to_file = persist_to_file
        self.callbacks = []

        # tracked variables
        self.started_at = datetime.now()
        self.updated_at = datetime.now()
        self.agents = {}
        self.messages = []
        self.tracers = []

        filename = measurement_filename_format.format(
            tag=tag, timestamp=self.started_at.strftime("%Y-%m-%dT%H-%M-%S")
        )
        self.filename = Path(MEASUREMENT_DIR) / filename

    def snapshot_result(self) -> T:
        """Return a measurement result of the session at the given moment."""
        return self.result_schema(
            started_at=self.started_at,
            updated_at=self.updated_at,
            agents=list(self.agents.values()),
            trajectories=[tracer.snapshot_trajectory() for tracer in self.tracers],
            messages=self.messages,
        )

    @property
    def trajectory_schema(self) -> type[T]:
        """Extract type of the agent traejctory from the result schema."""
        field_info = self.result_schema.model_fields["trajectories"]
        if get_origin(field_info.annotation) is not list:
            raise TypeError("`trajectories` must be a list of trajectories")
        contained_type = get_args(field_info.annotation)[0]
        return contained_type

    async def initialize(self) -> None:
        if self.session_id:
            raise MeasurementError("this measurement session has already started")
        result = self.snapshot_result()
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                # create a database record
                measurement_session = MeasurementModel(
                    tag=self.tag,
                    started_at=result.started_at,
                    updated_at=result.updated_at,
                    snapshot=result.model_dump_json(),
                )
                session.add(measurement_session)
                await session.flush()
                self.session_id = measurement_session.measurement_id

    def add_update_callback(self, callback: MeasurementUpdateCallback) -> None:
        self.callbacks.append(callback)

    def remove_update_callback(self, callback: MeasurementUpdateCallback) -> None:
        self.callbacks.remove(callback)

    async def update_database(self, result: BaseMeasurementResult) -> None:
        """Persist current state of the measurement session into the database."""
        if not self.session_id:
            raise MeasurementError("the session has not started yet")
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                # update a database record
                await session.execute(
                    sa.update(MeasurementModel)
                    .where(MeasurementModel.measurement_id == self.session_id)
                    .values(
                        updated_at=result.updated_at,
                        snapshot=result.model_dump_json(),
                    )
                )

    async def persist_changes(self) -> None:
        """Persist a current state of the measurement session into a file and the database."""
        async with self.persist_lock:
            result = self.snapshot_result()
            async with asyncio.TaskGroup() as tg:
                # persist to database
                tg.create_task(self.update_database(result))
                # persist to a file
                if self.persist_to_file:
                    tg.create_task(result.save(self.filename))

    async def add_agent(self, agent_id: UUID) -> None:
        """Link an new measured agent to the current session."""
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                integration = await session.scalar(
                    sa.select(IntegrationModel).where(
                        IntegrationModel.local_employee_id == agent_id
                    )
                )
                self.agents[agent_id] = MeasuredAgent(
                    agent_id=agent_id,
                    external_employee_name=integration.external_employee_name,
                    external_employee_id=integration.external_employee_id,
                )

    async def add_trajectory_tracer(self, tracer: "TrajectoryTracer") -> None:
        """Link the trajectory of an agent to the current session."""
        self.tracers.append(tracer)
        if tracer.agent_id not in self.agents:
            # this is the first trajectory of this agent
            await self.add_agent(tracer.agent_id)

    def update_callback(self) -> None:
        """This callback should be called on every update to traced data."""
        self.updated_at = datetime.now()
        asyncio.create_task(self.persist_changes())

    def notify_subscribers(
        self, measurement: BaseMeasurement, trajectory: BaseAgentTrajectory
    ) -> None:
        result = self.snapshot_result()
        for callback in self.callbacks:
            asyncio.create_task(execute_async(callback, measurement, result))

    def record_message(self, message: Message) -> None:
        """Add a new message to the measurement session."""
        self.messages.append(message)
        self.update_callback()


class NullTrajectoryTracer:
    """Tracer that does not write trajectories anywhere."""

    def record_measurement(self, _: BaseMeasurement) -> None:
        """Miss any measurement."""

    @asynccontextmanager
    async def begin(self):
        yield self


class TrajectoryTracer[T: BaseAgentTrajectory]:
    """Creates and traces a new trajectory of an agent."""

    session: MeasurementSession
    """A bound (linked) session within which to trace a trajectory."""
    agent_id: UUID
    """An agent traced by the trajectory tracer."""
    trajectory_id: Optional[UUID]
    """An ID of a trajectory instance traced by the trajectory tracer."""

    def __init__(
        self,
        session: MeasurementSession,
        agent_id: UUID,
    ):
        self.session = session
        self.agent_id = agent_id
        self.trajectory_id = None

        # tracked variables
        self.started_at = datetime.now()
        self.finished_at = None
        self.measurements = []
        self.exception = None

    def snapshot_trajectory(self) -> T:
        if not self.trajectory_id:
            raise MeasurementError(
                f"trajectory tracing has not begun for agent with ID: {self.agent_id}"
            )
        return self.session.trajectory_schema(
            id=self.trajectory_id,
            agent_id=self.agent_id,
            started_at=self.started_at,
            finished_at=self.finished_at,
            measurements=self.measurements,
        )

    def record_measurement(self, measurement: BaseMeasurement) -> None:
        """Add a new measurement to the traced trajectory."""
        if not self.trajectory_id:
            raise MeasurementError(
                f"trajectory tracing has not begun for agent with ID: {self.agent_id}"
            )
        self.measurements.append(measurement)
        self.session.update_callback()
        self.session.notify_subscribers(measurement, self.snapshot_trajectory())

    @asynccontextmanager
    async def begin(self):
        """A context manager to automatically complete a trajectory in case of exception or success."""
        if self.trajectory_id:
            raise MeasurementError(
                f"trajectory tracing has already begun for agent with ID: {self.agent_id}"
            )
        self.trajectory_id = uuid4()
        await self.session.add_trajectory_tracer(self)
        try:
            yield self
        except Exception as exc:
            self.exception = "".join(traceback.format_exception(exc))
            raise
        finally:
            self.finished_at = datetime.now()
            self.session.update_callback()


class MeasurementSessionManager:
    active_session: MeasurementSession
    """A current global measurement session for the entire system."""

    def __init__(self, persist_to_file: bool = False):
        self.active_session = None
        self.persist_to_file = persist_to_file
        if self.persist_to_file:
            os.makedirs(MEASUREMENT_DIR, exist_ok=True)

    def check_active(self) -> None:
        if not self.active_session:
            raise MeasurementError("no active measurement session")

    async def start(
        self,
        *,
        tag: str,
        result_schema: type[BaseMeasurementResult],
    ) -> None:
        """Start a new global trajectory tracing session (for the entire system)."""
        self.active_session = MeasurementSession(
            result_schema=result_schema,
            tag=tag,
            engine=async_engine,
            persist_to_file=self.persist_to_file,
        )
        await self.active_session.initialize()

    def stop(self) -> BaseMeasurementResult:
        """Unbind the active global trajectory tracing session."""
        self.check_active()
        result = self.active_session.snapshot_result()
        self.active_session = None
        return result

    def record_message(self, message: Message) -> None:
        """A callback to update the session on each new message."""
        if measurement_session.active_session:
            measurement_session.active_session.record_message(message)

    def trajectory_tracer(
        self,
        agent_id: UUID,
    ) -> TrajectoryTracer:
        """Create a new trajectory tracer for the agent."""
        if not self.active_session:
            return NullTrajectoryTracer()
        return TrajectoryTracer(
            session=self.active_session,
            agent_id=agent_id,
        )

    @asynccontextmanager
    async def session(self, tag: str, result_schema: type[BaseMeasurementResult]):
        await self.start(tag=tag, result_schema=result_schema)
        try:
            yield self.active_session
        finally:
            self.stop()


measurement_session = MeasurementSessionManager(
    persist_to_file=settings.ENVIRONMENT == "development"
)
