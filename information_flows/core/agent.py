from contextlib import asynccontextmanager
from contextvars import ContextV<PERSON>
from dataclasses import dataclass
from typing import Any, AsyncContextManager, Awaitable, Optional, Protocol
from uuid import UUID

from langchain_mcp_adapters.client import Connection
from langgraph.checkpoint.base import BaseCheckpointSaver
from opentelemetry.trace import Span, Tracer

from information_flows.core.measurement import BaseMeasurement, TrajectoryTracer
from information_flows.core.types import AgentEvent, AgentEventType, Message


class AgentError(Exception):
    """Base exception class for agent errors."""


class SendCallback(Protocol):
    def __call__(self, content: Any, receiver_id: UUID) -> Awaitable[Message]: ...


send_callback_var: ContextVar[SendCallback] = ContextVar("send_callback_var")


async def send_message(content: Any, receiver_id: UUID) -> Message:
    """Send a message from an agent and return the sent message. You
    can use this function anywhere in your agent implementation as it
    executes inside agent execution context."""
    try:
        callback = send_callback_var.get()
    except LookupError:
        raise AgentError("cannot 'send_message' outside agent execution context")
    return await callback(content, receiver_id)


@dataclass
class TelemetryContext:
    tracer: Tracer
    """An OpenTelemetry tracer collecting events of a specific agent."""
    agent_turn_span: Span
    """An OpenTelemetry span covering the entire turn of an agent."""


telemetry_context_var: ContextVar[TelemetryContext] = ContextVar(
    "telemetry_context_var"
)


def get_telemetry_context():
    """Return an agent telemetry context. Use this function anywhere in your
    agent implementation as it executes inside agent execution context."""
    try:
        context = telemetry_context_var.get()
    except LookupError:
        raise AgentError("cannot use telemetry context outside agent execution context")
    return context


class ReadSignalCallback(Protocol):
    def __call__(self, wait: bool) -> Awaitable[Optional[Any]]: ...


class SendEventCallback(Protocol):
    def __call__(
        self,
        event_type: AgentEventType,
        value: Optional[Any] = None,
        parent: Optional[AgentEvent] = None,
    ) -> Awaitable[AgentEvent]: ...


@dataclass
class AgentChannel:
    read_signal: ReadSignalCallback
    send_event: SendEventCallback


agent_channel_var: ContextVar[AgentChannel] = ContextVar("agent_channel_var")


async def read_signal(wait: bool = False) -> Optional[Any]:
    """Read the next signal to the agent. If `wait` is true then block and wait
    for the next signal. Use this function anywhere in your agent implementation
    as it executes inside agent execution context."""
    try:
        channel = agent_channel_var.get()
    except LookupError:
        raise AgentError("cannot 'read_signal' outside agent execution context")
    return await channel.read_signal(wait)


async def send_event(
    event_type: AgentEventType,
    value: Optional[Any] = None,
    parent: Optional[AgentEvent] = None,
) -> AgentEvent:
    """Send an event from the agent. Use this function anywhere in your
    agent implementation as it executes inside agent execution context."""
    try:
        channel = agent_channel_var.get()
    except LookupError:
        raise AgentError("cannot 'send_event' outside agent execution context")
    return await channel.send_event(event_type, value, parent)


trajectory_tracer_var: ContextVar[TrajectoryTracer] = ContextVar(
    "trajectory_tracer_var"
)


async def record_measurement(measurement: BaseMeasurement) -> None:
    """Record the measurement into a current trajectory of the agent. Use this
    function anywhere in your agent implementation as it executes inside agent
    execution context."""
    try:
        trajectory_tracer = trajectory_tracer_var.get()
    except LookupError:
        raise AgentError("cannot 'record_measurement' outside agent execution context")
    trajectory_tracer.record_measurement(measurement)


@asynccontextmanager
async def agent_execution_context(
    send_message_callback: SendCallback,
    agent_channel: AgentChannel,
    trajectory_tracer: TrajectoryTracer,
    telemetry_context: TelemetryContext,
):
    """Create an agent execution context with context variables and global callbacks."""
    send_message_callback_token = send_callback_var.set(send_message_callback)
    agent_channel_token = agent_channel_var.set(agent_channel)
    telemetry_token = telemetry_context_var.set(telemetry_context)
    trajectory_tracer_token = trajectory_tracer_var.set(trajectory_tracer)
    try:
        yield
    finally:
        send_callback_var.reset(send_message_callback_token)
        agent_channel_var.reset(agent_channel_token)
        telemetry_context_var.reset(telemetry_token)
        trajectory_tracer_var.reset(trajectory_tracer_token)


type AgentMCPConfig = dict[str, Connection]


class BaseAgent(Protocol):
    async def run(message: Message) -> None:
        """Asynchronously process the message."""


class BaseAgentFactory(Protocol):
    """A factory creating and configuring agent graphs."""

    employee_id: UUID
    mcp_servers: Optional[AgentMCPConfig]

    def context(
        self, checkpoiner: BaseCheckpointSaver
    ) -> AsyncContextManager[BaseAgent]:
        """Open agent execution context and return a contextualized agent."""
        ...
