import asyncio
import json
import logging
import traceback
from typing import Any, Callable, Literal, Optional
from uuid import UUID, uuid4

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession

from information_flows.core.agent import (
    AgentChannel,
    BaseAgentFactory,
    TelemetryContext,
    agent_execution_context,
)
from information_flows.core.measurement import measurement_session
from information_flows.core.telemetry import Telemetry, inject_tracing_context
from information_flows.core.types import (
    AgentEvent,
    AgentEventType,
    Message,
    MessageWithPriority,
)
from information_flows.core.utils import ActivityRecorder, execute_async
from information_flows.database.event import EventModel

logger = logging.getLogger("executor")
logger.setLevel(logging.INFO)

type EventCallback = Callable[[AgentEvent], None]

KILL_AGENT_MESSAGE = object()


class AgentDiedMessage(BaseModel):
    """A signal that the agent with the ID died and broadcast it to everyone."""

    agent_id: UUID


class AgentExecutor:
    in_queue: asyncio.PriorityQueue[MessageWithPriority]
    """A queue for incoming messages to the agent."""
    out_queue: asyncio.Queue[Message]
    """A queue for outgoing messages from the agent."""
    signal_queue: asyncio.Queue
    """A queue for input signals to the agent."""
    event_callbacks: list[EventCallback]
    """Registered callbacks for output events from the agent."""
    state: Literal["idle", "processing"]

    def __init__(
        self,
        agent_id: UUID,
        checkpointer: AsyncPostgresSaver,
        agent_factory: BaseAgentFactory,
        out_queue: asyncio.Queue[Message],
        event_callbacks: list[EventCallback],
        engine: AsyncEngine,
    ):
        self.agent_id = agent_id
        self.checkpointer = checkpointer
        self.agent_factory = agent_factory
        self.engine = engine
        self.in_queue = asyncio.PriorityQueue()
        self.out_queue = out_queue
        self.signal_queue = asyncio.Queue()
        self.event_callbacks = event_callbacks
        self.telemetry = Telemetry(f"agent:{agent_id}")
        self.state = "idle"
        self.activity_recorder = ActivityRecorder()

    async def run(self) -> None:
        """Start an infinite agent loop accepting messages."""
        message_retrieved = False
        try:
            agent_turn_id = f"turn:{uuid4()}"
            agent_channel = AgentChannel(self.read_signal, self.send_event)
            trajectory_tracer = measurement_session.trajectory_tracer(self.agent_id)
            with self.telemetry.tracer.start_as_current_span(agent_turn_id) as span:
                if self.agent_factory.mcp_servers:
                    inject_tracing_context(self.agent_factory.mcp_servers)
                telemetry_context = TelemetryContext(
                    tracer=self.telemetry.tracer,
                    agent_turn_span=span,
                )
                async with (
                    trajectory_tracer.begin(),
                    agent_execution_context(
                        send_message_callback=self.send_message,
                        agent_channel=agent_channel,
                        trajectory_tracer=trajectory_tracer,
                        telemetry_context=telemetry_context,
                    ),
                    self.agent_factory.context(self.checkpointer) as agent,
                ):
                    # Start agent (including parallel processing if enabled)
                    if hasattr(agent, 'start'):
                        await agent.start()
                    
                    try:
                        while True:
                            message_with_priority = await self.in_queue.get()
                            message_retrieved = True
                            if message_with_priority.message is KILL_AGENT_MESSAGE:
                                self.in_queue.task_done()
                                break
                            self.state = "processing"
                            self.activity_recorder.record_start()
                            await agent.run(message_with_priority.message)
                            self.state = "idle"
                            self.activity_recorder.record_finish()
                            self.in_queue.task_done()
                            message_retrieved = False
                    finally:
                        # Stop agent (including parallel processing if enabled)
                        if hasattr(agent, 'stop'):
                            await agent.stop()
        except Exception as exc:
            logger.error("Internal agent error", exc_info=True)
            if message_retrieved:
                self.in_queue.task_done()
            await self.out_queue.put(AgentDiedMessage(agent_id=self.agent_id))
            agent_error = "".join(traceback.format_exception(exc))
            await self.send_event("agent_error", value=agent_error)
            raise

    async def read_signal(self, wait: bool = False) -> Optional[Any]:
        """A callback to be provided inside of agent execution context."""
        if wait:
            payload = await self.signal_queue.get()
            self.signal_queue.task_done()
            return payload
        else:
            try:
                payload = self.signal_queue.get_nowait()
                if payload:
                    self.signal_queue.task_done()
                return payload
            except asyncio.QueueEmpty:
                return None

    async def persist_event(self, event: AgentEvent) -> None:
        """Persist an event to the database."""
        serializable = event.model_dump(mode="json")
        value = json.dumps(serializable["value"]) if event.value else None
        parent = json.dumps(serializable["parent"]) if event.parent else None
        async with AsyncSession(self.engine) as session:
            async with session.begin():
                session.add(
                    EventModel(
                        event_id=event.id,
                        agent_id=self.agent_id,
                        timestamp=event.timestamp,
                        type=event.type,
                        value=value,
                        parent=parent,
                    )
                )

    async def send_event(
        self,
        event_type: AgentEventType,
        value: Optional[Any] = None,
        parent: Optional[AgentEvent] = None,
    ) -> AgentEvent:
        """A callback to be provided inside of agent execution context."""
        event = AgentEvent(
            agent_id=self.agent_id,
            type=event_type,
            value=value,
            parent=parent,
        )
        await self.persist_event(event)
        for callback in self.event_callbacks:
            asyncio.create_task(execute_async(callback, event))
        return event

    async def put_message(self, message: Message) -> None:
        """Receive a new message and annotate it with a delivery priority."""
        if message is KILL_AGENT_MESSAGE:
            message_with_priority = MessageWithPriority(0, message)
        else:
            message_with_priority = MessageWithPriority(1, message)
        await self.in_queue.put(message_with_priority)

    async def send_message(self, content: Any, receiver_id: UUID) -> Message:
        """A callback to be provided inside of agent execution context."""
        message = Message(
            content=content,
            receiver_id=receiver_id,
            sender_id=self.agent_id,
            sender_role="agent",
        )
        await self.out_queue.put(message)
        return message
