import io
import itertools
import os
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, <PERSON><PERSON><PERSON>

from rich.console import Console
from rich.style import Style

from information_flows.settings import settings

TRAJECTORIES_DIR = "./trajectories"

DIALOGUE_WIDTH = 100

COLORS = [
    Style(color="green", bold=True),
    Style(color="blue", bold=True),
    Style(color="magenta", bold=True),
    Style(color="yellow", bold=True),
    Style(color="cyan", bold=True),
    Style(color="rgb(219, 160, 48)"),
]


def make_color_selector():
    """Cycle through colors returning the next one."""
    colors = itertools.cycle(COLORS)
    return lambda: next(colors)


next_color = make_color_selector()


@contextmanager
def with_title(title: str, color: str, console: Console):
    left, right = f"{title:^98}".split(title)
    left = "-" * len(left) + " "
    right = " " + "-" * len(right)
    console.print(left + title + right, style=color, width=DIALOGUE_WIDTH)
    yield
    console.print("-" * DIALOGUE_WIDTH, style=color, width=DIALOGUE_WIDTH)


def format_object(obj: Any) -> str:
    buffer = io.StringIO()
    console = Console(file=buffer)
    console.print(obj, highlight=True)
    return buffer.getvalue()


class AgentLogger(Console):
    """Formatting and logging key events from agents to the console and a file (for debugging only)."""

    def __init__(self, persist_to_file: bool = False):
        super().__init__(record=True, force_terminal=True)
        self.persist_to_file = persist_to_file
        self.participant_colors = {}

        os.makedirs(TRAJECTORIES_DIR, exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%S")

    def color_of(self, tag: Hashable) -> Style:
        """Return a color of a conversation participant with a given `tag`."""
        if tag not in self.participant_colors:
            self.participant_colors[tag] = next_color()
        return self.participant_colors[tag]

    def assistant_received(self, tag: Hashable, content: str, chief: str) -> None:
        tag = f"assistant:{tag}"
        participant = f"{chief}'s assistant"
        with with_title(participant, self.color_of(tag), self):
            self.print(
                f"{participant} received:\n{content}",
                style=self.color_of(tag),
                highlight=True,
                width=DIALOGUE_WIDTH,
            )

    def assistant_internal(self, tag: Hashable, content: str, chief: str) -> None:
        tag = f"assistant-internal:{tag}"
        title = f"{chief}'s assistant [INTERNAL]"
        with with_title(title, self.color_of(tag), self):
            self.print(content, width=None)

    def employee_received(self, tag: Hashable, content: str, name: str) -> None:
        tag = f"chief:{tag}"
        with with_title(name, self.color_of(tag), self):
            self.print(
                f"{name} received:\n{content}",
                style=self.color_of(tag),
                highlight=True,
                width=DIALOGUE_WIDTH,
            )

    def using_tool(
        self, name: str, *, content: str | None = None, mcp: str | None = None
    ) -> None:
        output = f"Using `{name}` tool"
        if mcp:
            output += f" from `{mcp}` MCP server"
        if content:
            output += f":\n{content}"
        self.print(output)

    def checkpoint_trajectory(self) -> None:
        """Export a logging trajectory as an HTML file."""
        os.makedirs(TRAJECTORIES_DIR, exist_ok=True)
        with open(Path(TRAJECTORIES_DIR) / f"tj-{self.timestamp}.html", "w+") as file:
            file.write(self.export_html())


agent_logger = AgentLogger(persist_to_file=(settings.ENVIRONMENT == "development"))
