import functools
import inspect
from typing import Any

from langchain_core.runnables import RunnableConfig


def node():
    """Extract agent graph config dictionary and pass it to an agent graph node."""

    def decorator(func):
        if inspect.iscoroutinefunction(func):

            @functools.wraps(func)
            async def wrapper(state: dict[str, Any], config: RunnableConfig):
                return await func(state, config["configurable"])

            return wrapper
        else:

            @functools.wraps(func)
            def wrapper(state: dict[str, Any], config: RunnableConfig):
                return func(state, config["configurable"])

            return wrapper

    return decorator


def make_registry():
    """A utility registry decorator factory."""
    registry = []

    def register(func):
        """Add a function to the registry."""
        registry.append(func)
        return func

    return registry, register
