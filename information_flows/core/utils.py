import asyncio
import inspect
from collections.abc import Callable
from datetime import datetime, timedelta


async def execute_async[R](func: Callable[..., R], *args) -> R:
    """Asynchronously execute sync function or async function (coroutine)."""
    if inspect.iscoroutinefunction(func):
        return await func(*args)
    else:
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, func, *args)


class ActivityRecorder:
    """Record active state of long-running processes."""

    def __init__(self):
        self.created_at = datetime.now()
        self.last_started_at = None
        self.last_finished_at = None

    def record_start(self) -> None:
        self.last_started_at = datetime.now()
        self.last_finished_at = None

    def record_finish(self) -> None:
        self.last_finished_at = datetime.now()

    def is_active_now(self) -> bool:
        if self.last_started_at and self.last_finished_at is None:
            return True
        return False

    def last_active_at(self) -> datetime | None:
        if self.is_active_now():
            return datetime.now()
        else:
            if self.last_finished_at:
                return self.last_finished_at
            else:
                # never was active
                return None

    def idle_for(self) -> timedelta:
        if self.is_active_now():
            return timedelta(seconds=0)
        elif self.last_finished_at:
            return datetime.now() - self.last_finished_at
        else:
            # never was active
            return datetime.now() - self.created_at
