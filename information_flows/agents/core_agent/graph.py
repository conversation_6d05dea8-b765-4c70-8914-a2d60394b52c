import logging
import operator
from typing import Annotated, Optional, TypedDict, TypeVar

from langchain_anthropic import ChatAnthropic
from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.graph.message import RemoveMessage
from langgraph.prebuilt import ToolNode

from information_flows.agents.core_agent import measurement
from information_flows.agents.core_agent.context import (
    AgentContextService,
    Chief<PERSON><PERSON><PERSON><PERSON>,
    ChiefRela<PERSON>,
)
from information_flows.agents.core_agent.observations import (
    ActionChoiceMessage,
    ActionResultMessage,
    AssistantMessage,
    ChiefMessage,
    Observation,
)
from information_flows.agents.core_agent.prompts import (
    Specification,
    load_memory_blueprint,
)
from information_flows.agents.core_agent.subsystems import acting, multitasking
from information_flows.agents.core_agent.tools import EndTurnReason
from information_flows.core.agent import (
    BaseAgentConfig,
    get_telemetry_context,
    read_signal,
    record_measurement,
    send_event,
)
from information_flows.core.decorators import node
from information_flows.core.logger import agent_logger, format_object
from information_flows.core.telemetry import (
    telemetry_serialize_state,
    telemetry_serialize_tools,
)
from information_flows.core.types import AgentEvent, Message
from information_flows.database.setup import async_engine as async_engine

logger = logging.getLogger("graph")
logger.setLevel(logging.INFO)


M = TypeVar("M", ChatOpenAI, ChatAnthropic)


class LLMConfig(TypedDict):
    """An LLM to use in each component of the agent."""

    focus_attention: M
    choose_action: M


class AgentConfig(BaseAgentConfig):
    llm: LLMConfig
    context: AgentContextService
    specification: Specification


class AgentState(TypedDict):
    received_message: Message
    chief_profile: ChiefProfile
    chief_relations: ChiefRelations
    observations: Annotated[list[Observation], add_messages]
    tasks: Annotated[list[multitasking.Task], multitasking.update_tasks]
    completed_tasks: Annotated[list[multitasking.CompletedTask], operator.add]
    current_task: Optional[multitasking.Task]
    explanation: Optional[acting.Explanation]
    current_action_event: Optional[AgentEvent]


class PartialAgentState(AgentState, total=False):
    pass


@node()
async def agent_setup(state: AgentState, config: AgentConfig) -> PartialAgentState:
    get_telemetry_context().agent_turn_span.set_attribute(
        "start_state", telemetry_serialize_state(state, AgentState)
    )
    chief_profile = await config["context"].chief_profile()
    chief_relations = await config["context"].chief_relations()
    await send_event("agent_start")
    return {
        "chief_profile": chief_profile,
        "chief_relations": chief_relations,
    }


@node()
async def perceive_input(state: AgentState, config: AgentConfig) -> PartialAgentState:
    """Convert the received message into an appropriate internal format."""
    with get_telemetry_context().tracer.start_as_current_span("perceive_input") as span:
        agent_logger.assistant_received(
            config["context"].employee_id,
            state["received_message"].content,
            state["chief_profile"].name,
        )
        await record_measurement(
            measurement.PerceiveMessage(message=state["received_message"])
        )
        if state["received_message"].sender_role == "user":
            message = ChiefMessage(
                state["received_message"].content, state["chief_profile"].name
            )
        else:
            employee = await config["context"].employee_profile(
                state["received_message"].sender_id
            )
            message = AssistantMessage(state["received_message"].content, employee.name)
        return {"observations": message}


@node()
async def focus_attention(state: AgentState, config: AgentConfig) -> PartialAgentState:
    """Analyze the last observation and determine the current task to work on."""
    with get_telemetry_context().tracer.start_as_current_span(
        "focus_attention"
    ) as span:
        prompt = await load_memory_blueprint("focus_attention.jinja")
        chain = prompt | config["llm"]["focus_attention"].with_structured_output(
            multitasking.MessageAnalysis, method="json_schema", strict=True
        )
        event = await send_event(
            "action_start",
            value={"title": "Analyze message", "content": "Analyzing the message..."},
        )
        analysis: multitasking.MessageAnalysis = await chain.ainvoke(
            {
                "specification": config["specification"],
                "observations": state["observations"],
                "completed_tasks": state["completed_tasks"],
                "tasks": state["tasks"],
                "chief": state["chief_profile"],
                "relations": state["chief_relations"],
            }
        )
        span.set_attribute("message_analysis", analysis.model_dump_json())

        agent_logger.assistant_internal(
            config["context"].employee_id,
            f"Focus on task:\n{format_object(analysis)}",
            state["chief_profile"].name,
        )

        await record_measurement(measurement.FocusAttention(analysis=analysis))

        await send_event(
            "action_end",
            value={"title": "Analyze message", "content": analysis.reasoning},
            parent=event,
        )
        return {"tasks": analysis.current_task, "current_task": analysis.current_task}


@node()
async def choose_action(state: AgentState, config: AgentConfig) -> PartialAgentState:
    """Choose the next action or actions based on previous observations."""
    with get_telemetry_context().tracer.start_as_current_span("choose_action") as span:
        event = await send_event(
            "action_start",
            value={"title": "Choose action", "content": "Choosing an action..."},
        )

        prompt = await load_memory_blueprint("choose_action.jinja")
        tools = config["context"].get_tools()
        if state.get("explanation"):
            # It is not the first action, so we reflect on the previous one
            action_schema = await acting.ReflectAndChooseAction.with_tools(tools)
        else:
            # It is the first action and we have nothing to reflect on
            action_schema = await acting.ChooseAction.with_tools(tools)
        chain = prompt | config["llm"]["choose_action"].with_structured_output(
            action_schema, method="json_schema", strict=True, include_raw=True
        )

        result: acting.ChooseActionResult = await chain.ainvoke(
            {
                "specification": config["specification"],
                "observations": state["observations"],
                "completed_tasks": state["completed_tasks"],
                "current_task": state["current_task"],
                "chief": state["chief_profile"],
                "relations": state["chief_relations"],
                "explanation": state.get("explanation"),
            }
        )
        action_choice = result["parsed"]
        action_message = action_schema.to_action_message(result)

        if state.get("explanation"):
            # Record reflection on the previous action
            reflection = action_choice.get_reflection()
            span.set_attribute("reflection", reflection.model_dump_json())
            agent_logger.assistant_internal(
                config["context"].employee_id,
                f"Reflection:\n{format_object(reflection)}",
                state["chief_profile"].name,
            )

        # Record explanation and reasoning about the next action
        explanation = action_choice.get_explanation()
        span.set_attribute("actions", telemetry_serialize_tools(action_message))
        span.set_attribute("explanation", explanation.model_dump_json())
        span.set_attribute("task_update", action_choice.updated_task.model_dump_json())
        await record_measurement(
            measurement.ChooseAction(actions=action_message.actions())
        )
        agent_logger.assistant_internal(
            config["context"].employee_id,
            f"Explanation:\n{format_object(explanation)}",
            state["chief_profile"].name,
        )
        agent_logger.assistant_internal(
            config["context"].employee_id,
            f"Updated task:\n{format_object(action_choice.updated_task)}",
            state["chief_profile"].name,
        )

        await send_event(
            "action_end",
            value={
                "title": "Choose action",
                "content": explanation.next_action_reasoning,
            },
            parent=event,
        )

        # Do not send an event about the action of ending the current turn
        tool_call = action_message.tool_calls[0]
        action_event = None
        if tool_call["name"] != "end_turn":
            action_event = await send_event(
                "action_start", value="Executing the action..."
            )
        return {
            "observations": action_message,
            "explanation": explanation,
            "current_task": action_choice.updated_task,
            "tasks": action_choice.updated_task,
            "current_action_event": action_event,
        }


def route_actions(state: AgentState):
    last_message = state["observations"][-1]
    for tool_call in last_message.tool_calls:
        if tool_call["name"] == "end_turn":
            if len(last_message.tool_calls) != 1:
                raise ValueError("`end_turn` must be called alone")
            # The last and the only action is to end the turn
            return "end_turn"
    return "tools"


async def perceive_result(state: AgentState) -> PartialAgentState:
    await send_event(
        event_type="action_end",
        value="Action completed!",
        parent=state["current_action_event"],
    )
    tool_message = state["observations"][-1]
    action_message = ActionResultMessage.from_tool_message(tool_message)
    await record_measurement(measurement.PerceiveResult(result=action_message.result()))
    return {
        "observations": [RemoveMessage(id=tool_message.id), action_message],
        "current_action_event": None,
    }


@node()
async def end_turn(state: AgentState, config: AgentConfig) -> PartialAgentState:
    """Complete the current turn with with `end_turn` tool call."""
    with get_telemetry_context().tracer.start_as_current_span("end_turn") as span:
        last_message: ActionChoiceMessage = state["observations"][-1]
        end_turn_call = last_message.tool_calls[0]
        reason: EndTurnReason = end_turn_call["args"]["reason"]
        agent_logger.assistant_internal(
            config["context"].employee_id,
            f"End turn reason: {reason.upper()}",
            state["chief_profile"].name,
        )
        span.set_attribute("reason", reason)

        if reason == "task_completed":
            tasks = [multitasking.RemoveTask(task=state["current_task"])]
            completed = [multitasking.CompletedTask.from_task(state["current_task"])]
        else:
            tasks = [state["current_task"]]
            completed = []
        return {
            "observations": [RemoveMessage(id=last_message.id)],
            "current_task": None,
            "completed_tasks": completed,
            "tasks": tasks,
            "explanation": None,
        }


async def agent_teardown(state: AgentState) -> None:
    get_telemetry_context().agent_turn_span.set_attribute(
        "end_state", telemetry_serialize_state(state, AgentState)
    )
    await send_event("agent_end")


def create_agent_graph(checkpointer: BaseCheckpointSaver, tools: list[BaseTool]):
    graph_builder = StateGraph(AgentState)
    graph_builder.add_node("agent_setup", agent_setup)
    graph_builder.add_node("perceive_input", perceive_input)
    graph_builder.add_node("focus_attention", focus_attention)
    graph_builder.add_node("choose_action", choose_action)
    graph_builder.add_node("tools", ToolNode(tools, messages_key="observations"))
    graph_builder.add_node("perceive_result", perceive_result)
    graph_builder.add_node("end_turn", end_turn)
    graph_builder.add_node("agent_teardown", agent_teardown)
    graph_builder.add_edge(START, "agent_setup")
    graph_builder.add_edge("agent_setup", "perceive_input")
    graph_builder.add_edge("perceive_input", "focus_attention")
    graph_builder.add_edge("focus_attention", "choose_action")
    graph_builder.add_conditional_edges(
        "choose_action", route_actions, ["tools", "end_turn"]
    )
    graph_builder.add_edge("tools", "perceive_result")
    graph_builder.add_edge("perceive_result", "choose_action")
    graph_builder.add_edge("end_turn", "agent_teardown")
    graph_builder.add_edge("agent_teardown", END)
    return graph_builder.compile(checkpointer)
