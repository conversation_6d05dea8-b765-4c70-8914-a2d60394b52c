from uuid import UUID

import sqlalchemy as sa
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from information_flows.database.employee import EmployeeModel
from information_flows.database.integration import IntegrationModel
from information_flows.database.relation import RelationModel
from information_flows.database.setup import async_engine


class ChiefProfile(BaseModel):
    name: str
    external_employee_id: str
    static_preferences: str | None


class EmployeeProfile(BaseModel):
    name: str
    external_employee_id: str


type ChiefRelations = list[EmployeeProfile]


class AgentContextService:
    def __init__(self, employee_id: UUID):
        self.employee_id = employee_id

    async def chief_profile(self) -> ChiefProfile:
        """Return chief's profile information from the database."""
        async with AsyncSession(async_engine) as session:
            async with session.begin():
                employee = await session.scalar(
                    sa.select(EmployeeModel).where(
                        EmployeeModel.employee_id == self.employee_id
                    )
                )
                integration = await session.scalar(
                    sa.select(IntegrationModel).where(
                        IntegrationModel.local_employee_id == self.employee_id
                    )
                )
                return Chief<PERSON><PERSON><PERSON>le(
                    name=integration.external_employee_name,
                    external_employee_id=integration.external_employee_id,
                    static_preferences=employee.static_preferences,
                )

    async def chief_relations(self) -> ChiefRelations:
        """Return a list of profiles of employees connected with relations to chief."""
        async with AsyncSession(async_engine) as session:
            async with session.begin():
                relations = await session.scalars(
                    sa.select(IntegrationModel)
                    .join(
                        RelationModel,
                        IntegrationModel.local_employee_id
                        == RelationModel.to_employee_id,
                    )
                    .where(
                        RelationModel.from_employee_id == self.employee_id,
                    )
                )
                return [
                    EmployeeProfile(
                        external_employee_id=relation.external_employee_id,
                        name=relation.external_employee_name,
                    )
                    for relation in relations
                ]

    async def employee_profile(self, local_employee_id: UUID) -> EmployeeProfile:
        """Return profile of an employee with a given ID from the database."""
        async with AsyncSession(async_engine) as session:
            async with session.begin():
                integration = await session.scalar(
                    sa.select(IntegrationModel).where(
                        IntegrationModel.local_employee_id == local_employee_id
                    )
                )
                return EmployeeProfile(
                    external_employee_id=integration.external_employee_id,
                    name=integration.external_employee_name,
                )

    async def external_to_local(self, external_employee_id: str) -> UUID:
        async with AsyncSession(async_engine) as session:
            async with session.begin():
                local_employee_id = await session.scalar(
                    sa.select(IntegrationModel.local_employee_id).where(
                        IntegrationModel.external_employee_id == external_employee_id
                    )
                )
                if not local_employee_id:
                    raise ValueError(
                        f"not existing `external_employee_id`: {external_employee_id}"
                    )
                return local_employee_id

    async def local_to_external(self, local_employee_id: UUID) -> str:
        async with AsyncSession(async_engine) as session:
            async with session.begin():
                return await session.scalar(
                    sa.select(IntegrationModel.external_employee_id).where(
                        IntegrationModel.local_employee_id == local_employee_id
                    )
                )
