import mlflow.metrics
from deepeval.metrics import GEval
from deepeval.test_case import LLMTest<PERSON>ase, LLMTestCaseParams
from mlflow.metrics.base import MetricValue
from mlflow.models import make_metric
from pydantic import BaseModel

from information_flows.agents.core_agent.measurement import (
    AgentTrajectory,
    MeasurementResult,
)


class MetricResult(BaseModel):
    name: str
    score: float
    reasoning: str
    evaluation_cost: float


with open(
    "information_flows/agents/core_agent/metrics/prompt_templates/intent_resolution.jinja"
) as file:
    prompt = file.read()


class IntentResolutionMetric:
    name = "Intent Resolution"

    def __init__(self, model: str = "gpt-4.1-mini"):
        self.evaluator = GEval(
            name=self.name,
            criteria=prompt,
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            model=model,
        )

    def project(self, trajectory: AgentTrajectory) -> LLMTestCase:
        input_message = trajectory.measurement("perceive_message").message.content
        message_analysis = trajectory.measurement("focus_attention").analysis
        return LLMTestCase(
            input=input_message,
            actual_output=f"""
            Inferred author intent: {message_analysis.author_intent}
            Reasoning: {message_analysis.reasoning}
            Inferred task description: {message_analysis.current_task.description}
            Inferred task goal: {message_analysis.current_task.goal}
            """,
        )

    def evaluate(self, trajectory: AgentTrajectory):
        test_case = self.project(trajectory)
        self.evaluator.measure(test_case)
        return MetricResult(
            name=self.name,
            score=self.evaluator.score,
            reasoning=self.evaluator.reason,
            evaluation_cost=self.evaluator.evaluation_cost,
        )

    def as_metric(self):
        def eval_fn(predictions):
            trajectory = AgentTrajectory.model_validate_json(predictions[0])
            result = self.evaluate(trajectory)
            return MetricValue(scores=[result.score], justifications=[result.reasoning])

        return make_metric(
            eval_fn=eval_fn, greater_is_better=True, name="intent_resolution"
        )
