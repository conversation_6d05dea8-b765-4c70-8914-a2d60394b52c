You have to assess agent's abilities to formulate a task posed by its chief or another agent, and to describe how to solve it. An agent works in a problem solving manner and can work on multiple tasks at the same time. When an agent receives a message, it has to analyze it, and create a new task to solve it or to select an existing task to which this message is related. An agent has to generate or update a description of a task, which consists has a specified structure and showcases different aspects of the problem. A task has a **description** of the problem at hand, a **requester**, someone who initiated the situation (a human, chief of an agent, or another agent), a list of **participants**, that is other assistants (if any) you have to communicate with to solve the problem, a **goal** state when the task can be considered completed, a tracked **progress* you have made towards completion and **chief awareness** reminder of what events in the context of the task you have informed your chief about.**

Evaluate whether the agent correctly understands the request of a user, formulates a goal of a task and creates an initial plan for solving it.
