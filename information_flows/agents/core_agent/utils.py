import re
from textwrap import dedent


def compress(text: str) -> str:
    """Transform multiline text into a single line string."""
    return re.sub(r"\s+", " ", text.strip())


def align(text: str) -> str:
    """Dedent and strip the text."""
    return dedent(text).strip()


def remove_indent(text: str) -> str:
    """Remove all spaces starting a line."""
    return re.sub(r"\n +", "\n", text.strip())
