{% extends "base.jinja" %}
{% block prompt_body %}
The task you are currently working on:
{{ current_task | for_llm }}
{% endblock prompt_body %}

{% block prompt_suffix %}
{% block explanation %}
{% if explanation %}
## Explanation

My reasoning about the last action and what I expected to achieve:
{{ explanation | for_llm }}

---

{% endif %}
{% endblock explanation %}
Make a sequence of actions using available tools to make progress with the current task, and when you are done or need additional input from other assistants or your chief, call a single tool `end_turn`, recording the state of the current task. Call `end_turn` only if you DO NOT plan to use any other tools or communicate with anyone for now, as you will not be able to call other tools after the `end_turn` (even send a message to your chief or another assistant). You have to complete all planned steps and execute actions as long as the task is not completed (all steps are done). You MUST call `end_turn` as the last tool. When a message arrives, you do not need to respond and finish. You can respond with a message and continue performing other actions, or even send more messages later, and perform more actions. If you want to communicate with your chief or other assistant, you must call `communicate_with_` tools. You must assure yourself that a message was delivered by checking a confirmation from these tools. You should try to respond to every message.
{% endblock prompt_suffix %}
