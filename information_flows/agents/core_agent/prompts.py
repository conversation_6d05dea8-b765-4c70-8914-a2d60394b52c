import datetime
import re
from pathlib import Path
from unittest.mock import patch

import aiofiles
from aiofiles import os, ospath
from jinja2 import Environment, PackageLoader, meta
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langchain_core.prompts.string import DEFAULT_FORMATTER_MAPPING
from pydantic import BaseModel

from information_flows.agents.core_agent.observations import format_observations

PROMPT_TEMPLATES_ROOT = "./information_flows/agents/core_agent/prompt_templates"

# required article number format to use in .md specification
ARTICLE_NUMBER_PATTERN = r"\[Article\]\((?P<number>\d+)\)"

environment = Environment(
    loader=PackageLoader(
        package_name="information_flows.agents.core_agent",
        package_path="prompt_templates",
    ),
    trim_blocks=True,
)

environment.globals["current_datetime"] = lambda: datetime.datetime.now()

environment.filters["format_observations"] = format_observations
environment.filters["for_llm"] = lambda llm_readable: llm_readable.for_llm()


def jinja2_formatter(template: str, /, **kwargs) -> str:
    """Render a prompt `template` in a local Jinja2 environment."""
    return environment.from_string(template).render(**kwargs)


DEFAULT_FORMATTER_MAPPING["jinja2"] = jinja2_formatter


def get_jinja2_variables_from_template(template: str) -> set[str]:
    ast = environment.parse(template)
    return meta.find_undeclared_variables(ast)


patch(
    "langchain_core.prompts.string._get_jinja2_variables_from_template",
    new=get_jinja2_variables_from_template,
).start()


async def load_memory_blueprint(template_filename: str | Path) -> ChatPromptTemplate:
    """Load a prompt template from a `.jinja` template file."""
    folder_root = await ospath.abspath(PROMPT_TEMPLATES_ROOT)
    template_filepath = Path(folder_root) / template_filename
    if not await ospath.exists(template_filepath):
        raise FileNotFoundError(
            f"Prompt template file `{template_filename}` not found in `{folder_root}`"
        )
    async with aiofiles.open(template_filepath) as file:
        prompt_template = await file.read()
        return ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(
                    prompt_template.strip(),
                    template_format="jinja2",
                )
            ]
        )


class Specification(BaseModel):
    articles: list[str]

    def __iter__(self):
        return iter(self.articles)

    @classmethod
    async def from_folder(cls, folder_root: str) -> "Specification":
        """Load assistant instructions from a set of .md files in a folder."""
        number_article = {}
        for file in await os.scandir(folder_root):
            async with aiofiles.open(file.path) as file:
                content = await file.read()
                content = content.strip()
                match = re.findall(ARTICLE_NUMBER_PATTERN, content)
                if not match:
                    raise ValueError(
                        f"Article number is not specified in `{file.name}`"
                    )
                number = int(match[0])
                if number in number_article:
                    raise ValueError(f"Duplicate article number in `{file.name}`")
                number_article[number] = content
        number_article = sorted(number_article.items())
        articles = [na[1] for na in number_article]
        return cls(articles=articles)


class CognitiveArtifact(BaseModel):
    """A signifier that the Pydantic model is both generated and consumed by an LLM."""

    def for_llm(self) -> str:
        """Serialize the model into an LLM- and human-readable representation."""
        raise NotImplementedError
