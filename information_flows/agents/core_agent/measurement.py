from datetime import datetime
from typing import Annotated, Literal, Optional

from pydantic import BaseModel, Field

from information_flows.agents.core_agent.observations import Action, ActionResult
from information_flows.agents.core_agent.subsystems.multitasking import MessageAnalysis
from information_flows.core.measurement import (
    BaseAgentTrajectory,
    BaseMeasurement,
    BaseMeasurementResult,
    MeasuredAgent,
)
from information_flows.core.types import Message


class DialogueMessage(BaseModel):
    sender: Literal["user", "target_agent", "companion_agent"]
    content: str
    timestamp: datetime

    def format(self) -> str:
        return f"{self.sender}: {self.content}"

    @staticmethod
    def format_dialogue(dialogue: list["DialogueMessage"]) -> str:
        formatted = []
        for message in dialogue:
            formatted.append(message.format())
        return "\n".join(formatted)


type MeasurementType = Literal[
    "perceive_message",
    "focus_attention",
    "choose_action",
    "perceive_result",
    "send_message",
]


class PerceiveMessage(BaseMeasurement):
    type: Literal["perceive_message"] = "perceive_message"
    message: Message


class FocusAttention(BaseMeasurement):
    type: Literal["focus_attention"] = "focus_attention"
    analysis: MessageAnalysis


class ChooseAction(BaseMeasurement):
    type: Literal["choose_action"] = "choose_action"
    actions: list[Action]


class PerceiveResult(BaseMeasurement):
    type: Literal["perceive_result"] = "perceive_result"
    result: ActionResult


class SendMessage(BaseMeasurement):
    type: Literal["send_message"] = "send_message"
    message: Message


type Measurement = Annotated[
    PerceiveMessage | FocusAttention | ChooseAction | PerceiveResult | SendMessage,
    Field(discriminator="type"),
]


class AgentTrajectory(BaseAgentTrajectory):
    measurements: list[Measurement] = Field(default_factory=list)

    def measurement(self, measurement_type: MeasurementType) -> Optional[Measurement]:
        """Return the first encountered measurement of `measurement_type`."""
        for measurement in self.measurements:
            if measurement.type == measurement_type:
                return measurement

    def all_measurements(self, measurement_type: MeasurementType) -> list[Measurement]:
        """Return all measurements of `measurement_type` in the trajectory."""
        return [m for m in self.measurements if m.type == measurement_type]

    def received_message(self) -> DialogueMessage:
        message = self.measurement("perceive_message").message
        return DialogueMessage(
            sender="user",
            content=message.content,
            timestamp=message.timestamp,
        )

    def performed_actions(self) -> list[Action]:
        actions = []
        for measurement in self.measurements:
            if measurement.type == "choose_action":
                for action in measurement.actions:
                    actions.append(action)
        return actions

    def tool_calls(self, tool_name: str) -> list[Action]:
        calls = []
        for action in self.performed_actions():
            if action.name == tool_name:
                calls.append(action)
        return calls


class MeasurementResult(BaseMeasurementResult[AgentTrajectory]):
    trajectories: list[AgentTrajectory]

    def dialogue(
        self,
        target_agent: MeasuredAgent,
        companion_agent: Optional[MeasuredAgent] = None,
    ) -> list[DialogueMessage]:
        """Return a dialogue of a target agent with another agent or its chief if unspecified."""
        messages: list[DialogueMessage] = []
        from_id = target_agent.agent_id
        to_id = companion_agent.agent_id if companion_agent else target_agent.agent_id
        for message in self.messages:
            if (message.sender_id == from_id and message.receiver_id == to_id) or (
                message.sender_id == to_id and message.receiver_id == from_id
            ):
                # the a message is between `from_id` and `to_id`
                if message.sender_id == message.receiver_id:
                    # the message is between an employee and an assistant
                    sender = "user" if message.sender_role == "user" else "target_agent"
                else:
                    # the message is between two agents
                    if message.sender_id == from_id:
                        sender = "target_agent"
                    else:
                        sender = "companion_agent"
                messages.append(
                    DialogueMessage(
                        sender=sender,
                        content=message.content,
                        timestamp=message.timestamp,
                    )
                )
        messages = sorted(messages, key=lambda message: message.timestamp)
        return messages
