import asyncio
import os
from typing import Dict, List, Literal

import dotenv
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from information_flows.agents.core_agent.mcp.policy_adv.policy_tree import Tree

dotenv.load_dotenv()

PolicyType = Literal[
    "bench_policy",
    "equipment_policy",
    "hiring_policy",
    "vacation_policy",
    "project_launch_policy",
    "project_rules_policy",
    "user_story_decomposition_policy",
    "user_story_estimation_policy",
    "user_story_policy",
    "insurance_policy",
    "holidays_policy",
    "payment_policy",
    "time_tracking_policy",
    "working_schedule_policy",
    "teem_lead_selection_policy",
]

prompt_template = """
You are an Advanced Policy Data Search Algorithm. You are provided with [POLICIES_FOLDER_STRUCTURE].

Your task is to recommend from 0 to 5 relevant policies to check based in [USER_REQUEST].


[USER_REQUEST]: {user_query}

[POLICIES_FOLDER_STRUCTURE]: {policies}
"""


class Policy(BaseModel):

    selected_policy_txt_file_name: PolicyType = Field(
        default_factory="",
        description="The name of the policy file which is relevant to the [USER_REQUEST]",
    )


class RelevantPolicies(BaseModel):
    relevant_policies: List[Policy] = Field(
        default_factory="",
        description="The list of relevant policy files of the policy file which is relevant to the query",
    )


class PolicySearchAgent:

    prompt_template = prompt_template

    tree = Tree.build(os.getenv("POLICY_DIR"))

    def __init__(self):

        self.llm = ChatOpenAI(
            model=os.getenv("SEARCH_MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    async def recommend_policies(self, queries: Dict[str, str]) -> dict[str, str]:

        model = self.llm.with_structured_output(RelevantPolicies)

        prompt = PromptTemplate(
            template=self.prompt_template,
            input_variables=["user_query", "policies"],
        )

        chain = prompt | model

        async def process_single_request(kwargs):
            try:
                return await chain.ainvoke(input=kwargs)
            except Exception as e:
                return {"error": str(e), "input": kwargs}

        batch_kwargs = [
            {"user_query": q, "policies": self.tree.to_json()} for q in queries
        ]

        tasks = [process_single_request(kwargs) for kwargs in batch_kwargs]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        final_results = {}
        for query, result in zip(queries, results):
            final_results[query] = self.get_context(
                query=query,
                ids=[p.selected_policy_txt_file_name for p in result.relevant_policies][
                    :5
                ],
            )

        return final_results

    def get_context(self, query, ids):

        context = f"Here are some relevant policy documents to query {query}"

        statements = []

        for id in ids:

            statements.append(
                f"\n\n\n[{id.upper()} FILE DATA] : \n\n\n{self.tree.flat_map[id].text}"
            )

        context += "".join(statements)

        return context


# ps = PolicySearchAgent()

# q = "how to start up a project"

# res = await ps.recommend_policies([q])

# print(res[q])
