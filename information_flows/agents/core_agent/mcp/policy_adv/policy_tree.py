uuid_description = {
    "policy_data": "This is the root directory containing all company policy documentation. It serves as the central repository for policies across different organizational levels.",
    "company_level_policy": "Contains policies applicable across the entire company. These documents define general rules and procedures for company-wide governance.",
    "company_processes_policies": "Houses detailed documents outlining internal human resources and project management procedures. This folder focuses on operational efficiency.",
    "human_recources_policy": "Includes human resource policies such as hiring, equipment use, vacation, and bench protocols. These define how HR activities are managed.",
    "bench_policy": "Outlines guidelines for managing employees who are temporarily not assigned to projects. It ensures resource optimization.",
    "equipment_policy": "Details the rules for issuing, using, and returning company equipment. It promotes responsible use of resources.",
    "hiring_policy": "Defines the procedures and criteria for hiring new employees. It includes evaluation, interviews, and onboarding steps.",
    "vacation_policy": "Describes rules for employee vacation time including accrual, approval, and scheduling. Helps maintain workforce planning.",
    "project_management_policy": "Contains policies guiding project execution, planning, and team collaboration. It supports project lifecycle consistency.",
    "project_launch_policy": "Covers steps and approvals required to initiate a project. Ensures readiness before execution begins.",
    "project_rules_policy": "Defines the standard rules and expectations that govern project execution. Promotes uniform practices.",
    "user_story_decomposition_policy": "Explains how to break down features into manageable user stories. Supports agile development.",
    "user_story_estimation_policy": "Provides guidelines for estimating effort and complexity of user stories. Aids sprint planning.",
    "user_story_policy": "Summarizes best practices for writing clear and actionable user stories. Ensures alignment with product goals.",
    "company_rules_policies": "This folder includes general administrative policies like insurance, holidays, and work schedules. It ensures clarity in employee rights.",
    "insurance_policy": "Explains insurance benefits provided by the company and the terms of coverage. Ensures employee awareness.",
    "holidays_policy": "Lists official holidays and rules for observing them. Helps maintain consistency across departments.",
    "payment_policy": "Outlines salary structure, payment frequency, and adjustments. Ensures transparent payroll practices.",
    "time_tracking_policy": "Describes procedures for tracking work hours and logging time. Essential for accountability and reporting.",
    "working_schedule_policy": "Defines expected working hours, flexibility options, and shift rules. Helps regulate work-life balance.",
    "teem_lead_selection_policy": "Formal policy outlining the process of appointing team leads. Ensures leadership quality and fairness.",
    "team_level_policy": "Includes documents that apply to specific teams rather than the entire company. Promotes team-specific governance."
}


import json,os
    
class Node:

    def __init__(
                    self, 
                    is_leaf       = False, 
                    children      = [],
                    text          = None, 
                    description   = None,
                    title         = None
        ):
        
        self.is_leaf        = is_leaf
        self.children       = children
        self.title          = title
        self.description    = description
        self.text           = text
   
    def to_json(self):

        return {

                "is_leaf"       : self.is_leaf,
                "children"      : [child.to_json() for child in self.children] if self.children else [],
                "title"         : self.title,
                "description"   : self.description,
                "text"          : self.text,
        }


class Tree:
    
    def __init__(self, root, flat_map, policies):

        self.root = root

        self.flat_map = flat_map

        self.policies = policies
            

    @classmethod
    def build(cls, folder_path):
        
        flat_map = {}

        policies = []

        def build_node(path, root_path):
            
            title = os.path.basename(path)
            title = title.split("/")[-1]

            if os.path.isdir(path):
                children = [build_node(os.path.join(path, entry), root_path) for entry in sorted(os.listdir(path))]

                flat_map[title.replace(".txt",'')] = Node(children=children, title=title,description=uuid_description[title])
                return flat_map[title]
            else:
                try:
                    with open(path, encoding='utf-8') as f:
                        lines = f.readlines()
                    text = ''.join(lines)
                except Exception as e:
                    text = f"[Error reading file: {e}]"
                
                policies.append(title.replace(".txt",''))
                flat_map[title.replace(".txt",'')] = Node(

                    is_leaf=True,
                    children=[],
                    description=uuid_description[title.replace(".txt",'')],
                    title=title,
                    text=text

                )
                return flat_map[title.replace(".txt",'')]

        root_node = build_node(folder_path, folder_path)
        
        return cls(root_node, flat_map, policies)
    
    def to_json(self):
        def simplify(node):
            data = {
                "title": node.title
            }
            if node.description is not None:
                data["description"] = node.description
            if node.children:
                data["sub-directories"] = [simplify(child) for child in node.children]
            return data

        tree_json = simplify(self.root)
        return json.dumps(tree_json, indent=1, ensure_ascii=False)
    
    @staticmethod
    def print_tree(node, prefix="", is_last=True):
        """Recursively prints the tree structure."""
        connector = "└── " if is_last else "├── "
        print(prefix + connector  + str(node.title))
        prefix += "    " if is_last else "│   "
        for i, child in enumerate(node.children):
            is_last_child = (i == len(node.children) - 1)
            Tree.print_tree(child, prefix, is_last_child)

