import logging

from mcp.server.fastmcp import FastMCP
from opentelemetry.trace import StatusCode

from information_flows.agents.core_agent.mcp.policy_adv.policy_search_agent import (
    PolicySearchAgent,
)
from information_flows.agents.core_agent.mcp.policy_adv.telemetry import tracer
from information_flows.core.telemetry import extract_tracing_context_stdio

logging.basicConfig(level=logging.INFO)

mcp = FastMCP("Policy")


@mcp.tool()
async def get_company_policy(question: str) -> str:
    """Retrieve the company policy documents on the subject from the company's
    policy knowledge base. The tool is a smart document search service that
    accepts queries in natural language and performs a comprehensive search
    through the policy knowledge base. Keep your questions short and simple.

    Example: given a subject "how to start up a project", retrieves policy
    documents about project management guidelines in our company.
    """
    with tracer.start_as_current_span(
        "get_company_policy", context=extract_tracing_context_stdio()
    ) as span:
        try:
            logging.info(f"Using `get_company_policy` tool from `Policy` MCP server")

            agent = PolicySearchAgent()

            logging.info(f"Policy search question:\n{question}")
            span.set_attribute("question", question)

            result = await agent.recommend_policies([question])
            policies = result[question]

            span.set_attribute("result", policies)
            logging.info(f"Policy search result:\n{policies}")
            return policies
        except Exception as exc:
            span.record_exception(exc)
            span.set_status(StatusCode.ERROR)
            logging.error("Failed to retrieve the company's policy", exc_info=True)
            return "The company's policy MCP failed to return a response."


if __name__ == "__main__":
    mcp.run(transport="stdio")
