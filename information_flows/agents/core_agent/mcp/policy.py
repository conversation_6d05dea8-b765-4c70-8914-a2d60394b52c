import logging
from enum import Str<PERSON>num, auto

from mcp.server.fastmcp import FastMCP

logging.basicConfig(level=logging.INFO)


class Topic(StrEnum):
    VACATION = auto()


# fake in-memory policy document store (replace with real remote database)
documents: dict[Topic, str] = {
    Topic.VACATION: "/home/<USER>/information-flows/data/vacation_policy.txt",
}

mcp = FastMCP("Policy")


@mcp.tool()
def get_policy_document(topic: Topic) -> str:
    """Retrieve a document with the company's policy on the given `topic`."""
    logging.info("Using `get_policy_document` tool from `Policy` MCP server")
    with open(documents[topic]) as file:
        return file.read()


if __name__ == "__main__":
    mcp.run(transport="stdio")
