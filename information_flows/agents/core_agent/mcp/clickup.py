import logging
from collections import defaultdict
from typing import NamedTuple

from mcp.server.fastmcp import Fast<PERSON><PERSON>
from pydantic import BaseModel, Field

mcp = FastMCP("ClickUp")


class Date(BaseModel):
    day: int = Field(description="Required format: 1-31")
    month: int = Field(description="Required format: 1-12")
    year: int = Field(description="Required format (4-digit): YYYY")


class VacationRequest(NamedTuple):
    requester_id: str
    assignee_id: str
    from_date: Date
    to_date: Date

    def format(self) -> str:
        return f"Request from {self.requester_id} to {self.assignee_id}: {self.from_date} to {self.to_date}"


# fake in-memory vacation requests registry (replace with real ClickUp API)
requests: defaultdict[str, list[VacationRequest]] = defaultdict(list)


@mcp.tool()
async def clickup_vacation_request(
    requester_id: str,
    assignee_id: str,
    from_date: Date,
    to_date: Date,
):
    """Create a ClickUp vacation form request from an employee requesting a
    vacation with the `requester_id` to a responsible employee with
    `assignee_id`. The ClickUp form collects requests of all employees
    and is a central storage of vacation requests. Each assignee is responsible
    for reviewing requests assigned to them and approve or reejct them. Most of
    the time the assignee is a team lead but it can be another role.

    **MARKS**
    - [DANGEROUS ACTION] The action has drastic and mutating consequences so you
    MUST first ask for human intervention and get explicit confirmation to before
    performing it."""
    logging.info(f"Using `clickup_vacation_request` tool from `ClickUp` MCP server")
    requests[requester_id].append(
        VacationRequest(
            requester_id,
            assignee_id,
            from_date,
            to_date,
        )
    )
    logging.info(
        f"Assigned a vacation request from {requester_id} to {assignee_id}: {from_date} to {to_date}"
    )
    return "Successfully make a ClickUp vacation form request."


@mcp.tool()
async def list_vacation_requests():
    """Return a list of all vacation requests in the ClickUp vacation requests form."""
    logging.info(f"Using `list_vacation_requests` tool from `ClickUp` MCP server")
    aggregated = []
    for per_person in requests.values():
        for request in per_person:
            aggregated.append(request.format())
    return "\n".join(aggregated)


if __name__ == "__main__":
    mcp.run(transport="stdio")
