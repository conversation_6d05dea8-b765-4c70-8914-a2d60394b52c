#%%
import pandas as pd
import numpy as np
from typing import Literal 

from text2pandas import PandasAgent

ag = PandasAgent()

await ag.precompute()

query = "List me all the employees who completed more than one task and took part in more then one project from 2020 year"

answer, dict_result, improvements = ag.execute(query, n_validation_trails = 3, decompose=False, summarize=False, validate = True)

# answer -  textual answer to the query after summarization

# dict_result - json answer to the query before summarization

# improvements - history of validation attempts 


# Using decompose parameter we can specify if we want to decompose query before translating it to pandas - OPTIONAL

# Using summarize parameter we can specify if we want to summarize dict_result before sharing it to other agent - OPTIONAL

    # need it for information richness of result in case json is not informative enough.

# Using validate parameter we can specify if we want to post-validate the pandas before getting the final results - OBLIGATORY FOR STABILITY

    # Using n_trails parameter we can specify number of trails the validation pipeline have to correct the errors.

    # Hypothetically, it is possible that script is large and requires 5 trails at least, but it is rare case. 3 trails is enough mostly. 

    # The best accuracy can be achieved if number of trails if inf but it will cause huge latency and cost.

    # We can minimize cost and latency by assigning different models to different tasks.

    # For example, code generation if managed by gpt-4o-mini and validation id by more clever model like gpt-4o.

# As for me, it is a good idea to make a possibility for the agent to configure that tool based on the request complexity.
#%%
print(dict_result)