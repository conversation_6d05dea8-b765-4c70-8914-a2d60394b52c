import os
from typing import Dict

import dotenv
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

from information_flows.agents.core_agent.mcp.text2pandas.models import Answer
from information_flows.agents.core_agent.mcp.text2pandas.prompts import (
    prompt_template_summarize,
)

dotenv.load_dotenv()


class Summarizer:

    prompt_template_summarize = prompt_template_summarize

    def __init__(self):

        self.llm = ChatOpenAI(
            model=os.getenv("SQL_MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    def summarize_result(self, data: Dict[str, str]) -> dict[str, str]:

        model = self.llm.with_structured_output(Answer)

        prompt = PromptTemplate(
            template=self.prompt_template_summarize,
            input_variables=["user_query", "dict_result"],
        )

        chain = prompt | model

        args = {"user_query": data["query"], "dict_result": data["dict_result"]}

        result = chain.invoke(args)

        return result.answer
