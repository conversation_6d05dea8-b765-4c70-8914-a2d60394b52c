--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Homebrew)
-- Dumped by pg_dump version 14.17 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: employees; Type: TABLE DATA; Schema: public; Owner: avanheim
--

SET SESSION AUTHORIZATION DEFAULT;

-- ALTER TABLE public.employees DISABLE TRIGGER ALL;

-- dl-team
INSERT INTO public.employees (id, first_name, last_name, corporative_code, hired_date, role, leader_id, manager_id, accrued_vacation_days)
VALUES
(1, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ie<PERSON>', 'tyd', '2020-01-10', 'TEAM_LEAD', NULL, NULL, 15),
(2, '<PERSON><PERSON>', 'Orlov', 'odv', '2020-02-12', 'ML_ENGINEER', 1, 1, 12),
(3, '<PERSON>', '<PERSON>chan', 'sk', '2020-03-05', 'ML_ENG<PERSON>EER', 1, 1, 10),
(4, '<PERSON><PERSON>', '<PERSON>hmura', 'vzh', '2020-01-18', 'PROJECT_<PERSON><PERSON><PERSON>R', 1, 1, 8),
(5, 'Sylvestr', 'Semeshko', 'ssy', '2020-04-22', 'ML_ENGINEER', 1, 1, 10),
(6, 'Vladyslav', 'Churkin', 'vlc', '2020-03-14', 'FRONTEND_DEV', 1, 1, 9),
(7, 'Evelina', 'Aleksiutenko', 'ale', '2020-05-01', 'ML_ENGINEER', 1, 1, 11),
(8, 'Yermukhamet', 'Medetov', 'erm', '2020-06-07', 'ML_ENGINEER', 1, 1, 10),
(9, 'Heorhii', 'Zaborei', 'zah', '2020-07-19', 'ML_ENGINEER', 1, 1, 10),

-- pink-goose-team
(10, 'Artem', 'Strzhyhotsky', 'asr', '2020-01-10', 'TEAM_LEAD', NULL, 12, 15),
(11, 'Vasil', 'Talkachou', 'vt', '2020-02-10', 'FULL_STACK_DEV', 10, 12, 12),
(12, 'Olga', 'Maslionak', 'onm', '2020-02-15', 'PROJECT_MANAGER', 10, NULL, 11),
(13, 'Evgeniy', 'Khmelnikov', 'ekh', '2020-03-01', 'FULL_STACK_DEV', 10, 12, 10),
(14, 'Kristina', 'Kurhanska', 'krk', '2020-03-21', 'PYTHON_DEV', 10, 12, 9),
(15, 'Anton', 'Zakharov', 'zaa', '2020-04-12', 'FULL_STACK_DEV', 10, 12, 10);


-- ALTER TABLE public.employees ENABLE TRIGGER ALL;

-- Insert into teams table
-- Insert teams with explicit IDs
INSERT INTO public.teams (id, name, leader_id, manager_id) VALUES
(1, 'dl-team', 1, 4),
(2, 'pink-goose-team', 10, 12);

-- ALTER TABLE public.teams ENABLE TRIGGER ALL;

-- DL team (team_id = 1)
INSERT INTO public.teams_membership (id, team_id, member_id) VALUES
(1, 1, 1),   -- Dmytro Tymofiiev
(2, 1, 2),   -- Danila Orlov
(3, 1, 3),   -- Sergey Kachan
(4, 1, 4),   -- Valentina Zhmura
(5, 1, 5),   -- Sylvestr Semeshko
(6, 1, 6),   -- Vladyslav Churkin
(7, 1, 7),   -- Evelina Aleksiutenko
(8, 1, 8),   -- Yermukhamet Medetov
(9, 1, 9);   -- Heorhii Zaborei

-- Pink Goose team (team_id = 2)
INSERT INTO public.teams_membership (id, team_id, member_id) VALUES
(10, 2, 10), -- Artem Strzhyhotsky
(11, 2, 11), -- Vasil Talkachou
(12, 2, 12), -- Olga Maslionak
(13, 2, 13), -- Evgeniy Khmelnikov
(14, 2, 14), -- Kristina Kurhanska
(15, 2, 15); -- Anton Zakharov


-- ALTER TABLE public.teams_membership ENABLE TRIGGER ALL;

--
-- Data for Name: vacation_requests; Type: TABLE DATA; Schema: public; Owner: avanheim
--

-- ALTER TABLE public.vacation_requests DISABLE TRIGGER ALL;

INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (1, 'PAID', '2025-04-20', '2025-04-26', '2025-05-21 01:13:38.514707+02', 'PENDING', 7, 'Anyone themselves fine month real challenge.', 2);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (2, 'UNPAID', '2023-09-08', '2023-09-13', '2025-05-21 01:13:38.51636+02', 'PENDING', 6, 'One community push response change energy produce interest campaign development budget if.', 2);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (3, 'UNPAID', '2023-11-23', '2023-11-26', '2025-05-21 01:13:38.517313+02', 'PENDING', 4, 'Big eat campaign peace career official college technology student consider themselves size.', 3);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (4, 'PAID', '2024-06-16', '2024-07-04', '2025-05-21 01:13:38.51799+02', 'PENDING', 19, 'Blood miss street risk resource ball arm question receive carry save guy.', 3);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (5, 'PAID', '2024-02-20', '2024-02-26', '2025-05-21 01:13:38.518681+02', 'APPROVED', 7, 'Measure group authority change old then pressure participant.', 4);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (6, 'UNPAID', '2025-04-14', '2025-04-22', '2025-05-21 01:13:38.519371+02', 'APPROVED', 9, 'Join international plan simply someone back stage throw performance already ball Mrs.', 4);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (7, 'PAID', '2024-06-12', '2024-06-19', '2025-05-21 01:13:38.520235+02', 'PENDING', 8, 'Blue similar food happen free analysis alone worker fly whatever film really some begin.', 5);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (8, 'UNPAID', '2024-03-05', '2024-03-06', '2025-05-21 01:13:38.520836+02', 'PENDING', 2, 'Present visit there film bring myself.', 6);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (9, 'UNPAID', '2024-05-14', '2024-05-16', '2025-05-21 01:13:38.521589+02', 'APPROVED', 3, 'Year successful ok factor town evening ask enjoy however.', 7);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (10, 'UNPAID', '2023-06-21', '2023-06-22', '2025-05-21 01:13:38.522357+02', 'PENDING', 2, 'Wear college participant control mother tend.', 7);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (11, 'UNPAID', '2024-09-15', '2024-09-17', '2025-05-21 01:13:38.52322+02', 'APPROVED', 3, 'Important expect former Congress child whatever.', 10);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (12, 'PAID', '2025-03-18', '2025-03-19', '2025-05-21 01:13:38.524774+02', 'PENDING', 2, 'Professor will medical relationship stuff capital animal rise security south open later the.', 11);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (13, 'UNPAID', '2023-11-04', '2023-11-08', '2025-05-21 01:13:38.525687+02', 'PENDING', 5, 'Manager building open first political decide near style base or.', 11);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (14, 'PAID', '2022-10-07', '2022-10-11', '2025-05-21 01:13:38.526535+02', 'PENDING', 5, 'Us generation fund central never nothing lead recognize purpose.', 13);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (15, 'PAID', '2023-02-21', '2023-02-28', '2025-05-21 01:13:38.527342+02', 'APPROVED', 8, 'Wear sport force cell if work white dinner election sea spring audience pass.', 13);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (16, 'UNPAID', '2024-10-21', '2024-10-23', '2025-05-21 01:13:38.528555+02', 'APPROVED', 3, 'Save memory step course ago fire sea box into sister pattern.', 13);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (17, 'UNPAID', '2024-09-11', '2024-09-15', '2025-05-21 01:13:38.529261+02', 'REJECTED', 5, 'Rise coach beat nothing with condition under.', 14);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (18, 'PAID', '2024-07-25', '2024-07-27', '2025-05-21 01:13:38.529906+02', 'PENDING', 3, 'Money theory Mr challenge carry soldier toward art development form sea.', 15);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (19, 'UNPAID', '2024-09-04', '2024-09-13', '2025-05-21 01:13:38.530499+02', 'APPROVED', 10, 'Machine option pass wife whom statement adult fact write tough keep view.', 15);
INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (20, 'PAID', '2024-11-13', '2024-11-13', '2025-05-21 01:13:38.531084+02', 'APPROVED', 1, 'Cause manage safe could ever probably century they evidence effect.', 15);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (21, 'UNPAID', '2024-03-03', '2024-03-10', '2025-05-21 01:13:38.531767+02', 'APPROVED', 8, 'Air concern important body half alone lot minute thousand recently law.', 16);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (22, 'PAID', '2024-08-29', '2024-09-06', '2025-05-21 01:13:38.532412+02', 'REJECTED', 9, 'Available very until record sing land less deep else.', 16);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (23, 'UNPAID', '2022-08-09', '2022-08-15', '2025-05-21 01:13:38.53305+02', 'PENDING', 7, 'Team several piece suddenly just watch reason off stage bring.', 16);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (24, 'UNPAID', '2024-10-16', '2024-10-24', '2025-05-21 01:13:38.53371+02', 'APPROVED', 9, 'Exist himself short couple opportunity simple plant minute sister she.', 17);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (25, 'UNPAID', '2023-07-15', '2023-07-17', '2025-05-21 01:13:38.534384+02', 'REJECTED', 3, 'Candidate two keep line change same career act assume old live.', 17);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (26, 'UNPAID', '2023-09-01', '2023-09-02', '2025-05-21 01:13:38.535031+02', 'APPROVED', 2, 'Executive have must economy option reach technology shoulder dog to or tonight evening.', 17);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (27, 'PAID', '2022-11-10', '2022-11-12', '2025-05-21 01:13:38.535736+02', 'PENDING', 3, 'Win with explain probably outside forget.', 18);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (28, 'PAID', '2024-06-10', '2024-06-16', '2025-05-21 01:13:38.536374+02', 'APPROVED', 7, 'Surface enjoy wind be watch miss create.', 20);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (29, 'UNPAID', '2024-12-01', '2024-12-02', '2025-05-21 01:13:38.536999+02', 'REJECTED', 2, 'Lose would gas front after cost support certainly bill figure.', 20);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (30, 'UNPAID', '2024-06-16', '2024-06-20', '2025-05-21 01:13:38.537665+02', 'APPROVED', 5, 'And nice north child although yes environmental.', 21);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (31, 'UNPAID', '2023-12-30', '2024-01-04', '2025-05-21 01:13:38.53819+02', 'APPROVED', 6, 'Door close money able write kind reality perform amount popular personal.', 21);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (32, 'PAID', '2024-06-04', '2024-06-11', '2025-05-21 01:13:38.538686+02', 'APPROVED', 8, 'Million population accept reveal stuff meeting wind decade international citizen up.', 22);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (33, 'UNPAID', '2025-04-02', '2025-04-03', '2025-05-21 01:13:38.539186+02', 'REJECTED', 2, 'On process south employee about large.', 22);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (34, 'PAID', '2025-04-09', '2025-04-10', '2025-05-21 01:13:38.53965+02', 'APPROVED', 2, 'Reduce significant business could soldier language this surface society reveal painting ball either.', 23);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (35, 'UNPAID', '2024-10-25', '2024-11-03', '2025-05-21 01:13:38.540133+02', 'PENDING', 10, 'Help thing think onto store stop subject give break offer.', 24);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (36, 'UNPAID', '2024-06-21', '2024-06-24', '2025-05-21 01:13:38.540589+02', 'REJECTED', 4, 'Not marriage do wait kid I leader laugh arrive western.', 24);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (37, 'PAID', '2024-07-19', '2024-07-22', '2025-05-21 01:13:38.541051+02', 'REJECTED', 4, 'Everyone far move letter land weight sure region final like.', 24);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (38, 'PAID', '2024-10-13', '2024-10-19', '2025-05-21 01:13:38.54154+02', 'APPROVED', 7, 'Computer city serve newspaper need when yard.', 25);
-- INSERT INTO public.vacation_requests (id, vacation_type, start_date, end_date, requested_at, status, calculated_days, note, employee_id) VALUES (39, 'UNPAID', '2024-04-26', '2024-04-26', '2025-05-21 01:13:38.541995+02', 'REJECTED', 1, 'Production my unit particularly standard Congress success few.', 25);


-- ALTER TABLE public.vacation_requests ENABLE TRIGGER ALL;

--
-- Data for Name: approvals; Type: TABLE DATA; Schema: public; Owner: avanheim
--

-- ALTER TABLE public.approvals DISABLE TRIGGER ALL;

INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES
(1, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.515707+02', 1, 1),
(2, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.516927+02', 1, 2),
(3, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.517678+02', 1, 3),
(4, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.518432+02', 1, 4),
(5, 'PROJECT_MANAGER', true, 'Positive city forget challenge decade memory left across effect fill feel generation.', '2025-05-21 01:13:38.519099+02', 4, 5),
(6, 'PROJECT_MANAGER', true, 'Enter make happen reach scientist air care arm have painting.', '2025-05-21 01:13:38.519871+02', 4, 6),
(7, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.520587+02', 4, 7),
(8, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.521138+02', 4, 8),
(9, 'PROJECT_MANAGER', true, 'Purpose life debate notice difference wait alone.', '2025-05-21 01:13:38.521871+02', 4, 9),
(10, 'TEAM_LEAD', true, 'Purpose life debate notice difference wait alone.', '2025-05-21 01:13:38.521871+02', 1, 10),

(11, 'TEAM_LEAD', true, 'Professor husband themselves often guy tend important determine may fast mean force magazine both.', '2025-05-21 01:13:38.522128+02', 10, 11),
(12, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.522608+02', 12, 12),
(13, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.522915+02', 10, 13),
(14, 'PROJECT_MANAGER', true, 'Customer thing board town all whom.', '2025-05-21 01:13:38.523785+02', 12, 14),
(15, 'TEAM_LEAD', true, 'Off would throughout discussion nearly you fact skin enjoy best against audience address.', '2025-05-21 01:13:38.524457+02', 10, 15),
(16, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.525157+02', 12, 16),
(17, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.525416+02', 10, 17),
(18, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.526051+02', 12, 18),
(19, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.526282+02', 10, 19),
(20, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.526876+02', 12, 20);

-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (34, 'PROJECT_MANAGER', true, 'Serious despite day language personal discover middle month time.', '2025-05-21 01:13:38.532021+02', 1, 21);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (35, 'TEAM_LEAD', true, 'Community yeah door energy spring forward capital wait.', '2025-05-21 01:13:38.532221+02', 5, 21);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (36, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.532647+02', 1, 22);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (37, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.532853+02', 5, 22);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (38, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.533286+02', 1, 23);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (39, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.533509+02', 5, 23);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (40, 'PROJECT_MANAGER', true, 'Anyone lot there mission all job add suffer capital.', '2025-05-21 01:13:38.533965+02', 1, 24);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (41, 'TEAM_LEAD', true, 'Hard civil really training sometimes loss.', '2025-05-21 01:13:38.534189+02', 4, 24);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (42, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.534633+02', 1, 25);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (43, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.534827+02', 4, 25);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (44, 'PROJECT_MANAGER', true, 'Relationship try blood the fact this.', '2025-05-21 01:13:38.53531+02', 1, 26);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (45, 'TEAM_LEAD', true, 'Together all which candidate affect rather long.', '2025-05-21 01:13:38.535511+02', 4, 26);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (46, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.535976+02', 2, 27);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (47, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.536177+02', 5, 27);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (48, 'PROJECT_MANAGER', true, 'Course real bar person both play peace interest personal receive kid must house.', '2025-05-21 01:13:38.536611+02', 2, 28);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (49, 'TEAM_LEAD', true, 'Break across movement indeed thousand require result act end his physical be.', '2025-05-21 01:13:38.536808+02', 4, 28);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (50, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.537234+02', 2, 29);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (51, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.537415+02', 4, 29);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (52, 'PROJECT_MANAGER', true, 'Hope less happen now back able go brother artist receive idea during culture.', '2025-05-21 01:13:38.537865+02', 2, 30);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (53, 'TEAM_LEAD', true, 'Now assume down experience suddenly door book customer nature draw.', '2025-05-21 01:13:38.538029+02', 5, 30);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (54, 'PROJECT_MANAGER', true, 'Partner change simple until interest stage federal until.', '2025-05-21 01:13:38.538365+02', 2, 31);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (55, 'TEAM_LEAD', true, 'True approach in special music performance.', '2025-05-21 01:13:38.538522+02', 5, 31);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (56, 'PROJECT_MANAGER', true, 'Together network particularly court record all information.', '2025-05-21 01:13:38.538863+02', 2, 32);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (57, 'TEAM_LEAD', true, 'Budget after to follow type also my present long administration thousand spring action.', '2025-05-21 01:13:38.539023+02', 5, 32);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (58, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.539344+02', 2, 33);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (59, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.539489+02', 5, 33);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (60, 'PROJECT_MANAGER', true, 'Notice board mouth media account notice.', '2025-05-21 01:13:38.539823+02', 1, 34);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (61, 'TEAM_LEAD', true, 'Including whatever peace wife that behind floor nor brother position strategy.', '2025-05-21 01:13:38.539978+02', 5, 34);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (62, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.540291+02', 2, 35);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (63, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.540434+02', 5, 35);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (64, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.54075+02', 2, 36);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (65, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.540891+02', 5, 36);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (66, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.541213+02', 2, 37);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (67, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.541357+02', 5, 37);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (68, 'PROJECT_MANAGER', true, 'Certain pressure bad baby others arm account camera scene down hospital.', '2025-05-21 01:13:38.541724+02', 1, 38);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (69, 'TEAM_LEAD', true, 'West just although purpose expert ready.', '2025-05-21 01:13:38.541864+02', 4, 38);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (70, 'PROJECT_MANAGER', false, NULL, '2025-05-21 01:13:38.54213+02', 1, 39);
-- INSERT INTO public.approvals (id, role, approved, comment, acted_at, approver_id, vacation_request_id) VALUES (71, 'TEAM_LEAD', false, NULL, '2025-05-21 01:13:38.542243+02', 4, 39);


-- ALTER TABLE public.approvals ENABLE TRIGGER ALL;

--
-- Data for Name: projects; Type: TABLE DATA; Schema: public; Owner: avanheim
--                                                                                                              -- 1,10 - lead 4,12 = PM


INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, pm_id) VALUES (1, 'Nordic Capital', '2020-05-31', '2027-02-19', 'ACTIVE', 1, 12); -- 2,3,5,6 1,12
INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, pm_id) VALUES (2, 'Brandfy', '2020-10-28', '2028-10-22', 'ACTIVE',10 , 12); --7,8,9,15 10,12
INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, pm_id) VALUES (3, 'Precise VetPro', '2020-05-31', '2029-01-20', 'ACTIVE', 1, 4); -- 11 1,4
INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, pm_id) VALUES (4, 'Igloo', '2020-12-21', '2026-02-14', 'ACTIVE', 10, 4); -- 13 10,4
INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, pm_id) VALUES (5, 'Neptune dashboard', '2020-12-21', '2027-02-14', 'ACTIVE', 10, 4); -- 14 10,4
-- INSERT INTO public.projects (id, name, start_date, deadline, status, lead_id, PROJECT_MANAGER_id) VALUES (6, 'Bench', '2023-12-21', '2027-02-14', 'ACTIVE', 1, 12);  -- 1,4,10,12 -? leads are always out of the project?
-- ALTER TABLE public.projects ENABLE TRIGGER ALL;

--
-- Data for Name: project_assignments; Type: TABLE DATA; Schema: public; Owner: avanheim
--

-- ALTER TABLE public.project_assignments DISABLE TRIGGER ALL;

INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (1, '2025-05-20', 2, 1);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (2, '2025-05-20', 3, 1);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (3, '2025-05-20', 5, 1);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (4, '2025-05-20', 6, 1);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (5, '2025-05-20', 1, 1);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (6, '2025-05-20', 12, 1);

INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (7, '2025-05-20', 7, 2);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (8, '2025-05-20', 8, 2);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (9, '2025-05-20', 9, 2);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (10, '2025-05-20', 15, 2);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (11, '2025-05-20', 10, 2);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (12, '2025-05-20', 12, 2);

INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (13, '2025-05-20', 11, 3);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (14, '2025-05-20', 1, 3);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (15, '2025-05-20', 4, 3);

INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (16, '2025-05-20', 13, 4);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (17, '2025-05-20', 10, 4);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (18, '2025-05-20', 4, 4);

INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (19, '2025-05-20', 14, 5);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (20, '2025-05-20', 10, 5);
INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (21, '2025-05-20', 4, 5);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (22, '2025-05-20', 20, 2);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (25, '2025-05-20', 22, 4);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (26, '2025-05-20', 23, 3);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (27, '2025-05-20', 24, 2);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (28, '2025-05-20', 24, 4);
-- INSERT INTO public.project_assignments (id, assigned_at, employee_id, project_id) VALUES (29, '2025-05-20', 25, 4);


-- ALTER TABLE public.project_assignments ENABLE TRIGGER ALL;

--
-- Data for Name: tasks; Type: TABLE DATA; Schema: public; Owner: avanheim
--

-- ALTER TABLE public.tasks DISABLE TRIGGER ALL;

INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (1, 'Task Title 1', 'Description for task 1', 'TODO', 'HIGH', '2023-01-01', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (2, 'Task Title 2', 'Description for task 2', 'DONE', 'LOW', '2023-01-02', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (3, 'Task Title 3', 'Description for task 3', 'IN_PROGRESS', 'MEDIUM', '2023-01-03', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (4, 'Task Title 4', 'Description for task 4', 'TODO', 'HIGH', '2023-01-04', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (5, 'Task Title 5', 'Description for task 5', 'DONE', 'LOW', '2023-01-05', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (6, 'Task Title 6', 'Description for task 6', 'DONE', 'HIGH', '2023-01-06', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (7, 'Task Title 7', 'Description for task 7', 'DONE', 'LOW', '2023-01-07', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (8, 'Task Title 8', 'Description for task 8', 'DONE', 'MEDIUM', '2023-01-08', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (9, 'Task Title 9', 'Description for task 9', 'IN_PROGRESS', 'HIGH', '2023-01-09', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (10, 'Task Title 10', 'Description for task 10', 'DONE', 'HIGH', '2023-01-10', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (11, 'Task Title 11', 'Description for task 11', 'IN_PROGRESS', 'MEDIUM', '2023-01-11', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (12, 'Task Title 12', 'Description for task 12', 'BLOCKED', 'LOW', '2023-01-12', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (13, 'Task Title 13', 'Description for task 13', 'TODO', 'MEDIUM', '2023-01-13', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (14, 'Task Title 14', 'Description for task 14', 'DONE', 'MEDIUM', '2023-01-14', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (15, 'Task Title 15', 'Description for task 15', 'BLOCKED', 'LOW', '2023-01-15', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (16, 'Task Title 16', 'Description for task 16', 'TODO', 'HIGH', '2023-01-16', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (17, 'Task Title 17', 'Description for task 17', 'TODO', 'HIGH', '2023-01-17', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (18, 'Task Title 18', 'Description for task 18', 'TODO', 'HIGH', '2023-01-18', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (19, 'Task Title 19', 'Description for task 19', 'BLOCKED', 'HIGH', '2023-01-19', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (20, 'Task Title 20', 'Description for task 20', 'IN_PROGRESS', 'MEDIUM', '2023-01-20', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (21, 'Task Title 21', 'Description for task 21', 'BLOCKED', 'HIGH', '2023-01-21', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (22, 'Task Title 22', 'Description for task 22', 'DONE', 'HIGH', '2023-01-22', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (23, 'Task Title 23', 'Description for task 23', 'DONE', 'HIGH', '2023-01-23', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (24, 'Task Title 24', 'Description for task 24', 'BLOCKED', 'HIGH', '2023-01-24', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (25, 'Task Title 25', 'Description for task 25', 'TODO', 'HIGH', '2023-01-25', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (26, 'Task Title 26', 'Description for task 26', 'IN_PROGRESS', 'LOW', '2023-01-26', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (27, 'Task Title 27', 'Description for task 27', 'IN_PROGRESS', 'MEDIUM', '2023-01-27', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (28, 'Task Title 28', 'Description for task 28', 'IN_PROGRESS', 'HIGH', '2023-01-28', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (29, 'Task Title 29', 'Description for task 29', 'IN_PROGRESS', 'HIGH', '2023-01-29', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (30, 'Task Title 30', 'Description for task 30', 'TODO', 'MEDIUM', '2023-01-30', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (31, 'Task Title 31', 'Description for task 31', 'IN_PROGRESS', 'HIGH', '2023-01-31', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (32, 'Task Title 32', 'Description for task 32', 'TODO', 'MEDIUM', '2023-02-01', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (33, 'Task Title 33', 'Description for task 33', 'DONE', 'LOW', '2023-02-02', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (34, 'Task Title 34', 'Description for task 34', 'TODO', 'HIGH', '2023-02-03', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (35, 'Task Title 35', 'Description for task 35', 'DONE', 'HIGH', '2023-02-04', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (36, 'Task Title 36', 'Description for task 36', 'IN_PROGRESS', 'LOW', '2023-02-05', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (37, 'Task Title 37', 'Description for task 37', 'BLOCKED', 'HIGH', '2023-02-06', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (38, 'Task Title 38', 'Description for task 38', 'DONE', 'LOW', '2023-02-07', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (39, 'Task Title 39', 'Description for task 39', 'DONE', 'LOW', '2023-02-08', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (40, 'Task Title 40', 'Description for task 40', 'TODO', 'HIGH', '2023-02-09', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (41, 'Task Title 41', 'Description for task 41', 'BLOCKED', 'LOW', '2023-02-10', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (42, 'Task Title 42', 'Description for task 42', 'DONE', 'HIGH', '2023-02-11', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (43, 'Task Title 43', 'Description for task 43', 'TODO', 'HIGH', '2023-02-12', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (44, 'Task Title 44', 'Description for task 44', 'IN_PROGRESS', 'HIGH', '2023-02-13', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (45, 'Task Title 45', 'Description for task 45', 'DONE', 'LOW', '2023-02-14', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (46, 'Task Title 46', 'Description for task 46', 'IN_PROGRESS', 'LOW', '2023-02-15', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (47, 'Task Title 47', 'Description for task 47', 'DONE', 'LOW', '2023-02-16', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (48, 'Task Title 48', 'Description for task 48', 'DONE', 'MEDIUM', '2023-02-17', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (49, 'Task Title 49', 'Description for task 49', 'BLOCKED', 'HIGH', '2023-02-18', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (50, 'Task Title 50', 'Description for task 50', 'DONE', 'LOW', '2023-02-19', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 1);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (51, 'Task Title 51', 'Description for task 51', 'TODO', 'MEDIUM', '2023-02-20', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (52, 'Task Title 52', 'Description for task 52', 'IN_PROGRESS', 'HIGH', '2023-02-21', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (53, 'Task Title 53', 'Description for task 53', 'IN_PROGRESS', 'HIGH', '2023-02-22', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (54, 'Task Title 54', 'Description for task 54', 'IN_PROGRESS', 'HIGH', '2023-02-23', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (55, 'Task Title 55', 'Description for task 55', 'DONE', 'MEDIUM', '2023-02-24', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (56, 'Task Title 56', 'Description for task 56', 'TODO', 'LOW', '2023-02-25', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (57, 'Task Title 57', 'Description for task 57', 'DONE', 'LOW', '2023-02-26', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (58, 'Task Title 58', 'Description for task 58', 'BLOCKED', 'LOW', '2023-02-27', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (59, 'Task Title 59', 'Description for task 59', 'TODO', 'LOW', '2023-02-28', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (60, 'Task Title 60', 'Description for task 60', 'DONE', 'HIGH', '2023-03-01', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (61, 'Task Title 61', 'Description for task 61', 'DONE', 'HIGH', '2023-03-02', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (62, 'Task Title 62', 'Description for task 62', 'BLOCKED', 'LOW', '2023-03-03', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (63, 'Task Title 63', 'Description for task 63', 'DONE', 'LOW', '2023-03-04', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (64, 'Task Title 64', 'Description for task 64', 'BLOCKED', 'LOW', '2023-03-05', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (65, 'Task Title 65', 'Description for task 65', 'DONE', 'MEDIUM', '2023-03-06', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (66, 'Task Title 66', 'Description for task 66', 'TODO', 'HIGH', '2023-03-07', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (67, 'Task Title 67', 'Description for task 67', 'BLOCKED', 'LOW', '2023-03-08', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (68, 'Task Title 68', 'Description for task 68', 'TODO', 'LOW', '2023-03-09', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (69, 'Task Title 69', 'Description for task 69', 'TODO', 'MEDIUM', '2023-03-10', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (70, 'Task Title 70', 'Description for task 70', 'TODO', 'LOW', '2023-03-11', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (71, 'Task Title 71', 'Description for task 71', 'IN_PROGRESS', 'HIGH', '2023-03-12', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (72, 'Task Title 72', 'Description for task 72', 'IN_PROGRESS', 'MEDIUM', '2023-03-13', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (73, 'Task Title 73', 'Description for task 73', 'BLOCKED', 'MEDIUM', '2023-03-14', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (74, 'Task Title 74', 'Description for task 74', 'TODO', 'MEDIUM', '2023-03-15', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (75, 'Task Title 75', 'Description for task 75', 'TODO', 'LOW', '2023-03-16', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (76, 'Task Title 76', 'Description for task 76', 'IN_PROGRESS', 'LOW', '2023-03-17', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (77, 'Task Title 77', 'Description for task 77', 'DONE', 'LOW', '2023-03-18', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (78, 'Task Title 78', 'Description for task 78', 'TODO', 'MEDIUM', '2023-03-19', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (79, 'Task Title 79', 'Description for task 79', 'IN_PROGRESS', 'MEDIUM', '2023-03-20', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (80, 'Task Title 80', 'Description for task 80', 'BLOCKED', 'HIGH', '2023-03-21', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 2);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (81, 'Task Title 81', 'Description for task 81', 'BLOCKED', 'MEDIUM', '2023-03-22', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (82, 'Task Title 82', 'Description for task 82', 'DONE', 'LOW', '2023-03-23', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (83, 'Task Title 83', 'Description for task 83', 'TODO', 'LOW', '2023-03-24', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (84, 'Task Title 84', 'Description for task 84', 'IN_PROGRESS', 'MEDIUM', '2023-03-25', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (85, 'Task Title 85', 'Description for task 85', 'IN_PROGRESS', 'HIGH', '2023-03-26', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (86, 'Task Title 86', 'Description for task 86', 'DONE', 'MEDIUM', '2023-03-27', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (87, 'Task Title 87', 'Description for task 87', 'IN_PROGRESS', 'MEDIUM', '2023-03-28', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (88, 'Task Title 88', 'Description for task 88', 'IN_PROGRESS', 'MEDIUM', '2023-03-29', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (89, 'Task Title 89', 'Description for task 89', 'DONE', 'LOW', '2023-03-30', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (90, 'Task Title 90', 'Description for task 90', 'BLOCKED', 'LOW', '2023-03-31', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (91, 'Task Title 91', 'Description for task 91', 'DONE', 'LOW', '2023-04-01', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (92, 'Task Title 92', 'Description for task 92', 'DONE', 'MEDIUM', '2023-04-02', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (93, 'Task Title 93', 'Description for task 93', 'BLOCKED', 'HIGH', '2023-04-03', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (94, 'Task Title 94', 'Description for task 94', 'BLOCKED', 'MEDIUM', '2023-04-04', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (95, 'Task Title 95', 'Description for task 95', 'DONE', 'MEDIUM', '2023-04-05', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (96, 'Task Title 96', 'Description for task 96', 'TODO', 'LOW', '2023-04-06', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (97, 'Task Title 97', 'Description for task 97', 'BLOCKED', 'LOW', '2023-04-07', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (98, 'Task Title 98', 'Description for task 98', 'DONE', 'MEDIUM', '2023-04-08', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (99, 'Task Title 99', 'Description for task 99', 'IN_PROGRESS', 'HIGH', '2023-04-09', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (100, 'Task Title 100', 'Description for task 100', 'TODO', 'LOW', '2023-04-10', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 1, 3);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (101, 'Task Title 101', 'Description for task 101', 'IN_PROGRESS', 'HIGH', '2023-04-11', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (102, 'Task Title 102', 'Description for task 102', 'BLOCKED', 'HIGH', '2023-04-12', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (103, 'Task Title 103', 'Description for task 103', 'TODO', 'MEDIUM', '2023-04-13', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (104, 'Task Title 104', 'Description for task 104', 'DONE', 'MEDIUM', '2023-04-14', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (105, 'Task Title 105', 'Description for task 105', 'TODO', 'MEDIUM', '2023-04-15', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (106, 'Task Title 106', 'Description for task 106', 'BLOCKED', 'HIGH', '2023-04-16', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (107, 'Task Title 107', 'Description for task 107', 'TODO', 'LOW', '2023-04-17', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (108, 'Task Title 108', 'Description for task 108', 'DONE', 'LOW', '2023-04-18', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (109, 'Task Title 109', 'Description for task 109', 'DONE', 'MEDIUM', '2023-04-19', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (110, 'Task Title 110', 'Description for task 110', 'TODO', 'LOW', '2023-04-20', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (111, 'Task Title 111', 'Description for task 111', 'DONE', 'HIGH', '2023-04-21', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (112, 'Task Title 112', 'Description for task 112', 'TODO', 'HIGH', '2023-04-22', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (113, 'Task Title 113', 'Description for task 113', 'TODO', 'LOW', '2023-04-23', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (114, 'Task Title 114', 'Description for task 114', 'TODO', 'HIGH', '2023-04-24', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (115, 'Task Title 115', 'Description for task 115', 'TODO', 'HIGH', '2023-04-25', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (116, 'Task Title 116', 'Description for task 116', 'BLOCKED', 'HIGH', '2023-04-26', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (117, 'Task Title 117', 'Description for task 117', 'DONE', 'MEDIUM', '2023-04-27', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (118, 'Task Title 118', 'Description for task 118', 'TODO', 'MEDIUM', '2023-04-28', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (119, 'Task Title 119', 'Description for task 119', 'DONE', 'LOW', '2023-04-29', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (120, 'Task Title 120', 'Description for task 120', 'BLOCKED', 'LOW', '2023-04-30', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 4);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (121, 'Task Title 121', 'Description for task 121', 'DONE', 'LOW', '2023-05-01', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (122, 'Task Title 122', 'Description for task 122', 'DONE', 'HIGH', '2023-05-02', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (123, 'Task Title 123', 'Description for task 123', 'IN_PROGRESS', 'HIGH', '2023-05-03', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (124, 'Task Title 124', 'Description for task 124', 'IN_PROGRESS', 'MEDIUM', '2023-05-04', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (125, 'Task Title 125', 'Description for task 125', 'IN_PROGRESS', 'MEDIUM', '2023-05-05', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (126, 'Task Title 126', 'Description for task 126', 'TODO', 'LOW', '2023-05-06', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (127, 'Task Title 127', 'Description for task 127', 'IN_PROGRESS', 'LOW', '2023-05-07', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (128, 'Task Title 128', 'Description for task 128', 'IN_PROGRESS', 'HIGH', '2023-05-08', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (129, 'Task Title 129', 'Description for task 129', 'IN_PROGRESS', 'MEDIUM', '2023-05-09', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (130, 'Task Title 130', 'Description for task 130', 'DONE', 'MEDIUM', '2023-05-10', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (131, 'Task Title 131', 'Description for task 131', 'TODO', 'HIGH', '2023-05-11', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (132, 'Task Title 132', 'Description for task 132', 'DONE', 'LOW', '2023-05-12', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);
INSERT INTO public.tasks (id, title, description, status, priority, due_date, created_at, updated_at, assigned_to_id, project_id) VALUES (133, 'Task Title 133', 'Description for task 133', 'IN_PROGRESS', 'MEDIUM', '2023-05-13', '2025-05-28 11:01:01', '2025-05-28 11:01:01', 10, 5);


-- ALTER TABLE public.tasks ENABLE TRIGGER ALL;

--
-- Name: approvals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.approvals_id_seq', 71, true);


--
-- Name: employees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.employees_id_seq', 25, true);


--
-- Name: project_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.project_assignments_id_seq', 29, true);


--
-- Name: projects_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.projects_id_seq', 4, true);


--
-- Name: tasks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.tasks_id_seq', 133, true);


--
-- Name: vacation_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avanheim
--

SELECT pg_catalog.setval('public.vacation_requests_id_seq', 39, true);


--
-- PostgreSQL database dump complete
--

