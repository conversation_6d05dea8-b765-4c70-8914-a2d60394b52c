import os
from datetime import date
from typing import List

import dotenv
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

from information_flows.agents.core_agent.mcp.text2pandas.models import DecomposedQuery
from information_flows.agents.core_agent.mcp.text2pandas.prompts import (
    prompt_template_decompose,
)

dotenv.load_dotenv()


class Decomposer:

    prompt_template_decompose = prompt_template_decompose

    def __init__(self, schema):

        self.schema = schema

        self.llm = ChatOpenAI(
            model=os.getenv("SQL_MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    @staticmethod
    def get_strategy(
        analysis, informative_output_format, paraphrased_user_query, warnings
    ):

        return f"""

The user asked some question and it was redundant and too complex, here is a break down on how to handle it using Pandas and Python:

**Paraphrased Query** : {paraphrased_user_query}

**Analysis** : {analysis}

**Output Instructions** : {informative_output_format}

**Warnings** : 

{'\n'.join([f"\t- {w}" for w in warnings])}
"""

    def decompose(self, query: List[str]) -> dict[str, str]:

        model = self.llm.with_structured_output(DecomposedQuery)

        prompt = PromptTemplate(
            template=self.prompt_template_decompose,
            input_variables=["user_query", "schema", "current_date"],
        )

        chain = prompt | model

        args = {
            "user_query": query,
            "schema": self.schema,
            "current_date": date.today().isoformat(),
        }

        result = chain.invoke(args)

        result = Decomposer.get_strategy(
            analysis=result.user_query_analysis,
            informative_output_format=result.informative_output_format,
            paraphrased_user_query=result.paraphrased_user_query,
            warnings=result.warnings,
        )
        return result
