--[SCHEMA]
BEGIN;
--
-- Create model Employee
--
CREATE TABLE "employees" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "first_name" varchar(50) NOT NULL, "last_name" varchar(50) NOT NULL, "corporative_code" varchar(254) NOT NULL UNIQUE, "hired_date" date NOT NULL, "role" varchar(20) NULL, "accrued_vacation_days" integer NOT NULL CHECK ("accrued_vacation_days" >= 0), "leader_id" bigint NULL, "manager_id" bigint NULL);

--
-- Create model Teams
--
CREATE TABLE teams (
    id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    name varchar(50) NOT NULL,
    leader_id bigint NOT NULL REFERENCES employees(id),
    manager_id bigint NOT NULL REFERENCES employees(id)
);

--
-- <PERSON>reate model Teams Membership
--
CREATE TABLE "teams_membership" (
    id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    team_id bigint NOT NULL REFERENCES teams(id),
    member_id bigint NOT NULL REFERENCES employees(id)
);

--
-- Create model Project
--
CREATE TABLE "projects" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "name" varchar(100) NOT NULL, "start_date" date NOT NULL, "deadline" date NULL, "status" varchar(20) NOT NULL, "lead_id" bigint NULL, "pm_id" bigint NULL);
--
-- Create model ProjectAssignment
--
CREATE TABLE "project_assignments" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "assigned_at" date NOT NULL, "employee_id" bigint NOT NULL, "project_id" bigint NOT NULL);
--
-- Create model Task
--
CREATE TABLE "tasks" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "title" varchar(200) NOT NULL, "description" text NULL, "status" varchar(20) NOT NULL, "priority" varchar(10) NOT NULL, "due_date" date NULL, "created_at" timestamp with time zone NOT NULL, "updated_at" timestamp with time zone NOT NULL, "assigned_to_id" bigint NULL, "project_id" bigint NOT NULL);
--
-- Create model VacationRequest
--
CREATE TABLE "vacation_requests" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "vacation_type" varchar(20) NOT NULL, "start_date" date NOT NULL, "end_date" date NOT NULL, "requested_at" timestamp with time zone NOT NULL, "status" varchar(20) NOT NULL, "calculated_days" integer NULL CHECK ("calculated_days" >= 0), "note" text NULL, "employee_id" bigint NOT NULL);
--
-- Create model Approval
--
CREATE TABLE "approvals" ("id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY, "role" varchar(50) NOT NULL, "approved" boolean NOT NULL, "comment" text NULL, "acted_at" timestamp with time zone NOT NULL, "approver_id" bigint NOT NULL, "vacation_request_id" bigint NOT NULL);
--
-- Create constraint employee_role_valid on model employee
--
ALTER TABLE "employees" ADD CONSTRAINT "employee_role_valid" CHECK ("role" IN ('FULL_STACK_DEV','BACKEND_DEV', 'FRONTEND_DEV', 'DEVOPS', 'PROJECT_MANAGER', 'QA', 'HR', 'OTHER','TEAM_LEAD','ML_ENGINEER','SALES','PYTHON_DEV'));
--
-- Create constraint project_status_valid on model project
--
ALTER TABLE "projects" ADD CONSTRAINT "project_status_valid" CHECK ("status" IN ('PLANNED', 'ACTIVE', 'COMPLETED', 'ON_HOLD'));
--
-- Alter unique_together for projectassignment (1 constraint(s))
--
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_employee_id_project_id_f8dff1f8_uniq" UNIQUE ("employee_id", "project_id");
--
-- Create constraint task_status_valid on model task
--
ALTER TABLE "tasks" ADD CONSTRAINT "task_status_valid" CHECK ("status" IN ('TODO', 'IN_PROGRESS', 'BLOCKED', 'DONE'));
--
-- Create constraint task_priority_valid on model task
--
ALTER TABLE "tasks" ADD CONSTRAINT "task_priority_valid" CHECK ("priority" IN ('LOW', 'MEDIUM', 'HIGH'));
--
-- Create constraint vacation_request_status_valid on model vacationrequest
--
ALTER TABLE "vacation_requests" ADD CONSTRAINT "vacation_request_status_valid" CHECK ("status" IN ('PENDING', 'APPROVED', 'REJECTED'));
--
-- Create constraint vacation_max_25_days on model vacationrequest
--
ALTER TABLE "vacation_requests" ADD CONSTRAINT "vacation_max_25_days" CHECK ("calculated_days" <= 25);
--
-- Create constraint vacation_type_valid on model vacationrequest
--
ALTER TABLE "vacation_requests" ADD CONSTRAINT "vacation_type_valid" CHECK ("vacation_type" IN ('PAID', 'UNPAID'));
--
-- Create constraint approval_role_valid on model approval
--
ALTER TABLE "approvals" ADD CONSTRAINT "approval_role_valid" CHECK ("role" IN ('PROJECT_MANAGER', 'TEAM_LEAD'));
ALTER TABLE "employees" ADD CONSTRAINT "employees_leader_id_8fbcc77a_fk_employees_id" FOREIGN KEY ("leader_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE "employees" ADD CONSTRAINT "employees_manager_id_0674f795_fk_employees_id" FOREIGN KEY ("manager_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "employees_leader_id_8fbcc77a" ON "employees" ("leader_id");
CREATE INDEX "employees_manager_id_0674f795" ON "employees" ("manager_id");
ALTER TABLE "projects" ADD CONSTRAINT "projects_lead_id_f77e29ca_fk_employees_id" FOREIGN KEY ("lead_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE "projects" ADD CONSTRAINT "projects_pm_id_0e055817_fk_employees_id" FOREIGN KEY ("pm_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "projects_lead_id_f77e29ca" ON "projects" ("lead_id");
CREATE INDEX "projects_pm_id_0e055817" ON "projects" ("pm_id");
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_employee_id_341b20d9_fk_employees_id" FOREIGN KEY ("employee_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE "project_assignments" ADD CONSTRAINT "project_assignments_project_id_88f65653_fk_projects_id" FOREIGN KEY ("project_id") REFERENCES "projects" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "project_assignments_employee_id_341b20d9" ON "project_assignments" ("employee_id");
CREATE INDEX "project_assignments_project_id_88f65653" ON "project_assignments" ("project_id");
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_to_id_942feeaf_fk_employees_id" FOREIGN KEY ("assigned_to_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_project_id_288f49d9_fk_projects_id" FOREIGN KEY ("project_id") REFERENCES "projects" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "tasks_assigned_to_id_942feeaf" ON "tasks" ("assigned_to_id");
CREATE INDEX "tasks_project_id_288f49d9" ON "tasks" ("project_id");
ALTER TABLE "vacation_requests" ADD CONSTRAINT "vacation_requests_employee_id_7b252319_fk_employees_id" FOREIGN KEY ("employee_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "vacation_requests_employee_id_7b252319" ON "vacation_requests" ("employee_id");
ALTER TABLE "approvals" ADD CONSTRAINT "approvals_approver_id_d3e19ab6_fk_employees_id" FOREIGN KEY ("approver_id") REFERENCES "employees" ("id") DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE "approvals" ADD CONSTRAINT "approvals_vacation_request_id_30048010_fk_vacation_requests_id" FOREIGN KEY ("vacation_request_id") REFERENCES "vacation_requests" ("id") DEFERRABLE INITIALLY DEFERRED;
CREATE INDEX "approvals_approver_id_d3e19ab6" ON "approvals" ("approver_id");
CREATE INDEX "approvals_vacation_request_id_30048010" ON "approvals" ("vacation_request_id");
COMMIT;
--[SCHEMA]