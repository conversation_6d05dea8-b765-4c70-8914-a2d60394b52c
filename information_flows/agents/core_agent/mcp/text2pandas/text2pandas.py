import os
from datetime import date

import dotenv
import pandas as pd
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

from information_flows.agents.core_agent.mcp.text2pandas.decomposer import Decomposer
from information_flows.agents.core_agent.mcp.text2pandas.models import (
    PandasCode,
    ValidatedPandasCode,
)
from information_flows.agents.core_agent.mcp.text2pandas.prompts import (
    prompt_template_pandas,
    prompt_template_validate,
    tools,
)
from information_flows.agents.core_agent.mcp.text2pandas.summarizer import Summarizer
from information_flows.agents.core_agent.mcp.text2pandas.utils import *

dotenv.load_dotenv()


class PandasAgent:

    prompt_template_validate = prompt_template_validate
    prompt_template_pandas = prompt_template_pandas

    def __init__(self):

        self.schema = extract_schema_block()

        self.decomposer_llm = Decomposer(self.schema)

        self.summarizer_llm = Summarizer()

        self.llm = ChatOpenAI(
            model=os.getenv("SQL_MODEL_NAME"),
            temperature=0,
            stream_usage=False,
        )

    async def precompute(self):

        res = await run_multiple_queries(
            {
                "tables": """
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
"""
            }
        )

        res = res["tables"]

        tables = {}

        for id, r in res.iterrows():

            tmp_data = await run_multiple_queries(
                {r["table_name"]: f"select * from {r['table_name']}"}
            )

            tables[f"{r['table_name']}_df"] = normalize_datetime_columns(
                tmp_data[r["table_name"]]
            )

        self.tables = tables

    def to_pandas(self, query: str, decompose: bool = True) -> dict[str, str]:

        if decompose:
            decomposed_user_query = self.decomposer_llm.decompose(query)

        model = self.llm.with_structured_output(PandasCode)

        prompt = PromptTemplate(
            template=self.prompt_template_pandas,
            input_variables=[
                "user_query",
                "schema",
                "current_date",
                "tables",
                "tables_structure",
                "tools",
            ],
        )

        chain = prompt | model

        tables = f"{len(self.tables.keys())} input parameters : {', '.join([p for p in list(self.tables.keys())])}"

        tables_structure = "\n".join(
            [
                f"Table Dataframe `{k}` columns : {list(v.columns)}"
                for k, v in self.tables.items()
            ]
        )

        args = {
            "query": decomposed_user_query if decompose else query,
            "schema": self.schema,
            "current_date": date.today().isoformat(),
            "tools": tools,
            "tables_structure": tables_structure,
            "tables": tables,
        }

        result = chain.invoke(args)

        return result.pandas_code.strip()

    def _execute_code(self, script):

        script = script.replace("return", "dict_result = ")

        parts = split_and_extract_variables(script)

        env = self.tables.copy()

        current_script = tools

        for part in parts:

            n_f_def = "def get_result" not in part["code"]
            used_dfs = ""

            if n_f_def:

                current_script += f"\n\n{part["code"].strip()}"
                for var in part["used_vars"]:
                    if isinstance(env.get(var, ""), pd.DataFrame):
                        current_script += f"\n# Dataframe {var} contain columns : {list(env[var].columns)}"
                        used_dfs += f"\nDataframe {var} contains columns : {list(env[var].columns)}"

                result = handle_exec(current_script, env, used_dfs, part)

                if "error" in result:

                    return {
                        "error": result["error"],
                        "current_script": current_script.replace(tools, ""),
                        "script": script,
                    }

                else:
                    current_script += result["new_dfs"]

        return {
            "dict_result": result["dict_result"],
            "final_script": current_script,
            "script": script,
        }

    def _validate(self, data: dict[str, str]) -> str:

        model = self.llm.with_structured_output(ValidatedPandasCode)

        prompt = PromptTemplate(
            template=self.prompt_template_validate,
            input_variables=[
                "user_query",
                "schema",
                "current_date",
                "error",
                "generated_code",
                "valid_code",
                "tables_structure",
                "tables",
                "tools",
            ],
        )

        chain = prompt | model

        tables = f"{len(self.tables.keys())} input parameters : {', '.join([p for p in list(self.tables.keys())])}"

        tables_structure = "\n".join(
            [
                f"Table Dataframe `{k}` columns : {list(v.columns)}"
                for k, v in self.tables.items()
            ]
        )

        args = {
            "query": data["query"],
            "schema": self.schema,
            "current_date": date.today().isoformat(),
            "generated_code": data["script"],
            "valid_code": data["current_script"],
            "error": data["error"],
            "tools": tools,
            "tables_structure": tables_structure,
            "tables": tables,
        }

        result = chain.invoke(input=args)

        return result

    def execute(
        self,
        user_query,
        n_validation_trails=3,
        decompose: bool = False,
        summarize: bool = False,
        validate: bool = False,
    ):

        code = self.to_pandas(user_query, decompose=decompose)

        initial_script = code

        answer = None

        improvements = {}

        result = None

        for i in range(n_validation_trails):

            data = self._execute_code(code)

            if "error" in data and ((not validate) or (i == n_validation_trails - 1)):
                return (
                    "Pandas agent was not able to handle the request, please try to paraphrase the question or decompose it into more simple ones.",
                    result,
                    improvements,
                )

            elif ("error" in data) and validate:

                data["query"] = user_query

                script = self._validate(data)

                code = script.validated_pandas_code

                improvements[i] = {
                    "corrected_script": code,
                    "improvements": script.improvements,
                    "error": data["error"],
                }

            elif "error" not in data:

                result = data

                break

        if improvements:

            improvements = {
                "initial_script": initial_script,
                "improvements": improvements,
            }

        if summarize:
            result["query"] = user_query
            answer = self.summarizer_llm.summarize_result(result)

        return answer, result, improvements
