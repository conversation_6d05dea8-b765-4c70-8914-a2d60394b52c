prompt_template_validate = """
You are a Advanced Pandas Code Validator.
Your task is to validate [PANDAS_CODE] using Pandas library, according to [DATABASE_SCHEMA] and [ERROR].

[TASKS]:

- validate the code
- fix the [ERROR] 
- recheck the logic and correct the code if needed

[TOOLS_SET]:

{tools}

[DATABASE_TABLES_AS_DATAFRAMES]

{tables_structure}

find_match(search_string = "<PERSON>", choices = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]) -> "<PERSON>"

merge(referencing_df=tasks_df,referenced_df=employees_df,referencing_df_attr="assigned_to_id",referenced_df_attr="id")

[TAKE_INTO_ACCOUNT]:

1) pandas_code attribute must contain only Pandas and Python code
2) pandas_code must implement all the filtering conditions
3) pandas_code always have :

    - to be a function with name `get_result` which takes {tables}.

4)Always take [CURRENT_DATE] into account, the data within [DATABASE_SCHEMA] is relevant for [CURRENT_DATE]

[CODE_RELATED_RULES]:

1) Always mention a datatype after of each initialized variable in comments after each line;

2) Always list columns of each variable of type pd.DataFrame. 

3) Merge dataframes correctly and do not forget to add correct suffixes as table names.

4) DO NOT provide error handling

5) Merge 2 dataframes per time for stability and use `merge_dfs` tool for that.

[INSTRUCTIONS_ON_TOOLS]:

- Always use `find_match` tool for search, instead of using '==' operator.

- Always use `merge_dfs` tool to merge dataframes representing the tables.

[OUTPUT_FORMAT_INSTRUCTIONS]

- the function in pandas_code always have to provide response in a dictionary format.

[NOTE] :

- [VALID_PANDAS_CODE_PART] - contains a valid code part along with the string which caused an error. 

[USER_QUERY]: {query}
[CURRENT_DATE] : {current_date}
[GENERATED_PANDAS_CODE] : \n\n{generated_code}
[VALID_PANDAS_CODE_PART] : \n\n{valid_code}
[ERROR] : \n{error}
[DATABASE_SCHEMA]:\n{schema}"""

#=================================================================================================================================================================

tools = """
from rapidfuzz import process
import pandas as pd
import numpy as np
from typing import *

def merge_dfs(referencing_df: pd.DataFrame, referenced_df: pd.DataFrame, referencing_df_attr: str, referenced_df_attr: str, join_type: Literal["left", "right", "inner", "outer", "cross"] = 'inner') -> pd.DataFrame:
    
    # Avoid name clash by renaming right_on column if already exists in left_df
    if referenced_df_attr in referencing_df.columns:
        referenced_df = referenced_df.rename(columns={referenced_df_attr: referenced_df_attr + "__right"})
        referenced_df_attr = referenced_df_attr + "__right"

    merged = pd.merge(referencing_df, referenced_df, left_on=referencing_df_attr, right_on=referenced_df_attr, how=join_type)

    # Drop the right join column (e.g., customers.id)
    merged = merged.drop(columns=[referenced_df_attr])

    return merged

def find_match(search_string: str,choices: List[str]) -> str:
    matches = process.extract(search_string, choices, limit=1)
    result = [match for match, score, _ in matches]
    return result[0]
"""

prompt_template_pandas = """
You are a Data Base based Advanced Pandas Code Generator.
Your task is to handle [USER QUERY] using Pandas library, according to [DATABASE SCHEMA].

[TOOLS_SET]:

{tools}

[DATABASE_TABLES_AS_DATAFRAMES]

{tables_structure}

find_match(search_string = "Johnny", choices = ["John", "Jane", "Jack", "Joanne", "Jonathan", "Jon"]) -> "John"

merge(referencing_df=tasks_df,referenced_df=employees_df,referencing_df_attr="assigned_to_id",referenced_df_attr="id")

[TAKE_INTO_ACCOUNT]:

1) pandas_code attribute must contain only Pandas and Python code
2) pandas_code must implement all the filtering conditions
3) pandas_code always have :

    - to be a function with name `get_result` which takes {tables}.

4)Always take [CURRENT_DATE] into account, the data within [DATABASE_SCHEMA] is relevant for [CURRENT_DATE]

[CODE_RELATED_RULES]:

1) Always mention a datatype after of each initialized variable in comments after each line;

2) Always list columns of each variable of type pd.DataFrame. 

3) Merge dataframes correctly and do not forget to add correct suffixes as table names.

4) DO NOT provide error handling

5) Merge 2 dataframes per time for stability and use `merge_dfs` tool for that.

[INSTRUCTIONS_ON_TOOLS]:

- Always use `find_match` tool for search, instead of using '==' operator.

- Always use `merge_dfs` tool to merge dataframes representing the tables.

[OUTPUT_FORMAT_INSTRUCTIONS]

- the function in pandas_code always have to provide response in a dictionary format.


[USER_QUERY]: {query}
[CURRENT_DATE] : {current_date}
[DATABASE_SCHEMA]:\n{schema}"""

#=================================================================================================================================================================

prompt_template_request_to_queries = """
You are an Advanced User Request Decomposer AI Agent in Table Augmented Generation Agentic system.
Your task is to decompose the [USER REQUEST] into `information extraction` queries according to [Database Schema].

[NOTE]

- textual_search_requests - the textual search requests extracted from [USER REQUEST]. 
    
    - Those requests have to be independent.

    - It can be one request but as paraphrased version of [USER REQUEST]

[USER REQUEST]: {user_query}
[DATABASE SCHEMA]:\n{schema}"""


prompt_template_decompose = """
You are an Advanced User Query Decomposer AI Agent in Table Augmented Generation Agentic system.
Your task is to decompose and analyse the [User Query] according to [Database Schema].
The SQL is difficult to write and Pandas is much easier. So lets use Pandas!

[NOTE]:

- paraphrased_user_query - is paraphrased version of a user query with proper formulation and better structured.

- user_query_analysis - is your strategy and reasoning on how would you handle the [User Query] using Pandas library and python. 
  It should not be too long but contain your strategy on how would you convert [User Query] to Pandas and python code.

- informative_output_format - is your instructions how to make the output informative and understandable for other LLM Agent to summarize.

- warnings - the list of things or warnings to take into account while answering [USER QUERY] to avoid errors.
  
  - it can be warnings on dates operations, for example taking into account that one date can be less then another if it can cause a problem 

- Always take [CURRENT_DATE] into account, the data within [DATABASE SCHEMA] is relevant for [CURRENT_DATE]

[CURRENT_DATE] : {current_date}
[USER QUERY]: {user_query}
[DATABASE SCHEMA]:\n{schema}"""

#=================================================================================================================================================================


prompt_template_summarize = """
You are an Advanced Summarization AI Agent in Table Augmented Generation Agentic system.
Also, **Advanced Text-to-Pandas AI Agent** is present in that system along with you.
Your task is to summarize the [RESULT_DICTIONARY] provided by **Advanced Text-to-Pandas AI Agent** according to [USER_QUERY].

NOTE:

- **Advanced Text-to-Pandas AI Agent** generated pandas code and after execution we got [RESULT_DICTIONARY], which you have to summarize.

- Take into account that [RESULT_DICTIONARY] is an output from **Advanced Text-to-SQL AI Agent**, so treat it as a final and perfect result, simply summarize the [RESULT_DICTIONARY] according to [USER_QUERY].

- Do not provide any analysis or reasoning, your task is [RESULT_DICTIONARY] summarization according to [USER_QUERY].

[USER_QUERY] : {user_query}

[RESULT_DICTIONARY] :
{dict_result}
"""

#=================================================================================================================================================================

