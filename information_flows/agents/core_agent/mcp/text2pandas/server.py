import json
import logging

from mcp.server.fastmcp import FastMCP
from opentelemetry.trace import StatusCode

from information_flows.agents.core_agent.mcp.text2pandas.telemetry import tracer
from information_flows.agents.core_agent.mcp.text2pandas.text2pandas import PandasAgent
from information_flows.core.telemetry import extract_tracing_context_stdio

logging.basicConfig(level=logging.INFO)

mcp = FastMCP("Database")


@mcp.tool()
async def ask_company_database(question: str) -> str:
    """Ask a question about data in the company's database. The database can
    accept queries in natural language. When you are interested in some details
    about a particular employee or project, pose a question and ask the database.
    To know what kind of information is available here, just ask the database.

    Despite that the database is smart and understands ordinary questions, its
    knowledge is limited to the data it stores. Thus, when you ask a question,
    provide all necessary details for the database to correctly interpret the
    question without knowing any additional context. Look at the problem as this:
    you have limited knowledge about what is in the databaes, and the database has
    limited awareness of what you know.

    To make database more aware and informed while answering your question, provide
    both a question and a context, defining what are the terms you use or how the
    data "works". For example, when hypothetically asking about working hours of a
    specific employee, mention in the context that each employee in a hypothetical
    company has fixed working hours per day, and mabe other company policies.

    Ask simple, straight forward and comprehensive questions questions of information
    retrieval, akin to select queries in natural language. NEVER ask the database to
    return a totality of objects or the whole list. Always ask to perform aggregation,
    unless the user's requests asks this. Remember the database has a full power of
    Python to perform computations and analysis.
    """
    with tracer.start_as_current_span(
        "ask_company_database", context=extract_tracing_context_stdio()
    ) as span:
        try:
            logging.info(
                f"Using `ask_company_database` tool from `Database` MCP server"
            )

            agent = PandasAgent()

            await agent.precompute()

            # query = f"{question}\nTo better understand the question here is the context: {context}"
            span.set_attribute("query", question)
            _, dict_result, improvements = agent.execute(
                question,
                decompose=False,
                summarize=False,
                validate=True,
                n_validation_trails=3,
            )
            result = (
                str(dict_result["dict_result"])
                if dict_result
                else "Empty database response."
            )
            span.set_attributes(
                {"result": result, "improvements": json.dumps(improvements)}
            )
            logging.info(f"\n\nQuery: {question}")
            logging.info(f"\n\nResult:\n{result}")
        except Exception as exc:
            span.record_exception(exc)
            span.set_status(StatusCode.ERROR)
            logging.error("Failed to ask the database", exc_info=True)
            return "The database failed to return a response."
        return result


if __name__ == "__main__":
    mcp.run(transport="stdio")
