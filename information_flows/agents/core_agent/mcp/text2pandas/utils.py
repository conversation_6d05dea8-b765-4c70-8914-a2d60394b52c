import ast
import asyncio
import os
import textwrap
from typing import Dict, List, Literal

import asyncpg
import numpy as np
import pandas as pd

# code /////////////////////////////////////////////////


class VariableExtractor(ast.NodeVisitor):
    def __init__(self):
        self.assigned = set()
        self.used = set()

    def visit_Name(self, node):
        if isinstance(node.ctx, ast.Store):
            self.assigned.add(node.id)
        elif isinstance(node.ctx, ast.Load):
            self.used.add(node.id)
        self.generic_visit(node)

    def visit_arg(self, node):
        self.assigned.add(node.arg)


def split_and_extract_variables(code_str):
    tree = ast.parse(textwrap.dedent(code_str))
    parts = []

    for node in tree.body:
        if isinstance(node, ast.FunctionDef):
            # Add the function signature (header)
            header = f"def {node.name}({', '.join(arg.arg for arg in node.args.args)}):"
            parts.append(
                {
                    "code": header,
                    "assigned_vars": set(),
                    "used_vars": {arg.arg for arg in node.args.args},
                }
            )

            # Extract full blocks from function body
            for stmt in node.body:
                try:
                    segment = ast.unparse(
                        stmt
                    )  # use unparse to keep indentation correct
                except Exception:
                    segment = ast.get_source_segment(code_str, stmt) or "<unavailable>"

                extractor = VariableExtractor()
                extractor.visit(stmt)

                parts.append(
                    {
                        "code": segment,
                        "assigned_vars": extractor.assigned,
                        "used_vars": extractor.used,
                    }
                )
        else:
            try:
                segment = ast.unparse(node)
            except Exception:
                segment = ast.get_source_segment(code_str, node) or "<unavailable>"

            extractor = VariableExtractor()
            extractor.visit(node)

            parts.append(
                {
                    "code": segment,
                    "assigned_vars": extractor.assigned,
                    "used_vars": extractor.used,
                }
            )

    return parts


def handle_exec(current_script, env, used_dfs, part):

    try:

        exec(current_script, env)

        new_dfs = "\n".join(
            [
                f"# Dataframe {v} contains columns : {list(env[v].columns)}"
                for v in part["assigned_vars"]
                if isinstance(v, pd.DataFrame)
            ]
        )

        return {"dict_result": env.get("dict_result", None), "new_dfs": new_dfs}

    except Exception as e:

        if isinstance(e, KeyError):
            return {
                "error": f"\n\nError was detected on that line : \n\n{part["code"]}.\n\nError Description : \nColumn `{e.args[0]}` was not found in some of DataFrames used.\n\nHere are used DataFrames columns available: \n\n{used_dfs}"
            }

        else:
            return {
                "error": f"\n\nError was detected on that line : \n\n{part["code"]}.\n\nError Description : \n{str(e)}"
            }


def extract_schema_block(sql_file_path: str = os.getenv("SQL_FILE_PATH")) -> str:
    """
    Extracts the block of SQL between --[SCHEMA] markers from the given file.

    Args:
        sql_file_path (str): Path to the .sql file.

    Returns:
        str: Extracted SQL schema block with all \n preserved.
    """
    in_schema = False
    schema_lines = []

    with open(sql_file_path, "r", encoding="utf-8") as file:
        for line in file:
            if "--[SCHEMA]" in line:
                if not in_schema:
                    in_schema = True
                else:
                    # Second --[SCHEMA] ends the block
                    break
            elif in_schema:
                schema_lines.append(line)

    str = "".join(schema_lines)
    return "\n".join([s if "CREATE INDEX" not in s else "" for s in str.split("\n")])


def normalize_datetime_columns(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()

    def normalize_series(s: pd.Series) -> pd.Series:
        # Always wrap as Series
        if not isinstance(s, pd.Series):
            s = pd.Series(s if isinstance(s, (list, np.ndarray)) else [s])

        # Convert to datetime
        s = pd.to_datetime(s, errors="coerce")

        # Normalize timezone
        if hasattr(s.dt, "tz") and s.dt.tz is not None:
            s = s.dt.tz_convert("UTC").dt.tz_localize(None)

        return s

    for col in df.columns:
        # Heuristics: column name or dtype
        if (
            pd.api.types.is_object_dtype(df[col])
            and any(
                keyword in col.lower()
                for keyword in ["date", "time", "datetime", "timestamp"]
            )
        ) or pd.api.types.is_datetime64_any_dtype(df[col]):
            df[col] = normalize_series(df[col])

    return df


# ////////////////SYSTEM//////////////////


async def run_multiple_queries(queries: Dict[str, str]) -> Dict[str, pd.DataFrame]:
    """
    Run multiple SQL queries concurrently and return results as Pandas DataFrames.

    Args:
        queries: A dict mapping query names to SQL strings.

    Returns:
        A dict mapping query names to Pandas DataFrames.
    """
    pool = await asyncpg.create_pool(
        user=os.getenv("POSTGRES_USER"),
        password=os.getenv("POSTGRES_PASSWORD"),
        host=os.getenv("POSTGRES_HOST"),
        port=os.getenv("POSTGRES_PORT"),
        database=os.getenv("SQL_DEMO_DATABASE"),
        min_size=1,
        max_size=len(queries),
    )

    async def run_query(name: str, sql: str) -> dict[str, pd.DataFrame]:
        async with pool.acquire() as conn:
            try:
                records = await conn.fetch(sql)
                df = (
                    pd.DataFrame(records, columns=records[0].keys())
                    if records
                    else pd.DataFrame()
                )
                return name, df
            except Exception as e:
                print(f"[{name}] failed: {e}")
                return name, pd.DataFrame({"error": [str(e)]})  ### handle sql errors

    tasks = [run_query(name, sql) for name, sql in queries.items()]
    results = await asyncio.gather(*tasks)
    await pool.close()

    return {name: df for name, df in results}


# ********8********** TOOLS ********8******************8******************8******************8******************8******************8**********

from rapidfuzz import process


def find_match(search_string: str, choices: List[str]) -> str:
    matches = process.extract(search_string, choices, limit=1)
    result = [match for match, score, _ in matches]
    return result[0]


def merge_dfs(
    referencing_df: pd.DataFrame,
    referenced_df: pd.DataFrame,
    referencing_df_attr: str,
    referenced_df_attr: str,
    join_type: Literal["left", "right", "inner", "outer", "cross"] = "inner",
) -> pd.DataFrame:
    """
    Merge two DataFrames on different column names and keep only one join key.

    Parameters:
        referencing_df (pd.DataFrame): DataFrame which represents table referencing to another df(table).
        referenced_df (pd.DataFrame): DataFrame on which referencing DataFrame refers.
        referencing_df_attr (str): Column in referencing_df to join on.
        referenced_df_attr (str): Column in referenced_df to join on.
        join_type (str): Join type ('inner', 'left', etc.).

    Returns:
        pd.DataFrame: Merged DataFrame without duplicate join key.
    """
    # Avoid name clash by renaming right_on column if already exists in left_df
    if referenced_df_attr in referencing_df.columns:
        referenced_df = referenced_df.rename(
            columns={referenced_df_attr: referenced_df_attr + "__right"}
        )
        referenced_df_attr = referenced_df_attr + "__right"

    merged = pd.merge(
        referencing_df,
        referenced_df,
        left_on=referencing_df_attr,
        right_on=referenced_df_attr,
        how=join_type,
        suffixes=("", "_del"),
    )

    # Drop the right join column (e.g., customers.id)
    merged = merged.drop(columns=[referenced_df_attr])

    merged = merged[[c for c in merged.columns if not c.endswith("_del")]]

    return merged


def date_operation(date1, date2=None, operation="get_diff_days", **kwargs):
    """
    Safely perform date operations between one or two datetime-like inputs.
    Always returns a pandas Series and handles all type/timezone conflicts.

    Args:
        date1 (pd.Series or scalar): First date series or scalar.
        date2 (pd.Series, scalar, or None): Second date series or scalar.
        operation (str): Operation to perform.

    Returns:
        pd.Series: Result of the date operation.
    """

    # Always wrap as Series first to avoid DatetimeIndex issues
    if not isinstance(date1, pd.Series):
        date1 = pd.Series(date1 if isinstance(date1, (list, np.ndarray)) else [date1])
    if date2 is not None and not isinstance(date2, pd.Series):
        date2 = pd.Series(date2 if isinstance(date2, (list, np.ndarray)) else [date2])

    # Convert to datetime
    date1 = pd.to_datetime(date1, errors="coerce")
    if date2 is not None:
        # Broadcast if needed
        if len(date2) == 1 and len(date1) > 1:
            date2 = pd.Series([date2.iloc[0]] * len(date1), index=date1.index)

        date2 = pd.to_datetime(date2, errors="coerce")

        # Normalize timezone mismatch
        if hasattr(date1.dt, "tz") or hasattr(date2.dt, "tz"):
            if date1.dt.tz is not None and date2.dt.tz is None:
                date1 = date1.dt.tz_localize(None)
            elif date1.dt.tz is None and date2.dt.tz is not None:
                date2 = date2.dt.tz_localize(None)
            elif date1.dt.tz is not None and date2.dt.tz is not None:
                date1 = date1.dt.tz_convert("UTC").dt.tz_localize(None)
                date2 = date2.dt.tz_convert("UTC").dt.tz_localize(None)

    # Unary operations
    if date2 is None:
        if operation == "get_year":
            return date1.dt.year
        elif operation == "get_month":
            return date1.dt.month
        elif operation == "get_day":
            return date1.dt.day
        elif operation == "get_weekday":
            return date1.dt.weekday
        elif operation == "get_hour":
            return date1.dt.hour
        elif operation == "get_minute":
            return date1.dt.minute
        elif operation == "get_second":
            return date1.dt.second
        else:
            raise ValueError(f"Unsupported unary operation: {operation}")

    # Binary operations
    if operation == "get_diff_seconds":
        return (date1 - date2).dt.total_seconds()
    elif operation == "get_diff_days":
        return (date1 - date2).dt.days
    elif operation == "get_min":
        return pd.Series(np.minimum(date1.values, date2.values), index=date1.index)
    elif operation == "get_max":
        return pd.Series(np.maximum(date1.values, date2.values), index=date1.index)
    elif operation == "compare_is_earlier":
        return date1 < date2
    elif operation == "compare_is_earlier_equal":
        return date1 <= date2
    elif operation == "compare_is_later":
        return date1 > date2
    elif operation == "compare_is_later_equal":
        return date1 >= date2
    elif operation == "compare_is_equal":
        return date1 == date2
    else:
        raise ValueError(f"Unsupported binary operation: {operation}")
