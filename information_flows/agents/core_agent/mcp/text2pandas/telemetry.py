from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor

from information_flows.settings import settings

resource = Resource.create(
    attributes={
        SERVICE_NAME: "database_mcp",
        "telemetry_correlation_id": settings.TELEMETRY_CORRELATION_ID,
    }
)
tracer_provider = TracerProvider(resource=resource)
processor = SimpleSpanProcessor(
    OTLPSpanExporter(endpoint=settings.OPENTELEMETRY_COLLECTOR_ENDPOINT)
)
tracer_provider.add_span_processor(processor)
trace.set_tracer_provider(tracer_provider)

tracer = tracer_provider.get_tracer("opentelemetry")
