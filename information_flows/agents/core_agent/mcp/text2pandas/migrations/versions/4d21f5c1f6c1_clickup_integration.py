"""clickup integration

Revision ID: 4d21f5c1f6c1
Revises: cd8d5b77561c
Create Date: 2025-07-07 15:33:39.057917

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect


revision: str = '4d21f5c1f6c1'
down_revision: Union[str, None] = 'cd8d5b77561c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    bind = op.get_bind()
    inspector = inspect(bind)
    columns = [col['name'] for col in inspector.get_columns('employees')]

    if 'clickup_id' not in columns:
        op.add_column('employees', sa.Column('clickup_id', sa.String(), nullable=True))

    op.execute(
        sa.text("""
            UPDATE employees
            SET clickup_id = :clickup_id
            WHERE clickup_id IS NULL
        """).bindparams(clickup_id='81772234')
    )


def downgrade() -> None:
    """Downgrade schema."""
    bind = op.get_bind()
    inspector = inspect(bind)
    columns = [col['name'] for col in inspector.get_columns('employees')]

    if 'clickup_id' in columns:
        op.drop_column('employees', 'clickup_id')
