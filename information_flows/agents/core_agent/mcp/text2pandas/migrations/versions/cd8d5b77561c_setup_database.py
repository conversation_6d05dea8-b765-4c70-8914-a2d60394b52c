"""setup database

Revision ID: cd8d5b77561c
Revises:
Create Date: 2025-06-04 10:10:55.772255

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "cd8d5b77561c"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    with open("./dml.sql") as file:
        op.execute(file.read())

    with open("./insert.sql") as file:
        op.execute(file.read())


def downgrade() -> None:
    """Downgrade schema."""
    tables = [
        "approvals",
        "project_assignments",
        "tasks",
        "teams_membership",
        "vacation_requests",
        "projects",
        "teams",
        "employees",
    ]
    for table in tables:
        op.drop_table(table, if_exists=True)
