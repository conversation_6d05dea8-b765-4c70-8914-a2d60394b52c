from typing import List, Optional

import dotenv
from pydantic import BaseModel, Field

from information_flows.agents.core_agent.mcp.text2pandas.prompts import *

dotenv.load_dotenv()


class Answer(BaseModel):
    answer: str = Field(
        None,
        description="The answer to user question based on [RESULT_DICTIONARY] provided.",
    )


class PandasCode(BaseModel):
    pandas_code: str = Field(
        ..., description="the Pandas code to implement answer [USER QUERY]"
    )
    explanation: Optional[str] = Field(
        None, description="Detailed explanation of Pandas code"
    )


class ValidatedPandasCode(BaseModel):
    validated_pandas_code: str = Field(..., description="the validated Pandas")
    improvements: Optional[str] = Field(None, description="what was improved")


class DecomposedQuery(BaseModel):

    paraphrased_user_query: str = Field(
        default_factory="",
        description="Paraphrased and better structured user query.",
    )

    user_query_analysis: str = Field(
        default_factory="",
        description="The detailed analysis of the user query according to the database schema.",
    )

    informative_output_format: str = Field(
        default_factory="",
        description="The instructions how to make the output informative and understandable.",
    )

    warnings: List[str] = Field(
        default_factory=[],
        description="The list of things or warnings to take into account while answering [USER QUERY] to avoid errors.",
    )
