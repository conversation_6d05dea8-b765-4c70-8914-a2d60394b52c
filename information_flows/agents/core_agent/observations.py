from datetime import datetime
from typing import Any, Optional, override

from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
    ToolCall,
    ToolMessage,
)
from pydantic import BaseModel


def message_timestamp(timestamp: datetime | None = None) -> str:
    """Formatted string representation of a message timestamp."""
    if not timestamp:
        timestamp = datetime.now()
    return timestamp.strftime("%H:%M:%S %B %d, %Y")


class BaseObservation(BaseMessage):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.timestamp = datetime.now()

    def prefix(self) -> str:
        raise NotImplementedError

    def suffix(self) -> str:
        return f"[Received at: {message_timestamp(self.timestamp)}]"


class ChiefMessage(HumanMessage, BaseObservation):
    """Message from a chief to an assistant."""

    def __init__(self, content: str, chief_name: str, **kwargs):
        super().__init__(content=content, **kwargs)
        self.chief_name = chief_name

    @override
    def prefix(self) -> str:
        return f"[Message from your chief {self.chief_name}]"


class AssistantMessage(AIMessage, BaseObservation):
    """Message from another assistant to an assistant."""

    def __init__(self, content: str, employee_name: str, **kwargs):
        super().__init__(content=content, **kwargs)
        self.employee_name = employee_name

    @override
    def prefix(self) -> str:
        return f"[Message from {self.employee_name}'s assistant]"


class Action(BaseModel):
    """A representation of an action of the agent, a tool call with arguments."""

    name: str
    args: dict[str, Any]
    tool_call_id: str


class ActionChoiceMessage(AIMessage, BaseObservation):
    """Assistant's own message with an action(s) (tool calls)."""

    def __init__(self, content: Any, tool_calls: list[ToolCall], **kwargs):
        formatted = []
        for tool_call in tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]
            content = f"\nUse the tool `{tool_name}` with arguments `{tool_args}`"
            formatted.append(content)
        super().__init__(content="\n".join(formatted), tool_calls=tool_calls, **kwargs)

    def actions(self) -> list[Action]:
        """Return a list of agent's tool calls as actions."""
        converted = []
        for tool_call in self.tool_calls:
            converted.append(
                Action(
                    name=tool_call["name"],
                    args=tool_call["args"],
                    tool_call_id=tool_call["id"],
                )
            )
        return converted

    @classmethod
    def from_ai_message(cls, ai_message: AIMessage) -> "ActionChoiceMessage":
        """Convert a message with tool calls to an action message."""
        return cls(**ai_message.model_dump())

    @override
    def prefix(self) -> str:
        return f"[You call a tool]"

    @override
    def suffix(self) -> str:
        return f"[Performed at: {message_timestamp(self.timestamp)}]"


class ActionResult(BaseModel):
    """A representation of result of an action, a return value of a tool."""

    name: str
    return_value: Any
    tool_call_id: str


class ActionResultMessage(ToolMessage, BaseObservation):
    """Message from a tool to an assistant carrying tool's result."""

    def __init__(self, content: Any, **kwargs):
        super().__init__(content=content, **kwargs)

    @classmethod
    def from_tool_message(cls, tool_message: ToolMessage) -> "ActionResultMessage":
        """Convert a message with tool response to an action result message."""
        return cls(**tool_message.model_dump())

    def result(self) -> ActionResult:
        """Convert tool call return value to action result."""
        return ActionResult(
            name=self.name,
            return_value=self.content,
            tool_call_id=self.tool_call_id,
        )

    @override
    def prefix(self) -> str:
        return f"[Tool returns a response]"

    @override
    def suffix(self) -> None:
        return None


type Observation = ChiefMessage | AssistantMessage | ActionChoiceMessage | ActionResultMessage


def format_observations(observations: list[Observation]) -> str:
    formatted_observations = []
    for i, observation in enumerate(observations, start=1):
        suffix: Optional[str] = None
        if isinstance(
            observation,
            (ChiefMessage, AssistantMessage, ActionChoiceMessage, ActionResultMessage),
        ):
            prefix = observation.prefix()
            suffix = observation.suffix()
        elif isinstance(observation, SystemMessage):
            prefix = "System"
        else:
            raise ValueError(f"Got unsupported observation type: {observation}")
        formatted_observations.append(
            f"{i}. {prefix}: {observation.content} {suffix if suffix else ""}"
        )
    return "\n".join(formatted_observations)
