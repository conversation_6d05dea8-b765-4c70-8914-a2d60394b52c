from pydantic import BaseModel, Field

from information_flows.agents.core_agent.prompts import CognitiveArtifact
from information_flows.agents.core_agent.utils import compress, remove_indent


class Task(CognitiveArtifact):
    identifier: str = Field(
        description="A task identifier in the form of `helping_frank_with_email_draft`."
    )
    requester: str = Field(
        description=compress(
            """Who requested the task or initiated the situation, either your chief or another
            assistant. Explicitely specify the name of the requester."""
        )
    )
    requester_language: str = Field(
        description="Determine language of the requester in the message, e.g. English or Russian."
    )
    description: str = Field(
        description=compress(
            """A precise and comprehensive description of the task at hand. Ranging from
            simple requests to complex long conversations with multiple particimants and
            long sequences of actions. Mention all necessary details and you understanding
            of the task."""
        )
    )
    participants: list[str] = Field(
        description="What other assistants are involved in this situation, e.g. <PERSON><PERSON>'s assistant."
    )
    goal: str = Field(
        description=compress(
            """A clear definition of the goal state, specifying when the task is considered
            complete. Usually a task is to fulfill a specific request of a user or an
            assistant, and it might involve a long sequence of communication and actions."""
        )
    )
    plan: str = Field(
        description=compress(
            """Describe a list of steps that you already made and intend to make to achieve
            the goal. For example, steps might include `respond to an assistant of...` or
            `tell my chief about this...`, `check the policy rules, and then...`, `notify
            my chief about task results...`, etc. Describe steps as enumerated list with
            numbers meaning their order. Mark each step with `done` or `planned` markers
            in the parentheses in the end, e.g. `2. Notify my chief about... (planned)`.
            Often update the list, even changing the plan or replanning the steps."""
        )
    )
    requester_awareness: str = Field(
        description=compress(
            """What I have informed the requester of the task about this situation and
            the progress in achieving its goal. Does they have a complete understanding
            of the ongoing conversations and the execution."""
        )
    )

    def for_llm(self) -> str:
        return remove_indent(
            f"""
            **Identifier**: {self.identifier}
            **Requester**: {self.requester}
            **Requester language**: {self.requester_language}
            **Description**:\n{self.description}
            **Participants**: {", ".join(self.participants) or "No other assistants involved."}
            **Goal**: {self.goal}
            **Plan**:\n{self.plan}
            **Requester awareness**:\n{self.requester_awareness}
            """
        )


def update_tasks(old: list[Task], updated: Task | list[Task]) -> list[Task]:
    """Update a list of current tasks, adding, removing, or modifying them."""
    if not isinstance(old, list):
        old = [old]
    if not isinstance(updated, list):
        updated = [updated]
    # collect updated versions of tasks skipping removed tasks
    modified_task_ids = set()
    modified = []
    for task in updated:
        if isinstance(task, RemoveTask):
            modified_task_ids.add(task.task.identifier)
        else:
            modified_task_ids.add(task.identifier)
            modified.append(task)
    # skip old versions of tasks if new ones are available
    unmodified = []
    for task in old:
        if task.identifier not in modified_task_ids:
            unmodified.append(task)
    return [*modified, *unmodified]


class RemoveTask(BaseModel):
    """Wrap the task into a deleted marker."""

    task: Task


class CompletedTask(BaseModel):
    """Derive a completed task instance from an active task."""

    task: Task

    def for_llm(self) -> str:
        return f"**Description**:\n{self.task.description}"


class Request(BaseModel):
    quote: str = Field(
        description=compress(
            """A literal piece of a message citing a distinct request. The citation might be
            scattered over the message, e.g. "we need to get the task done ... also make a
            progress report", so a quote has to include all related pieces of the same request
            separated by ellipsis "...". Different requests might share some pieces or details."""
        )
    )
    paraphrased_message: str = Field(
        description=compress(
            """Paraphrase the quote to make it complete and self-contained message as though
            it was sent alone. For example, if an extracted quote is "to book a call in Google
            Meet" then a completed quote is "I would like to book a call in Google Meet". The
            resulting self-contained request has to include the request itself and the auxiliary
            information and words that was used before splitting the original message. If a
            message contains only one request, then leave it as is."""
        )
    )
    interpretation: str = Field(
        description=compress(
            """Your reasoning about what the request is about (if it is a request at all). Is
            this request related to an existing task or is a new task that has to be created?
            Each request has to be associated with a task, even a trivial one."""
        )
    )
    detected_task: Task = Field(
        description=compress(
            """Given the detected requests in a message, determine to which of the active tasks
            (if any) each of the requests belongs and update the tasks accordingly. If some requests
            are not related to any of the active tasks, then create new task for the request."""
        )
    )


class MessageAnalysis(BaseModel):
    """Analyze the **last** message either from your chief or another assistant in the context
    of the tasks you are currently working on (active tasks, NOT completed ones). Check all
    the active tasks with respect to the message, linking the author of the latest message with
    the requestor or participants of existing tasks. If you find the message related to some
    existing task, take the task, update its description and participants, clarify the goal,
    and update your progress in performing this active task using the additional information
    from the message. If the message is not related to any of the existing tasks (or there
    are no tasks at all), then create a new task about this new request of your chief or
    another assistant, assigning a unique task identifier to the new task. If you receive
    a request explicitely to repeat or implying to repeat the task you have already completed,
    create a new similar task, and repeat everything necessary.
    """

    detected_requests: list[Request] = Field(
        description=compress(
            """If the message implies multiple distinct requests, then extract each of them into
            a list. For example, a message "Hey, I want to get a report of today's updates, and
            also to notify my PM that I will skip the daily meeting" contains two distinct
            requests, namely "to get a report of today's updates" and "to notify my PM", because
            they are not connected and can be performed separately, thus these are two distinct tasks.
            Extract the requests as quotes in the form of "... to make a pull request ...", where
            inferred meaning is intervined with the actual one. A message contains at least one request,
            and usually only one request, but it can possibly contain any number of requests. However,
            be very accurate, and base your reasoning on the currently active tasks, e.g. if a request
            is related to an active task, then it is a separate request. Although, multiple requests
            related to the same task MUST be considered a single one, and not divided. Include all
            details relevant to an request and mentioned across the message in the description of
            the request. If some details are related to multiple requests, then include the details
            in each of them."""
        )
    )
