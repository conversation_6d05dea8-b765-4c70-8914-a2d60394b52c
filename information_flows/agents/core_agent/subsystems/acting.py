from pydantic import BaseModel, Field

from information_flows.agents.core_agent.actionable_output import (
    ActionableOutput,
    RawStructuredOutput,
    ToolChoicePlaceholder,
)
from information_flows.agents.core_agent.prompts import CognitiveArtifact
from information_flows.agents.core_agent.subsystems import multitasking
from information_flows.agents.core_agent.utils import compress, remove_indent


class Explanation(CognitiveArtifact):
    next_action_reasoning: str
    next_action_expected_outcome: str
    next_action_anticipated_failure: str
    next_action_estimated_utility: str

    def for_llm(self) -> str:
        return remove_indent(
            f"""
            **Purpose and motivation**: {self.next_action_reasoning}
            **Expected outcome of the action**: {self.next_action_expected_outcome}
            **Anticipated failures or issues**: {self.next_action_anticipated_failure}
            **Estimated utility of the outcome**: {self.next_action_estimated_utility}
            """
        )


class ChooseAction(ActionableOutput):
    """Make the next action and explain your reasoning."""

    next_action_reasoning: str = Field(
        description=compress(
            """Explicitly describe your **next** action and the underlying reasoning. Use human-friendly
            style, up to 2 sentences. Do not mention tools and arguments explicitly, but describe
            the action in essence. If you are ending the turn using `end_turn` tool, then describe
            your motivation, e.g. you are completed the task or waiting for a response, and
            explain the reasoning on completing the turn and updating the task. However, do not
            mention that you are ending the turn, or anything about turn-based approach, but
            say that you are completing teh task instead."""
        )
    )
    next_action_expected_outcome: str = Field(
        description=compress(
            "What is the expected result of the **next** action and its effect on the task?"
        ),
    )
    next_action_anticipated_failure: str = Field(
        description=compress(
            "What can go wrong (e.g. undesirable side effects, errors, technical issues)?"
        ),
    )
    next_action_estimated_utility: str = Field(
        description=compress(
            "How useful is the desired outcome of the **next** action?"
        ),
    )

    tool_call: ToolChoicePlaceholder = Field(
        description=compress(
            "Call a tool corresponding to the action with appropriate arguments."
        )
    )

    updated_task: multitasking.Task = Field(
        description=compress(
            """Update the current task description, plan and other parameters to reflect
            your currently achieved progress and future steps. If a task is completed,
            then explain how your current results agree with the goal of the task and
            update the `description` to reflect the task completion."""
        )
    )

    def get_explanation(self) -> Explanation:
        """Return explanation and reasoning of this action choice."""
        return Explanation(
            next_action_reasoning=self.next_action_reasoning,
            next_action_expected_outcome=self.next_action_expected_outcome,
            next_action_anticipated_failure=self.next_action_anticipated_failure,
            next_action_estimated_utility=self.next_action_estimated_utility,
        )


class Reflection(BaseModel):
    previous_action_observed_outcome: str
    previous_action_reflection: str


class ReflectAndChooseAction(ActionableOutput):
    """Reflect on the outcome of your **previous** action in the context of the current task
    and its influence on achieving the goal of the task. Analyze any information returned
    by a tool received from any information source. Update the `plan` of the current
    task to reflect the latest achieved outcomes and changes of the environment in response
    to your actions. Adjust your plan accordingly to account for any contingencies or
    unexpected issues, if necessary. Incorporate newly discovered information from the
    result of last action into the plan, update the plan to achieve the goal in the best
    way and making as grounded decisions as possible. Remove redundant steps and add new
    necessary steps, given the new information, if needed. Make the next action and explain
    your reasoning.
    """

    previous_action_observed_outcome: str = Field(
        description=compress(
            "What is the observed result of the **previous** action after performing it?"
        ),
    )
    previous_action_reflection: str = Field(
        description=compress(
            """Reflect on the outcome of your **previous** action in the context of the current
            task and its influence on achieving the goal of the task. Analyze any information
            returned by a tool received from any information source."""
        ),
    )

    updated_task: multitasking.Task = Field(
        description=compress(
            """Update the current task description, plan and other parameters to reflect
            your currently achieved progress and future steps. If a task is completed,
            then explain how your current results agree with the goal of the task and
            update the `description` to reflect the task completion. Change the plan,
            add new steps clarified by obtained information from the previous tool,
            and remove unnecessary steps."""
        )
    )

    next_action_reasoning: str = Field(
        description=compress(
            """Explicitly describe your **next** action and the underlying reasoning. Use human-friendly
            style, up to 2 sentences. Do not mention tools and arguments explicitly, but describe
            the action in essence. If you are ending the turn using `end_turn` tool, then describe
            your motivation, e.g. you are completed the task or waiting for a response, and
            explain the reasoning on completing the turn and updating the task. However, do not
            mention that you are ending the turn, or anything about turn-based approach, but
            say that you are completing teh task instead."""
        )
    )
    next_action_expected_outcome: str = Field(
        description=compress(
            "What is the expected result of the **next** action and its effect on the task?"
        ),
    )
    next_action_anticipated_failure: str = Field(
        description=compress(
            "What can go wrong (e.g. undesirable side effects, errors, technical issues)?"
        ),
    )
    next_action_estimated_utility: str = Field(
        description=compress(
            "How useful is the desired outcome of the **next** action?"
        ),
    )

    tool_call: ToolChoicePlaceholder = Field(
        description=compress(
            "Call a tool corresponding to the action with appropriate arguments."
        )
    )

    def get_reflection(self) -> Reflection:
        """Return reflection on the previous action before current action choice."""
        return Reflection(
            previous_action_reflection=self.previous_action_reflection,
            previous_action_observed_outcome=self.previous_action_observed_outcome,
        )

    def get_explanation(self) -> Explanation:
        """Return explanation and reasoning of this action choice."""
        return Explanation(
            next_action_reasoning=self.next_action_reasoning,
            next_action_expected_outcome=self.next_action_expected_outcome,
            next_action_anticipated_failure=self.next_action_anticipated_failure,
            next_action_estimated_utility=self.next_action_estimated_utility,
        )


type ChooseActionResult = RawStructuredOutput[ChooseAction | ReflectAndChooseAction]
