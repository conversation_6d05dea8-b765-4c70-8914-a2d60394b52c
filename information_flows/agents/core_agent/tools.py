import logging
from typing import Literal

from langchain_core.tools import tool
from langgraph.runtime import get_runtime

from information_flows.agents.core_agent import measurement
from information_flows.agents.core_agent.graphs.state import RuntimeContext
from information_flows.core.agent import record_measurement, send_message
from information_flows.core.decorators import make_registry
from information_flows.core.logger import agent_logger

logger = logging.getLogger("core_tools")
logger.setLevel(logging.INFO)

core_tools, add_tool = make_registry()

type EndTurnReason = Literal["task_completed", "in_progress"]


@add_tool
@tool
def end_turn(reason: EndTurnReason):
    """Specify the `reason` why you want to end the turn. End your current turn
    of interaction and wait until the next incoming message. You MUST call this
    tool when you have either `completed` your current task or the task is
    `in_progress` if you have asked other assistants or your chief to provide
    additional information and is waiting for response from them to proceed.
    Use `in_progress` only for long running tasks, with anticipated multiple
    turns in a conversation."""
    return None


@add_tool
@tool
async def communicate_with_chief(message: str) -> str:
    """Send the `message` to your chief. You must use this tool to communicate
    with your chief as it is the only way to send a message to them. The message
    text can contain Markdown formatting and arbitrary unicode symbols."""
    runtime = get_runtime(RuntimeContext)
    chief_profile = runtime.context.cheif_profile
    sent_message = await send_message(message, receiver_id=runtime.context.employee_id)
    await record_measurement(measurement.SendMessage(message=sent_message))
    logger.info(
        f"Send a message from {chief_profile.name}'s assistant to {chief_profile.name}"
    )
    return f"Successfully send the message to your chief {chief_profile.name}."


@add_tool
@tool
async def communicate_with_assistant(message: str, external_employee_id: str) -> str:
    """Send message to the assistant of an employee with `external_employee_id`.
    Base your tone on your internal understanding of the working relationship,
    but do not mention that this understanding is derived from a description
    or any tracked record."""
    runtime = get_runtime(RuntimeContext)
    try:
        local_employee_id = await runtime.context.context_service.external_to_local(
            external_employee_id
        )
    except ValueError:
        return (
            f"Failed to communicate with assistant: external_employee_id `{external_employee_id}`"
            f"does not exist or you are not allowed to communicate with the assistant"
        )
    employee = await runtime.context.context_service.employee_profile(local_employee_id)
    agent_logger.assistant_internal(
        runtime.context.employee_id,
        f"Sending message to {employee.name}'s assistant:\n{message}",
        runtime.context.cheif_profile.name,
    )
    sent_message = await send_message(message, receiver_id=local_employee_id)
    await record_measurement(measurement.SendMessage(message=sent_message))
    logger.info(
        f"Send a message from {runtime.context.cheif_profile.name}'s assistat to {employee.name}'s assistant"
    )
    return f"Successfully send the message to {employee.name}'s assistant."
