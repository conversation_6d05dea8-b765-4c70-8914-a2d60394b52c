import asyncio
from contextlib import asynccontextmanager
from typing import Any, Optional
from uuid import UUID, uuid4

from langchain.chat_models import init_chat_model
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from langchain_core.tracers import LangChainTracer
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.base import BaseCheckpointSaver
from langsmith import Client
from rich.pretty import pprint

from information_flows.agents.core_agent.context import AgentContextService
from information_flows.agents.core_agent.graphs.attention_graph import (
    AttentionState,
    create_attention_graph,
)
from information_flows.agents.core_agent.graphs.executor_graph import (
    ExecutorState,
    create_executor_graph,
)
from information_flows.agents.core_agent.graphs.state import (
    AgentState,
    LLMConfig,
    RuntimeContext,
)
from information_flows.agents.core_agent.graphs.state_resolver import StateResolver
from information_flows.agents.core_agent.parallel_processing_mixin import (
    ParallelProcessingMixin,
)
from information_flows.agents.core_agent.prompts import Specification
from information_flows.agents.core_agent.subsystems import multitasking
from information_flows.agents.core_agent.tools import core_tools
from information_flows.core.agent import AgentMCPConfig, BaseAgent, BaseAgentFactory
from information_flows.core.types import Message
from information_flows.settings import settings


# Lazy initialization of LLM config to avoid unclosed connections
def get_llm_config() -> LLMConfig:
    """Get LLM configuration with lazy initialization."""
    return {
        "focus_attention": init_chat_model(
            "openai:gpt-4.1", api_key=settings.OPENAI_API_KEY, temperature=0
        ),
        "choose_action": init_chat_model(
            "openai:gpt-4.1", api_key=settings.OPENAI_API_KEY, temperature=0
        ),
    }


class Agent(ParallelProcessingMixin, BaseAgent):
    def __init__(
        self,
        employee_id: UUID,
        checkpointer: BaseCheckpointSaver,
        context: AgentContextService,
        tools: list[BaseTool],
        specification: Specification,
        langsmith_trace: bool = False,
        recursion_limit: int = 100,
        enable_parallel_processing: bool = True,
    ):
        super().__init__()

        self.employee_id = employee_id
        self.checkpointer = checkpointer
        self.context = context
        self.tools = tools
        self.specification = specification
        self.langsmith_trace = langsmith_trace
        self.recursion_limit = recursion_limit
        self.enable_parallel_processing = enable_parallel_processing

        self.state_resolver = StateResolver(self.checkpointer, AgentState)

        # Initialize LLM models
        self.llm_config = get_llm_config()

        # Initialize parallel processing if enabled
        if self.enable_parallel_processing:
            self._init_parallel_processing()

    def get_graph_config(
        self, *, thread_id: str, **kwargs: dict[str, Any]
    ) -> RunnableConfig:
        callbacks = []
        if self.langsmith_trace and settings.LANGSMITH_API_KEY:
            tracer = LangChainTracer(
                project_name="Information Flows",
                client=Client(api_key=settings.LANGSMITH_API_KEY),
            )
            callbacks.append(tracer)
        return {
            "configurable": {
                "thread_id": thread_id,
                **kwargs,
            },
            "callbacks": callbacks,
            "recursion_limit": self.recursion_limit,
        }

    async def get_runtime_context(self) -> RuntimeContext:
        cheif_profile = await self.context.chief_profile()
        chief_relations = await self.context.chief_relations()
        return RuntimeContext(
            llm=self.llm_config,
            context_service=self.context,
            specification=self.specification,
            tools=self.tools,
            employee_id=self.employee_id,
            cheif_profile=cheif_profile,
            chief_relations=chief_relations,
        )

    async def detect_tasks(self, message: Message) -> AttentionState:
        """Analyze the message and return one or more tasks detected in it."""
        initial_state = await self.state_resolver.get_state(self.employee_id)

        attention_graph = create_attention_graph()
        config = self.get_graph_config(thread_id=self.employee_id)
        runtime_context = await self.get_runtime_context()

        attention_state = self.state_resolver.merge_states(
            AttentionState, initial_state, {"received_message": message}
        )

        attention_state: AttentionState = await attention_graph.ainvoke(
            attention_state,
            config=config,
            context=runtime_context,
        )
        return attention_state

    async def execute_task(
        self, task: multitasking.Task, initial_state: AgentState
    ) -> None:
        """Execute a task and merge its result into the global state."""
        executor_graph = create_executor_graph(self.tools)
        config = self.get_graph_config(thread_id=uuid4())
        runtime_context = await self.get_runtime_context()

        executor_state = self.state_resolver.merge_states(
            ExecutorState, initial_state, {"current_task": task}
        )

        executor_state: ExecutorState = await executor_graph.ainvoke(
            executor_state,
            config=config,
            context=runtime_context,
        )

        async with self.state_update_lock:
            if executor_state.get("completed_task"):
                await self.state_resolver.update_state(
                    self.employee_id,
                    {
                        "active_tasks": multitasking.RemoveTask(
                            task=executor_state["current_task"]
                        ),
                        "completed_tasks": [executor_state["completed_task"]],
                        "observations": executor_state["observations"],
                    },
                )
            else:
                await self.state_resolver.update_state(
                    self.employee_id,
                    {
                        "active_tasks": executor_state["current_task"],
                        "observations": executor_state["observations"],
                    },
                )

    async def run(self, message: Message) -> None:
        """
        Run the agent with the given message.

        If parallel processing is enabled, the message will be processed with
        parallel task execution. Otherwise, it will be processed sequentially.
        """
        if self.enable_parallel_processing:
            # Use parallel processing
            await self.process_message_with_parallel_tasks(message)
        else:
            # Use sequential processing
            await self._run_sequential(message)

    async def _run_sequential(self, message: Message) -> None:
        """Original sequential processing logic."""
        # 1. Analyze message and return detected tasks
        detection_state = await self.detect_tasks(message)

        # 2. Get detected tasks from the detection state (list of tuples)
        detected_tasks = detection_state.get("detected_tasks", [])

        # Extract just the task objects
        tasks_to_execute = [task for _, task in detected_tasks]

        # 3. Update state to persist new set of running tasks
        await self.state_resolver.update_state(
            self.employee_id,
            {
                "active_tasks": tasks_to_execute,
                "observations": detection_state["observations"],
            },
        )
        initial_state = await self.state_resolver.get_state(self.employee_id)

        # 4. Execute all tasks starting from the same state
        for task in tasks_to_execute:
            await self.execute_task(task, initial_state)

    async def start(self):
        """Start the agent and parallel processing if enabled."""
        if self.enable_parallel_processing:
            await self.start_parallel_processing()

    async def stop(self):
        """Stop the agent and parallel processing if enabled."""
        if self.enable_parallel_processing:
            await self.stop_parallel_processing()

        # Give event loop time to clean up pending tasks
        await asyncio.sleep(0.01)

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - ensure cleanup."""
        await self.stop()


class AgentFactory(BaseAgentFactory):
    def __init__(
        self,
        employee_id: UUID,
        specification_dir: str,
        mcp_servers: Optional[AgentMCPConfig] = None,
        langsmith_trace: bool = True,
        recursion_limit: int = 100,
    ):
        self.employee_id = employee_id
        self.specification_dir = specification_dir
        self.mcp_servers = mcp_servers or {}
        self.langsmith_trace = langsmith_trace
        self.recursion_limit = recursion_limit

    @asynccontextmanager
    async def context(self, checkpointer: BaseCheckpointSaver):
        specification = await Specification.from_folder(self.specification_dir)

        # Try to get MCP tools, but continue if connection fails
        mcp_tools = []
        mcp_client = None
        if self.mcp_servers:
            try:
                mcp_client = MultiServerMCPClient(self.mcp_servers)
                mcp_tools = await mcp_client.get_tools()
                print(
                    f"Successfully connected to MCP servers and loaded {len(mcp_tools)} tools"
                )
            except Exception as e:
                print(f"Warning: Failed to connect to MCP servers: {e}")
                print("Continuing without MCP tools...")

        tools = [*mcp_tools, *core_tools]
        context = AgentContextService(self.employee_id)
        agent = Agent(
            self.employee_id,
            checkpointer,
            context=context,
            tools=tools,
            specification=specification,
            langsmith_trace=self.langsmith_trace,
            recursion_limit=self.recursion_limit,
        )

        try:
            yield agent
        finally:
            await agent.stop()
