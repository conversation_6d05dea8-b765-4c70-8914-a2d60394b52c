"""
Parallel Processing Mixin - Provides true parallel message processing capabilities to agents.

This mixin enables agents to process tasks in parallel without blocking new messages.
Tasks are executed in the background using asyncio.create_task(), allowing new messages
to be processed immediately while previous tasks are still running.
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from uuid import UUID, uuid4

from information_flows.agents.core_agent.subsystems.multitasking import Task
from information_flows.core.types import Message
from information_flows.api.chat.chat_service import ChatService
from information_flows.database.session import AsyncSessionLocal

logger = logging.getLogger("parallel_processing_mixin")
logger.setLevel(logging.INFO)


@dataclass
class QueuedTask:
    """Represents a task that was queued because it was already running."""

    extracted_message: str
    task: Task
    original_message: Message

    def deferred_message(self) -> Message:
        """Use extracted part as a new message instead of the original message."""
        return self.original_message.model_copy(
            update={"content": self.extracted_message}
        )


class ParallelProcessingMixin:
    """
    Mixin that provides true parallel message processing capabilities to agents.

    Key features:
    - Non-blocking task execution using asyncio.create_task()
    - New messages are processed immediately without waiting
    - Duplicate tasks are queued and re-detected when the running task completes
    - State updates are synchronized with locks
    """

    def __init__(self, *args, **kwargs):
        """Initialize the mixin components."""
        super().__init__(*args, **kwargs)

        # Initialize parallel processing components
        self._init_parallel_processing()

    def _init_parallel_processing(self):
        """Initialize parallel processing components."""
        # Track running tasks by identifier
        self.running_tasks: Dict[str, asyncio.Task] = {}

        # Queue for tasks that couldn't run due to duplicates
        self.task_queue: asyncio.Queue[QueuedTask] = asyncio.Queue()

        # Lock for state updates
        self.state_update_lock = asyncio.Lock()

        # Background task for processing queued tasks
        self.queue_processor_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # Track task IDs for status updates
        self.task_db_ids: Dict[str, UUID] = {}
        
        # Chat service for persistence (lazy initialization)
        self._chat_service = None

        # Statistics
        self.parallel_stats = {
            "messages_received": 0,
            "tasks_detected": 0,
            "tasks_started": 0,
            "tasks_completed": 0,
            "tasks_queued": 0,
            "tasks_requeued": 0,
            "errors": 0,
        }
    
    @property
    def chat_service(self):
        """Lazy initialization of chat service."""
        if self._chat_service is None:
            self._chat_service = ChatService(AsyncSessionLocal)
        return self._chat_service

    async def start_parallel_processing(self):
        """Start the parallel processing components."""
        if self.is_running:
            return

        self.is_running = True
        # Start background queue processor
        self.queue_processor_task = asyncio.create_task(self._process_queue())
        logger.info("Parallel processing started")

    async def stop_parallel_processing(self):
        """Stop parallel processing and wait for all tasks to complete."""
        if not self.is_running:
            return

        logger.info("Stopping parallel processing")
        self.is_running = False

        # Cancel queue processor
        if self.queue_processor_task:
            self.queue_processor_task.cancel()
            try:
                await self.queue_processor_task
            except asyncio.CancelledError:
                pass

        # Wait for all running tasks to complete
        if self.running_tasks:
            logger.info(
                f"Waiting for {len(self.running_tasks)} running tasks to complete"
            )
            tasks = list(self.running_tasks.values())
            await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("Parallel processing stopped")

    # Create task wrapper that handles completion
    async def execute_and_cleanup(self, task: Task, initial_state):
        try:
            await self.execute_task(task, initial_state)
            self.parallel_stats["tasks_completed"] += 1
            logger.info(f"Task '{task.identifier}' completed successfully")
            
            # Update task status to completed
            if task.identifier in self.task_db_ids:
                try:
                    await self.chat_service.update_task_status(
                        task_id=self.task_db_ids[task.identifier],
                        status="completed"
                    )
                except Exception as e:
                    logger.debug(f"Could not update task status: {e}")
        except Exception as e:
            logger.error(
                f"Error executing task '{task.identifier}': {e}", exc_info=True
            )
            self.parallel_stats["errors"] += 1
            
            # Update task status to failed
            if task.identifier in self.task_db_ids:
                try:
                    await self.chat_service.update_task_status(
                        task_id=self.task_db_ids[task.identifier],
                        status="failed"
                    )
                except Exception as e:
                    logger.debug(f"Could not update task status: {e}")
        finally:
            # Remove from running tasks
            self.running_tasks.pop(task.identifier, None)
            # Process any queued tasks for this identifier
            await self._check_queue_for_task(task.identifier)

    async def process_message_with_parallel_tasks(self, message: Message) -> None:
        """
        Process a message with true parallel task execution.

        This method:
        1. Detects tasks under a lock
        2. Starts execution of non-duplicate tasks immediately (non-blocking)
        3. Queues duplicate tasks for later processing

        New messages can be processed while previous tasks are still running.
        """
        self.parallel_stats["messages_received"] += 1
        
        # Get or create chat_id
        chat_id = message.chat_id or uuid4()
        if message.chat_id is None:
            # Update message with generated chat_id
            message.chat_id = chat_id
            logger.info(f"Generated new chat_id: {chat_id}")
        
        # Try to persist chat and message, but don't fail if it doesn't work (e.g., in tests)
        try:
            # Ensure chat exists in database
            await self.chat_service.get_or_create_chat(
                employee_id=self.employee_id,
                chat_id=chat_id
            )
            
            # Add message to chat
            await self.chat_service.add_message_to_chat(chat_id, message)
        except Exception as e:
            logger.debug(f"Could not persist chat/message (may be in test environment): {e}")

        async with self.state_update_lock:
            # 1. Analyze message and detect tasks
            detection_state = await self.detect_tasks(message)
            detected_tasks = detection_state.get("detected_tasks", [])

            if not detected_tasks:
                logger.debug(
                    f"No tasks detected for message: {message.content[:50]}..."
                )
                return

            self.parallel_stats["tasks_detected"] += len(detected_tasks)

            # 2. Separate tasks into those that can run and those that must be queued
            tasks_to_execute: List[Tuple[str, Task]] = []
            tasks_to_queue: List[Tuple[str, Task]] = []

            for extracted_message, task in detected_tasks:
                if task.identifier in self.running_tasks:
                    # Task already running, queue it
                    tasks_to_queue.append((extracted_message, task))
                else:
                    # Task can run
                    tasks_to_execute.append((extracted_message, task))

            # 3. Create tasks in database (if possible)
            try:
                for _, task in tasks_to_execute:
                    task_db_id = await self.chat_service.create_task(
                        chat_id=chat_id,
                        task=task,
                        status="active"
                    )
                    self.task_db_ids[task.identifier] = task_db_id
                    
                for _, task in tasks_to_queue:
                    task_db_id = await self.chat_service.create_task(
                        chat_id=chat_id,
                        task=task,
                        status="queued"
                    )
                    self.task_db_ids[task.identifier] = task_db_id
            except Exception as e:
                logger.debug(f"Could not persist tasks (may be in test environment): {e}")

            # 4. Update state with new active tasks
            if tasks_to_execute:
                active_tasks = [task for _, task in tasks_to_execute]
                await self.state_resolver.update_state(
                    self.employee_id,
                    {
                        "active_tasks": active_tasks,
                        "observations": detection_state.get("observations", []),
                    },
                )

            # 5. Get current state for task execution
            initial_state = await self.state_resolver.get_state(self.employee_id)

        # 6. Start executing tasks in the background (non-blocking!)
        for extracted_message, task in tasks_to_execute:
            # Start task execution in background
            task_future = asyncio.create_task(
                self.execute_and_cleanup(task, initial_state)
            )
            self.running_tasks[task.identifier] = task_future
            self.parallel_stats["tasks_started"] += 1
            logger.info(f"Started task '{task.identifier}' in background")

        # 7. Queue tasks that couldn't run due to duplicates
        for extracted_message, task in tasks_to_queue:
            queued_task = QueuedTask(
                extracted_message=extracted_message, task=task, original_message=message
            )
            await self.task_queue.put(queued_task)
            self.parallel_stats["tasks_queued"] += 1
            logger.debug(f"Task '{task.identifier}' queued (already running)")

    async def _check_queue_for_task(self, task_identifier: str):
        """Check if there are queued tasks for the given identifier."""
        # Look through the queue for tasks with this identifier
        # This is called when a task completes
        temp_queue = []
        found_task = None

        # Empty the queue and look for matching task
        while not self.task_queue.empty():
            try:
                queued_task = self.task_queue.get_nowait()
                if queued_task.task.identifier == task_identifier and not found_task:
                    found_task = queued_task
                else:
                    temp_queue.append(queued_task)
            except asyncio.QueueEmpty:
                break

        # Put back non-matching tasks
        for task in temp_queue:
            await self.task_queue.put(task)

        # If we found a matching task, re-process the message
        if found_task:
            logger.info(f"Re-processing queued task '{task_identifier}'")
            self.parallel_stats["tasks_requeued"] += 1
            # Re-process the original message to detect and execute the task
            await self.process_message_with_parallel_tasks(
                found_task.deferred_message()
            )

    async def _process_queue(self):
        """Background task to periodically check for tasks that can now run."""
        while self.is_running:
            try:
                # Check if any queued tasks can now run
                temp_queue = []

                while not self.task_queue.empty():
                    try:
                        queued_task = self.task_queue.get_nowait()
                        # Check if this task can now run
                        if queued_task.task.identifier not in self.running_tasks:
                            # Re-process the message
                            logger.info(
                                f"Queue processor: re-processing task '{queued_task.task.identifier}'"
                            )
                            await self.process_message_with_parallel_tasks(
                                queued_task.deferred_message()
                            )
                        else:
                            # Still can't run, put it back
                            temp_queue.append(queued_task)
                    except asyncio.QueueEmpty:
                        break

                # Put back tasks that still can't run
                for task in temp_queue:
                    await self.task_queue.put(task)

                # Sleep before next check
                await asyncio.sleep(0.5)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in queue processor: {e}", exc_info=True)

    def get_parallel_stats(self) -> Dict[str, any]:
        """Get parallel processing statistics."""
        return {
            "running_tasks": list(self.running_tasks.keys()),
            "running_count": len(self.running_tasks),
            "queue_size": self.task_queue.qsize(),
            **self.parallel_stats,
        }

    async def wait_for_parallel_completion(self, timeout: Optional[float] = None):
        """Wait for all tasks to complete."""
        start_time = asyncio.get_event_loop().time()

        while self.running_tasks or not self.task_queue.empty():
            if timeout and (asyncio.get_event_loop().time() - start_time) > timeout:
                raise asyncio.TimeoutError(
                    f"Timeout waiting for completion after {timeout}s"
                )

            await asyncio.sleep(0.1)

        logger.info("All parallel tasks completed")
