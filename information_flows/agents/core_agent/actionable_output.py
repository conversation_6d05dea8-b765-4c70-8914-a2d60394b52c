import asyncio
import json
import pathlib
import tempfile
from concurrent.futures import Process<PERSON><PERSON><PERSON>xecutor
from typing import Any, Optional, TypedDict, Union
from uuid import uuid4

from datamodel_code_generator import (
    DataModelType,
    InputFileType,
    PythonVersion,
    generate,
)
from langchain_core.messages import AIMessage, ToolCall
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field, create_model
from pydantic.alias_generators import to_pascal

from information_flows.agents.core_agent.observations import ActionChoiceMessage


def generate_definition_script(
    json_schema: dict[str, Any], verbose: bool = False
) -> tuple[str, str]:
    """Generate a Python script defining Pydantic models from JSON schema."""
    with tempfile.NamedTemporaryFile(delete_on_close=False) as output_file:
        schema_name = to_pascal(json_schema["title"])
        output_path = pathlib.Path(output_file.name)
        generate(
            json.dumps(json_schema),
            input_file_type=InputFileType.JsonSchema,
            output_model_type=DataModelType.PydanticV2BaseModel,
            output=output_path,
            target_python_version=PythonVersion.PY_312,
            class_name=schema_name,
            use_schema_description=True,
            field_constraints=True,
        )
        script = output_path.read_text()
        if verbose:
            print("=" * 30 + f"\n{script}\n" + "=" * 30)
        return schema_name, script


def execute_definition_script(schema_name: str, script: str) -> type[BaseModel]:
    """Execute Python sccript defining Pydantic models and return the main model."""
    env_globals = {}
    exec(script, env_globals)
    for var_name, var_value in env_globals.items():
        if (
            issubclass(var_value, BaseModel)
            and var_value is not env_globals["BaseModel"]
        ):
            var_value.model_rebuild()
        if var_value is not env_globals[schema_name]:
            setattr(env_globals[schema_name], var_name, var_value)
    return env_globals[schema_name]


def json_schema_to_pydantic(tools: list[dict[str, Any]]) -> list[type[BaseModel]]:
    """Convert JSON schema tool args to Pydantic models."""
    with ProcessPoolExecutor(max_workers=4) as executor:
        results = executor.map(generate_definition_script, tools)
    models = []
    for schema_name, script in results:
        models.append(execute_definition_script(schema_name, script))
    return models


async def get_args_schemas(tools: list[BaseTool]) -> list[type[BaseModel]]:
    """Extract tool args Pydantic model, converting from JSON Schema if neccesary."""
    schemas = []
    require_conversion = []
    for tool in tools:
        if isinstance(tool.args_schema, dict):
            require_conversion.append(tool.args_schema)
        else:
            schemas.append(tool.args_schema)
    loop = asyncio.get_running_loop()
    models = await loop.run_in_executor(
        None, json_schema_to_pydantic, require_conversion
    )
    for model, tool in zip(models, tools):
        model.__name__ = tool.name
    schemas.extend(models)
    return schemas


class ToolChoicePlaceholder(BaseModel):
    """A special type to mark a field as a placeholder for a list of tools
    that a model has to choose from when performing structured tool calling."""


class MultiToolChoicePlaceholder(BaseModel):
    """A special type to mark a field as a placeholder for a list of tools
    that a model has to choose from when performing structured tool calling.
    Unlike an ordinary placeholder, a model can call multiple tools at once."""


class RawStructuredOutput[T: BaseModel](TypedDict):
    """A dictionary returned by .with_structured_output() in raw mode."""

    raw: AIMessage
    parsed: T
    parsing_error: Optional[Exception]


class CalledTool[T](BaseModel):
    tool_name: str
    tool_args: T

    @classmethod
    def model_parametrized_name(cls, params: tuple[type[Any], ...]) -> str:
        return params[0].__name__


type ToolChoice[T] = CalledTool[T] | list[CalledTool[T]]


class ActionableOutput(BaseModel):
    @classmethod
    async def with_tools(cls, tools: list[BaseTool]):
        """Replace the tool choice placeholder field with a field annotated
        with an actual list of tools. Modifies a target class at runtime."""
        args_schemas = await get_args_schemas(tools)
        tool_schemas = []
        for schema in args_schemas:
            tool_schemas.append(CalledTool[schema])
        tool_schemas = tuple(tool_schemas)

        placeholder_fields = {}
        for field, field_info in cls.model_fields.items():
            if field_info.annotation is ToolChoicePlaceholder:
                annotation = Union[tool_schemas]
                tool_choice = "single"
            elif field_info.annotation is MultiToolChoicePlaceholder:
                annotation = list[Union[tool_schemas]]
                tool_choice = "multiple"
            else:
                annotation = None
                tool_choice = None

            if annotation and tool_choice:
                placeholder_fields[field] = (
                    annotation,
                    Field(
                        description=field_info.description,
                        json_schema_extra={"tool_choice": tool_choice},
                    ),
                )

        if not placeholder_fields:
            raise ValueError(
                "a target class must have at least one tool choice placeholder field"
            )

        patched_cls = create_model(
            cls.__name__,
            **placeholder_fields,
            __doc__=cls.__doc__,
            __config__=getattr(cls, "__config__", None),
            __validators__=getattr(cls, "__validators__", None),
            __cls_kwargs__=getattr(cls, "__cls_kwargs__", None),
            __base__=cls,
        )
        return patched_cls

    @classmethod
    def to_action_message(cls, output: RawStructuredOutput) -> ActionChoiceMessage:
        """Convert raw structured output into AI message with tool calls."""
        tool_calls = []
        for field, field_info in cls.model_fields.items():
            if (
                field_info.json_schema_extra
                and "tool_choice" in field_info.json_schema_extra
            ):
                tool_choice: ToolChoice = getattr(output["parsed"], field)
                if isinstance(tool_choice, list):
                    # multiple tool calls simultaneously
                    for tool_call in tool_choice:
                        tool_calls.append(
                            ToolCall(
                                id=f"call_{uuid4().hex}",
                                name=tool_call.tool_name,
                                args=tool_call.tool_args.model_dump(mode="json"),
                                type="tool_call",
                            )
                        )
                else:
                    # single tool call
                    tool_calls.append(
                        ToolCall(
                            id=f"call_{uuid4().hex}",
                            name=tool_choice.tool_name,
                            args=tool_choice.tool_args.model_dump(mode="json"),
                            type="tool_call",
                        )
                    )

        if not tool_calls:
            raise ValueError("no tool choices found in the structured output")

        message_with_tool_calls = output["raw"].model_copy(
            update={
                "content": "",
                "tool_calls": tool_calls,
                "additional_kwargs": {
                    "refusal": output["raw"].additional_kwargs["refusal"],
                },
            }
        )
        return ActionChoiceMessage.from_ai_message(message_with_tool_calls)
