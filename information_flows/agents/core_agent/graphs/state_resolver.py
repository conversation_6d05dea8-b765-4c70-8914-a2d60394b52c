"""
Agent State Resolver - persists state of the agent to the database and handles state updates.
"""

from typing import TypedDict
from uuid import UUID

from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph


class State(TypedDict, total=False):
    pass


class StateResolver[S: State]:
    def __init__(self, checkpointer: BaseCheckpointSaver, state_schema: type[S]):
        schema_graph = StateGraph(state_schema).add_edge(START, END)
        self.schema_graph = schema_graph.compile(checkpointer)

    def graph_config(self, thread_id: str | UUID) -> RunnableConfig:
        """Return graph configuration identifying a state checkpoint."""
        return {"configurable": {"thread_id": str(thread_id)}}

    async def get_state(self, thread_id: str | UUID) -> S:
        """Return a current state of the agent from the database."""
        config = self.graph_config(thread_id)
        snapshot = await self.schema_graph.aget_state(config)
        return snapshot.values

    async def update_state(self, thread_id: str | UUID, partial_state: S) -> None:
        """Merge partial state with the current state using defined reducers."""
        config = self.graph_config(thread_id)
        await self.schema_graph.aupdate_state(config, partial_state)

    async def compare_states(self, state1: S, state2: S) -> bool:
        """Return True if the two states are equal, otherwise False."""
        return state1 == state2

    def merge_states[T](
        self, state_schema: type[T], current_state: S, partial_state: T
    ) -> T:
        """In-memory merging the partial and the current state according to the schema."""
        schema_graph = StateGraph(state_schema).add_edge(START, END)
        schema_graph = schema_graph.compile(MemorySaver())
        config = {"configurable": {"thread_id": "_"}}
        # record current state to the in-memory checkpoint
        schema_graph.update_state(config, current_state)
        # update current state with the partial state updates
        schema_graph.update_state(config, partial_state)
        return schema_graph.get_state(config).values
