import logging
from typing import Optional

from langgraph.checkpoint.base import Base<PERSON>heckpoint<PERSON>aver
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.runtime import Runtime

from information_flows.agents.core_agent import measurement
from information_flows.agents.core_agent.graphs.state import AgentState, RuntimeContext
from information_flows.agents.core_agent.observations import (
    Assistant<PERSON><PERSON><PERSON>,
    ChiefMessage,
)
from information_flows.agents.core_agent.prompts import load_memory_blueprint
from information_flows.agents.core_agent.subsystems import multitasking
from information_flows.core.agent import (
    get_telemetry_context,
    read_signal,
    record_measurement,
    send_event,
)
from information_flows.core.logger import agent_logger, format_object
from information_flows.core.types import Message
from information_flows.database.setup import async_engine as async_engine

logger = logging.getLogger("attention_graph")
logger.setLevel(logging.INFO)


class AttentionState(AgentState):
    received_message: Message
    detected_tasks: list[tuple[str, multitasking.Task]]


class PartialAttentionState(AttentionState, total=False):
    pass


async def perceive_input(
    state: AttentionState, runtime: Runtime[RuntimeContext]
) -> PartialAttentionState:
    """Convert the received message into an appropriate internal format."""
    with get_telemetry_context().tracer.start_as_current_span("perceive_input") as span:
        agent_logger.assistant_received(
            runtime.context.employee_id,
            state["received_message"].content,
            runtime.context.cheif_profile.name,
        )
        await record_measurement(
            measurement.PerceiveMessage(message=state["received_message"])
        )
        if state["received_message"].sender_role == "user":
            message = ChiefMessage(
                state["received_message"].content, runtime.context.cheif_profile.name
            )
        else:
            employee = await runtime.context.context_service.employee_profile(
                state["received_message"].sender_id
            )
            message = AssistantMessage(state["received_message"].content, employee.name)
        return {
            "observations": message,
        }


async def focus_attention(
    state: AttentionState, runtime: Runtime[RuntimeContext]
) -> PartialAttentionState:
    """Analyze the last observation and determine the current task to work on."""
    with get_telemetry_context().tracer.start_as_current_span(
        "focus_attention"
    ) as span:
        prompt = await load_memory_blueprint("focus_attention.jinja")
        chain = prompt | runtime.context.llm["focus_attention"].with_structured_output(
            multitasking.MessageAnalysis, method="json_schema", strict=True
        )
        analysis: multitasking.MessageAnalysis = await chain.ainvoke(
            {
                "specification": runtime.context.specification,
                "observations": state["observations"],
                "completed_tasks": state.get("completed_tasks"),
                "active_tasks": state.get("active_tasks"),
                "chief": runtime.context.cheif_profile,
                "relations": runtime.context.chief_relations,
            }
        )
        span.set_attribute("message_analysis", analysis.model_dump_json())

        agent_logger.assistant_internal(
            runtime.context.employee_id,
            f"Focus on task:\n{format_object(analysis)}",
            runtime.context.cheif_profile.name,
        )

        await record_measurement(measurement.FocusAttention(analysis=analysis))

        return {
            "detected_tasks": [
                (request.paraphrased_message, request.detected_task)
                for request in analysis.detected_requests
            ]
        }


def create_attention_graph(checkpointer: Optional[BaseCheckpointSaver] = None):
    graph_builder = StateGraph(AttentionState)
    graph_builder.add_node("perceive_input", perceive_input)
    graph_builder.add_node("focus_attention", focus_attention)
    graph_builder.add_edge(START, "perceive_input")
    graph_builder.add_edge("perceive_input", "focus_attention")
    graph_builder.add_edge("focus_attention", END)
    return graph_builder.compile(checkpointer or MemorySaver())
