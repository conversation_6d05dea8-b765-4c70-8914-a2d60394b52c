import logging
from typing import NotRequired, Optional

from langchain_core.tools import BaseTool
from langgraph.checkpoint.base import Base<PERSON>heckpointSaver
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import RemoveMessage
from langgraph.prebuilt import ToolNode
from langgraph.runtime import Runtime

from information_flows.agents.core_agent import measurement
from information_flows.agents.core_agent.graphs.state import AgentState, RuntimeContext
from information_flows.agents.core_agent.observations import (
    ActionChoiceMessage,
    ActionResultMessage,
)
from information_flows.agents.core_agent.prompts import load_memory_blueprint
from information_flows.agents.core_agent.subsystems import acting, multitasking
from information_flows.agents.core_agent.tools import EndTurnReason
from information_flows.core.agent import (
    get_telemetry_context,
    read_signal,
    record_measurement,
    send_event,
)
from information_flows.core.logger import agent_logger, format_object
from information_flows.core.telemetry import (
    telemetry_serialize_state,
    telemetry_serialize_tools,
)
from information_flows.core.types import Agent<PERSON>vent
from information_flows.database.setup import async_engine as async_engine

logger = logging.getLogger("executor_graph")
logger.setLevel(logging.INFO)


class ExecutorState(AgentState):
    current_task: multitasking.Task | multitasking.CompletedTask
    explanation: NotRequired[acting.Explanation]
    current_action_event: AgentEvent
    completed_task: NotRequired[multitasking.CompletedTask]


class PartialExecutorState(ExecutorState, total=False):
    pass


async def agent_setup(state: ExecutorState) -> None:
    get_telemetry_context().agent_turn_span.set_attribute(
        "start_state", telemetry_serialize_state(state, ExecutorState)
    )
    await send_event("agent_start")


async def choose_action(
    state: ExecutorState, runtime: Runtime[RuntimeContext]
) -> PartialExecutorState:
    """Choose the next action or actions based on previous observations."""
    with get_telemetry_context().tracer.start_as_current_span("choose_action") as span:
        event = await send_event(
            "action_start",
            value={"title": "Choose action", "content": "Choosing an action..."},
        )

        prompt = await load_memory_blueprint("choose_action.jinja")
        if state.get("explanation"):
            # It is not the first action, so we reflect on the previous one
            action_schema = await acting.ReflectAndChooseAction.with_tools(
                runtime.context.tools
            )
        else:
            # It is the first action and we have nothing to reflect on
            action_schema = await acting.ChooseAction.with_tools(runtime.context.tools)
        chain = prompt | runtime.context.llm["choose_action"].with_structured_output(
            action_schema, method="json_schema", strict=True, include_raw=True
        )

        result: acting.ChooseActionResult = await chain.ainvoke(
            {
                "specification": runtime.context.specification,
                "observations": state["observations"],
                "completed_tasks": state["completed_tasks"],
                "current_task": state["current_task"],
                "chief": runtime.context.cheif_profile,
                "relations": runtime.context.chief_relations,
                "explanation": state.get("explanation"),
            }
        )
        action_choice = result["parsed"]
        action_message = action_schema.to_action_message(result)

        if state.get("explanation"):
            # Record reflection on the previous action
            reflection = action_choice.get_reflection()
            span.set_attribute("reflection", reflection.model_dump_json())
            agent_logger.assistant_internal(
                runtime.context.employee_id,
                f"Reflection:\n{format_object(reflection)}",
                runtime.context.cheif_profile.name,
            )

        # Record explanation and reasoning about the next action
        explanation = action_choice.get_explanation()
        span.set_attribute("actions", telemetry_serialize_tools(action_message))
        span.set_attribute("explanation", explanation.model_dump_json())
        span.set_attribute("task_update", action_choice.updated_task.model_dump_json())
        await record_measurement(
            measurement.ChooseAction(actions=action_message.actions())
        )
        agent_logger.assistant_internal(
            runtime.context.employee_id,
            f"Explanation:\n{format_object(explanation)}",
            runtime.context.cheif_profile.name,
        )
        agent_logger.assistant_internal(
            runtime.context.employee_id,
            f"Updated task:\n{format_object(action_choice.updated_task)}",
            runtime.context.cheif_profile.name,
        )

        await send_event(
            "action_end",
            value={
                "title": "Choose action",
                "content": explanation.next_action_reasoning,
            },
            parent=event,
        )

        # Do not send an event about the action of ending the current turn
        tool_call = action_message.tool_calls[0]
        action_event = None
        if tool_call["name"] != "end_turn":
            action_event = await send_event(
                "action_start", value="Executing the action..."
            )
        return {
            "observations": action_message,
            "explanation": explanation,
            "current_task": action_choice.updated_task,
            "current_action_event": action_event,
        }


def route_actions(state: ExecutorState):
    last_message = state["observations"][-1]
    for tool_call in last_message.tool_calls:
        if tool_call["name"] == "end_turn":
            if len(last_message.tool_calls) != 1:
                raise ValueError("`end_turn` must be called alone")
            # The last and the only action is to end the turn
            return "end_turn"
    return "tools"


async def perceive_result(state: ExecutorState) -> ExecutorState:
    await send_event(
        event_type="action_end",
        value="Action completed!",
        parent=state["current_action_event"],
    )
    tool_message = state["observations"][-1]
    action_message = ActionResultMessage.from_tool_message(tool_message)
    await record_measurement(measurement.PerceiveResult(result=action_message.result()))
    return {
        "observations": [RemoveMessage(id=tool_message.id), action_message],
    }


async def end_turn(
    state: ExecutorState, runtime: Runtime[RuntimeContext]
) -> PartialExecutorState:
    """Complete the current turn with with `end_turn` tool call."""
    with get_telemetry_context().tracer.start_as_current_span("end_turn") as span:
        last_message: ActionChoiceMessage = state["observations"][-1]
        end_turn_call = last_message.tool_calls[0]
        reason: EndTurnReason = end_turn_call["args"]["reason"]
        agent_logger.assistant_internal(
            runtime.context.employee_id,
            f"End turn reason: {reason.upper()}",
            runtime.context.cheif_profile.name,
        )
        span.set_attribute("reason", reason)

        output_state = {"observations": RemoveMessage(id=last_message.id)}
        if reason == "task_completed":
            completed_task = multitasking.CompletedTask(task=state["current_task"])
            output_state.update({"completed_task": completed_task})
        return output_state


async def agent_teardown(state: ExecutorState) -> None:
    get_telemetry_context().agent_turn_span.set_attribute(
        "end_state", telemetry_serialize_state(state, ExecutorState)
    )
    await send_event("agent_end")


def create_executor_graph(
    tools: list[BaseTool],
    checkpointer: Optional[BaseCheckpointSaver] = None,
):
    graph_builder = StateGraph(ExecutorState)
    graph_builder.add_node("agent_setup", agent_setup)
    graph_builder.add_node("choose_action", choose_action)
    graph_builder.add_node("tools", ToolNode(tools, messages_key="observations"))
    graph_builder.add_node("perceive_result", perceive_result)
    graph_builder.add_node("end_turn", end_turn)
    graph_builder.add_node("agent_teardown", agent_teardown)
    graph_builder.add_edge(START, "agent_setup")
    graph_builder.add_edge("agent_setup", "choose_action")
    graph_builder.add_conditional_edges(
        "choose_action", route_actions, ["tools", "end_turn"]
    )
    graph_builder.add_edge("tools", "perceive_result")
    graph_builder.add_edge("perceive_result", "choose_action")
    graph_builder.add_edge("end_turn", "agent_teardown")
    graph_builder.add_edge("agent_teardown", END)
    return graph_builder.compile(checkpointer or MemorySaver())
