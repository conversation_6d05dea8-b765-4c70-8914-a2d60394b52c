ENVIRONMENT=production

TIMEZONE=Europe/Kyiv

HOST=localhost
PORT=45454

FRONTEND_PORT=52525

POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DATABASE=

SQL_DEMO_DATABASE=
SQL_MODEL_NAME=gpt-4o
SQL_FILE_PATH=information_flows/agents/core_agent/mcp/text2pandas/dml.sql

SEARCH_MODEL_NAME=gpt-4o-mini
POLICY_DIR=./policy_data

OPENAI_API_KEY=

CLICKUP_API_TOKEN=
CLICKUP_LIST_ID=
CLICKUP_TEAM_ID=

OPENTELEMETRY_COLLECTOR_ENDPOINT=http://host.docker.internal:4317

TELEMETRY_CORRELATION_ID=<your_company_code>
