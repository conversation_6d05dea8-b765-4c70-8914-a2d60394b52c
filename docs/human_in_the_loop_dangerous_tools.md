# Human-in-the-Loop for Dangerous Tool Execution

## Overview

This document outlines the implementation approach for adding human confirmation requirements for dangerous tool executions in the agent system. The solution allows the agent to pause specific tasks that require approval while continuing to process other tasks in parallel.

## Architecture

### 1. Dangerous Tools Configuration

Create a centralized configuration for managing dangerous tools:

```python
# information_flows/agents/core_agent/dangerous_tools.py
from dataclasses import dataclass, field
from typing import Set, Dict, Optional
import re

@dataclass
class DangerousToolsConfig:
    """Configuration for tools requiring human approval."""
    
    # Exact tool names requiring approval
    dangerous_tools: Set[str] = field(default_factory=lambda: {
        # ClickUp operations
        "submit_vacation",
        "approve_task",
        "create_task",
        
        # Database mutations
        "execute_sql_mutation",
        "delete_records",
        
        # Time tracking
        "track_time",
        "submit_timesheet",
        
        # Communication
        "send_bulk_email",
        "notify_all_employees"
    })
    
    # Regex patterns for dynamic matching
    dangerous_patterns: list[str] = field(default_factory=lambda: [
        r".*_create_.*",
        r".*_delete_.*",
        r".*_mutate_.*",
        r".*_approve_.*"
    ])
    
    # Optional custom approval messages per tool
    approval_messages: Dict[str, str] = field(default_factory=dict)
    
    def is_dangerous(self, tool_name: str) -> bool:
        """Check if a tool requires approval."""
        # Check exact matches
        if tool_name in self.dangerous_tools:
            return True
            
        # Check patterns
        for pattern in self.dangerous_patterns:
            if re.match(pattern, tool_name):
                return True
                
        return False
    
    def get_approval_message(self, tool_name: str, tool_args: dict) -> str:
        """Generate approval request message for a tool."""
        if tool_name in self.approval_messages:
            return self.approval_messages[tool_name].format(**tool_args)
            
        # Default message
        args_str = "\n".join([f"  - {k}: {v}" for k, v in tool_args.items()])
        return f"🔐 **Approval Required**\n\nThe assistant wants to execute a potentially dangerous action:\n\n**Tool:** `{tool_name}`\n**Arguments:**\n{args_str}\n\nPlease reply with:\n- `/approve {'{approval_id}'}` to allow this action\n- `/reject {'{approval_id}'}` to deny this action"
```

### 2. Approval Request Storage

Extend the database schema to track approval requests:

```python
# information_flows/database/approval.py
from sqlalchemy import Column, String, DateTime, JSON, Enum, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid
import enum

from information_flows.database.base import Base

class ApprovalStatus(str, enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"

class ApprovalRequest(Base):
    __tablename__ = "approval_requests"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    chat_id = Column(UUID(as_uuid=True), ForeignKey("chats.id"), nullable=False)
    
    tool_name = Column(String, nullable=False)
    tool_args = Column(JSON, nullable=False)
    
    status = Column(Enum(ApprovalStatus), default=ApprovalStatus.PENDING)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    responded_at = Column(DateTime, nullable=True)
    
    # Store the complete task state for resumption
    task_state = Column(JSON, nullable=False)
    
    # User feedback
    rejection_reason = Column(String, nullable=True)
```

### 3. Enhanced ToolNode with Approval Checks

Create an intercepting ToolNode that checks for dangerous tools:

```python
# information_flows/agents/core_agent/approval_tool_node.py
from typing import Any, Dict, List, Optional
from uuid import UUID
import asyncio
from langgraph.prebuilt import ToolNode
from langchain_core.tools import BaseTool

from information_flows.agents.core_agent.dangerous_tools import DangerousToolsConfig
from information_flows.core.agent import send_message
from information_flows.api.chat.chat_service import ChatService
from information_flows.database.session import AsyncSessionLocal

class ApprovalRequiredToolNode(ToolNode):
    """ToolNode that intercepts dangerous tool calls for approval."""
    
    def __init__(
        self, 
        tools: List[BaseTool],
        dangerous_config: DangerousToolsConfig,
        messages_key: str = "observations"
    ):
        super().__init__(tools, messages_key)
        self.dangerous_config = dangerous_config
        self.chat_service = ChatService(AsyncSessionLocal)
        
    async def __call__(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Intercept tool calls and check for dangerous operations."""
        last_message = state[self.messages_key][-1]
        
        # Check each tool call
        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            
            if self.dangerous_config.is_dangerous(tool_name):
                # This is a dangerous tool - request approval
                return await self._request_approval(state, config, tool_call)
        
        # No dangerous tools - proceed normally
        return await super().__call__(state, config)
    
    async def _request_approval(
        self, 
        state: Dict[str, Any], 
        config: Dict[str, Any],
        tool_call: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Request approval for a dangerous tool."""
        from information_flows.agents.core_agent.observations import ToolMessage
        
        # Get context from state
        employee_id = config["configurable"]["employee_id"]
        chat_id = state.get("chat_id")
        current_task = state.get("current_task")
        
        # Create approval request in database
        approval_id = await self.chat_service.create_approval_request(
            task_id=current_task.identifier if current_task else None,
            chat_id=chat_id,
            tool_name=tool_call["name"],
            tool_args=tool_call["args"],
            task_state=state  # Store complete state for resumption
        )
        
        # Send approval request message to user
        approval_message = self.dangerous_config.get_approval_message(
            tool_call["name"], 
            tool_call["args"]
        ).format(approval_id=approval_id)
        
        await send_message(approval_message, receiver_id=employee_id)
        
        # Update task status to pending_approval
        if current_task:
            await self.chat_service.update_task_status(
                task_id=current_task.identifier,
                status="pending_approval"
            )
        
        # Return a tool message indicating approval is required
        tool_message = ToolMessage(
            content=f"⏸️ Action requires approval. Request ID: {approval_id}. Waiting for user confirmation...",
            tool_call_id=tool_call["id"],
            name=tool_call["name"],
            artifact={"approval_required": True, "approval_id": str(approval_id)}
        )
        
        return {
            self.messages_key: [tool_message],
            "pending_approval": approval_id,
            "current_task": current_task._replace(status="pending_approval") if current_task else None
        }
```

### 4. Parallel Processing Integration

Enhance the ParallelProcessingMixin to handle approval states:

```python
# Add to information_flows/agents/core_agent/parallel_processing_mixin.py

class EnhancedParallelProcessingMixin(ParallelProcessingMixin):
    """Extended mixin that handles approval states."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.approval_service = ApprovalService()
        
    async def process_approval_response(self, message: Message) -> None:
        """Handle approval/rejection messages from users."""
        content = message.content.strip()
        
        if content.startswith("/approve "):
            approval_id = content.split()[1]
            await self._handle_approval(approval_id, approved=True)
            
        elif content.startswith("/reject "):
            parts = content.split(maxsplit=2)
            approval_id = parts[1]
            reason = parts[2] if len(parts) > 2 else None
            await self._handle_approval(approval_id, approved=False, reason=reason)
    
    async def _handle_approval(
        self, 
        approval_id: str, 
        approved: bool,
        reason: Optional[str] = None
    ) -> None:
        """Process an approval decision."""
        # Get approval request
        approval_request = await self.approval_service.get_approval_request(approval_id)
        
        if not approval_request or approval_request.status != "pending":
            # Send error message
            await send_message(
                f"❌ Invalid or expired approval request: {approval_id}",
                receiver_id=self.employee_id
            )
            return
        
        # Update approval status
        await self.approval_service.update_approval_status(
            approval_id, 
            "approved" if approved else "rejected",
            reason=reason
        )
        
        if approved:
            # Resume task execution
            await self._resume_task_after_approval(approval_request)
        else:
            # Update task to failed/cancelled
            await self.chat_service.update_task_status(
                task_id=approval_request.task_id,
                status="cancelled"
            )
            
            # Notify agent of rejection
            rejection_msg = f"❌ Tool execution was rejected by user"
            if reason:
                rejection_msg += f": {reason}"
                
            await send_message(rejection_msg, receiver_id=self.employee_id)
    
    async def _resume_task_after_approval(self, approval_request) -> None:
        """Resume a task after approval is granted."""
        # Restore task state
        restored_state = approval_request.task_state
        
        # Update task status back to active
        await self.chat_service.update_task_status(
            task_id=approval_request.task_id,
            status="active"
        )
        
        # Create a synthetic message to trigger tool execution
        tool_execution_message = Message(
            content=f"Approval granted for {approval_request.tool_name}",
            metadata={
                "approved_tool": approval_request.tool_name,
                "approved_args": approval_request.tool_args,
                "approval_id": str(approval_request.id)
            }
        )
        
        # Re-execute the task with the approved tool
        await self.execute_approved_tool(
            task_id=approval_request.task_id,
            tool_name=approval_request.tool_name,
            tool_args=approval_request.tool_args,
            state=restored_state
        )
```

### 5. Modified Agent Graph

Update the agent graph to use the approval-aware ToolNode:

```python
# Modify information_flows/agents/core_agent/graph.py

def create_agent_graph_with_approval(
    checkpointer: BaseCheckpointSaver, 
    tools: list[BaseTool],
    dangerous_config: Optional[DangerousToolsConfig] = None
):
    """Create agent graph with approval support for dangerous tools."""
    if dangerous_config is None:
        dangerous_config = DangerousToolsConfig()
    
    graph_builder = StateGraph(AgentState)
    
    # ... existing nodes ...
    
    # Replace standard ToolNode with ApprovalRequiredToolNode
    graph_builder.add_node(
        "tools", 
        ApprovalRequiredToolNode(
            tools, 
            dangerous_config,
            messages_key="observations"
        )
    )
    
    # Add approval handling edge
    def route_tool_response(state: AgentState):
        """Route based on tool execution result."""
        last_message = state["observations"][-1]
        if hasattr(last_message, "artifact") and last_message.artifact.get("approval_required"):
            return "await_approval"
        return "perceive_result"
    
    graph_builder.add_conditional_edges(
        "tools",
        route_tool_response,
        ["perceive_result", "await_approval"]
    )
    
    # Add await_approval node
    graph_builder.add_node("await_approval", await_approval_node)
    graph_builder.add_edge("await_approval", "end_turn")
    
    # ... rest of graph construction ...
    
    return graph_builder.compile(checkpointer)
```

### 6. Approval Service

Create a dedicated service for managing approvals:

```python
# information_flows/services/approval_service.py
from typing import Optional
from uuid import UUID
from datetime import datetime, timedelta

from information_flows.database.approval import ApprovalRequest, ApprovalStatus
from information_flows.database.session import AsyncSessionLocal

class ApprovalService:
    """Service for managing tool approval requests."""
    
    def __init__(self, session_factory=AsyncSessionLocal):
        self.session_factory = session_factory
        
    async def create_approval_request(
        self,
        task_id: UUID,
        chat_id: UUID,
        tool_name: str,
        tool_args: dict,
        task_state: dict
    ) -> UUID:
        """Create a new approval request."""
        async with self.session_factory() as session:
            approval = ApprovalRequest(
                task_id=task_id,
                chat_id=chat_id,
                tool_name=tool_name,
                tool_args=tool_args,
                task_state=task_state,
                status=ApprovalStatus.PENDING
            )
            session.add(approval)
            await session.commit()
            return approval.id
    
    async def get_approval_request(self, approval_id: str) -> Optional[ApprovalRequest]:
        """Get an approval request by ID."""
        async with self.session_factory() as session:
            return await session.get(ApprovalRequest, UUID(approval_id))
    
    async def update_approval_status(
        self,
        approval_id: str,
        status: str,
        reason: Optional[str] = None
    ) -> None:
        """Update the status of an approval request."""
        async with self.session_factory() as session:
            approval = await session.get(ApprovalRequest, UUID(approval_id))
            if approval:
                approval.status = ApprovalStatus(status)
                approval.responded_at = datetime.utcnow()
                if reason:
                    approval.rejection_reason = reason
                await session.commit()
    
    async def expire_old_requests(self, timeout_hours: int = 24) -> None:
        """Expire pending requests older than timeout."""
        async with self.session_factory() as session:
            cutoff = datetime.utcnow() - timedelta(hours=timeout_hours)
            
            # Update old pending requests to expired
            await session.execute(
                update(ApprovalRequest)
                .where(
                    ApprovalRequest.status == ApprovalStatus.PENDING,
                    ApprovalRequest.created_at < cutoff
                )
                .values(status=ApprovalStatus.EXPIRED)
            )
            await session.commit()
```

## Usage Example

### 1. Configuration

```python
# In your agent initialization
dangerous_config = DangerousToolsConfig(
    dangerous_tools={
        "submit_vacation",
        "create_clickup_task",
        "update_database"
    },
    approval_messages={
        "submit_vacation": "The assistant wants to submit a vacation request for {name} from {start_date_unix} to {due_date_unix}. Approve?"
    }
)

# Create agent with approval support
agent_graph = create_agent_graph_with_approval(
    checkpointer=checkpointer,
    tools=all_tools,
    dangerous_config=dangerous_config
)
```

### 2. User Interaction Flow

1. **Agent attempts dangerous action:**
   ```
   User: "Create a vacation request for next week"
   Agent: "I'll create a vacation request for you..."
   ```

2. **System intercepts and requests approval:**
   ```
   System: 🔐 **Approval Required**
   
   The assistant wants to execute a potentially dangerous action:
   
   **Tool:** `submit_vacation`
   **Arguments:**
     - name: John Doe Vacation
     - start_date_unix: 2024-01-15
     - due_date_unix: 2024-01-19
     - requester_id: 123
     - approver_id: 456
   
   Please reply with:
   - `/approve abc-123` to allow this action
   - `/reject abc-123` to deny this action
   ```

3. **User responds:**
   ```
   User: /approve abc-123
   ```

4. **System resumes execution:**
   ```
   Agent: ✅ Vacation request has been successfully submitted. Task ID: xyz-789
   ```

### 3. Testing Dangerous Tools

```python
# testing/core_agent/test_dangerous_tools_approval.py
import pytest
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_dangerous_tool_requires_approval():
    """Test that dangerous tools trigger approval requests."""
    # Setup
    dangerous_config = DangerousToolsConfig(
        dangerous_tools={"dangerous_action"}
    )
    
    # Create mock tool
    dangerous_tool = AsyncMock()
    dangerous_tool.name = "dangerous_action"
    
    # Create approval node
    approval_node = ApprovalRequiredToolNode(
        [dangerous_tool],
        dangerous_config
    )
    
    # Mock state with tool call
    state = {
        "observations": [
            MockMessage(tool_calls=[
                {"name": "dangerous_action", "args": {"param": "value"}, "id": "123"}
            ])
        ],
        "current_task": Task(identifier="task-1", description="Test task")
    }
    
    # Execute
    result = await approval_node(state, {"configurable": {"employee_id": "emp-1"}})
    
    # Verify approval was requested
    assert result["pending_approval"] is not None
    assert result["observations"][0].artifact["approval_required"] is True
    
    # Verify tool was NOT executed
    dangerous_tool.assert_not_called()
```

## Benefits

1. **Non-blocking**: Other tasks continue processing while waiting for approval
2. **Configurable**: Easy to add/remove dangerous tools
3. **Auditable**: All approval requests are logged in the database
4. **User-friendly**: Clear approval messages with context
5. **Resumable**: Task state is preserved and restored after approval
6. **Integrated**: Works with existing messaging and parallel processing systems

## Implementation Checklist

- [ ] Create database migration for approval_requests table
- [ ] Implement DangerousToolsConfig class
- [ ] Create ApprovalRequiredToolNode
- [ ] Implement ApprovalService
- [ ] Update ParallelProcessingMixin for approval handling
- [ ] Modify agent graph to include approval flow
- [ ] Add approval command handlers to message processing
- [ ] Create tests for approval flow
- [ ] Update UI to show pending approvals
- [ ] Add configuration for tool danger levels
- [ ] Implement approval expiration logic
- [ ] Add monitoring/metrics for approval requests
