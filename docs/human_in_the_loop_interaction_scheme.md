# Human-in-the-Loop Dangerous Tools: Interaction Scheme

## Overview

This document provides a visual representation of the interaction flow for the human-in-the-loop dangerous tools approval system, showing how the system handles both safe and dangerous tool executions while maintaining parallel processing capabilities.

## System Components Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           INFORMATION FLOWS SYSTEM                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────┐ │
│  │   User Input    │    │  Message Queue   │    │    Dynamic Worker Pool      │ │
│  │                 │    │                  │    │                             │ │
│  │ • Chat Messages │───▶│ • Classification │───▶│ • Task-based Routing        │ │
│  │ • Approval Cmds │    │ • Prioritization │    │ • Parallel Execution        │ │
│  │ • Tool Requests │    │ • Queue Mgmt     │    │ • State Management          │ │
│  └─────────────────┘    └──────────────────┘    └─────────────────────────────┘ │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        APPROVAL SYSTEM CORE                                │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │ │
│  │  │ Dangerous Tools │  │ Approval Tool   │  │    Approval Database        │ │ │
│  │  │   Config        │  │     Node        │  │                             │ │ │
│  │  │                 │  │                 │  │ • Pending Requests          │ │ │
│  │  │ • Tool Names    │  │ • Intercepts    │  │ • Approval History          │ │ │
│  │  │ • Patterns      │  │ • Validates     │  │ • Task State Storage        │ │ │
│  │  │ • Messages      │  │ • Routes        │  │ • User Responses            │ │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Detailed Interaction Flow

### 1. Message Processing Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            MESSAGE PROCESSING FLOW                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. User Message Arrives                                                        │
│     ┌─────────────────┐                                                         │
│     │ "Create vacation │                                                         │
│     │ request for      │                                                         │
│     │ next week"       │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. Task Classification                                                         │
│     ┌─────────────────┐                                                         │
│     │ TaskClassifier  │                                                         │
│     │ determines:     │                                                         │
│     │ task_name =     │                                                         │
│     │ "vacation_mgmt" │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. Dynamic Worker Check                                                        │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ Is task already │────▶│ YES: Requeue    │                                │
│     │ in progress?    │     │ message, retry  │                                │
│     └─────────┬───────┘     │ later           │                                │
│               │             └─────────────────┘                                │
│               ▼ NO                                                              │
│  4. Create Worker Task                                                          │
│     ┌─────────────────┐                                                         │
│     │ Mark task as    │                                                         │
│     │ IN_PROGRESS     │                                                         │
│     │ Create async    │                                                         │
│     │ worker task     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  5. Agent Processing                                                            │
│     ┌─────────────────┐                                                         │
│     │ Agent analyzes  │                                                         │
│     │ message and     │                                                         │
│     │ determines tool │                                                         │
│     │ calls needed    │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  6. Tool Execution Check                                                        │
│     ┌─────────────────┐                                                         │
│     │ ApprovalRequired│                                                         │
│     │ ToolNode checks │                                                         │
│     │ if tools are    │                                                         │
│     │ dangerous       │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  7. Route Based on Safety                                                       │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ SAFE TOOLS      │     │ DANGEROUS TOOLS │                                │
│     │ Execute         │     │ Request         │                                │
│     │ immediately     │     │ Approval        │                                │
│     └─────────────────┘     └─────────────────┘                                │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 2. Approval Request Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           APPROVAL REQUEST FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. Dangerous Tool Detected                                                     │
│     ┌─────────────────┐                                                         │
│     │ Tool: submit_   │                                                         │
│     │ vacation        │                                                         │
│     │ Args: {employee,│                                                         │
│     │ start_date, ... }│                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. Create Approval Request                                                     │
│     ┌─────────────────┐                                                         │
│     │ ApprovalService │                                                         │
│     │ creates DB      │                                                         │
│     │ record with:    │                                                         │
│     │ • approval_id   │                                                         │
│     │ • task_state    │                                                         │
│     │ • tool_info     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. Send Approval Message                                                       │
│     ┌─────────────────┐                                                         │
│     │ 🔐 **Approval   │                                                         │
│     │ Required**      │                                                         │
│     │                 │                                                         │
│     │ Tool: submit_   │                                                         │
│     │ vacation        │                                                         │
│     │ Args: ...       │                                                         │
│     │                 │                                                         │
│     │ /approve abc123 │                                                         │
│     │ /reject abc123  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  4. Update Task Status                                                          │
│     ┌─────────────────┐                                                         │
│     │ Task status →   │                                                         │
│     │ "pending_       │                                                         │
│     │ approval"       │                                                         │
│     │                 │                                                         │
│     │ Worker marked   │                                                         │
│     │ as IDLE         │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  5. System Continues Processing Other Tasks                                     │
│     ┌─────────────────┐                                                         │
│     │ Other messages  │                                                         │
│     │ and tasks can   │                                                         │
│     │ be processed    │                                                         │
│     │ in parallel     │                                                         │
│     └─────────────────┘                                                         │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 3. User Response Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            USER RESPONSE FLOW                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  User Response Options:                                                         │
│                                                                                 │
│  ┌─────────────────┐                    ┌─────────────────┐                    │
│  │ /approve abc123 │                    │ /reject abc123  │                    │
│  │                 │                    │ [reason]        │                    │
│  └─────────┬───────┘                    └─────────┬───────┘                    │
│            │                                      │                            │
│            ▼                                      ▼                            │
│  ┌─────────────────┐                    ┌─────────────────┐                    │
│  │ APPROVAL PATH   │                    │ REJECTION PATH  │                    │
│  └─────────┬───────┘                    └─────────┬───────┘                    │
│            │                                      │                            │
│            ▼                                      ▼                            │
│  1. Validate Request                    1. Update Status                       │
│     ┌─────────────────┐                    ┌─────────────────┐                │
│     │ • Check ID      │                    │ • Mark rejected │                │
│     │ • Verify status │                    │ • Store reason  │                │
│     │ • Get task_state│                    │ • Update task   │                │
│     └─────────┬───────┘                    └─────────┬───────┘                │
│               │                                      │                        │
│               ▼                                      ▼                        │
│  2. Update Approval                     2. Cancel Task                         │
│     ┌─────────────────┐                    ┌─────────────────┐                │
│     │ • Status →      │                    │ • Task status → │                │
│     │   "approved"    │                    │   "cancelled"   │                │
│     │ • responded_at  │                    │ • Notify user   │                │
│     └─────────┬───────┘                    └─────────────────┘                │
│               │                                                               │
│               ▼                                                               │
│  3. Restore Task State                                                         │
│     ┌─────────────────┐                                                        │
│     │ • Load saved    │                                                        │
│     │   task_state    │                                                        │
│     │ • Task status → │                                                        │
│     │   "active"      │                                                        │
│     └─────────┬───────┘                                                        │
│               │                                                               │
│               ▼                                                               │
│  4. Execute Approved Tool                                                      │
│     ┌─────────────────┐                                                        │
│     │ • Run original  │                                                        │
│     │   tool with     │                                                        │
│     │   saved args    │                                                        │
│     │ • Complete task │                                                        │
│     └─────────────────┘                                                        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Parallel Processing Visualization

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         PARALLEL PROCESSING EXAMPLE                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  Timeline: ────────────────────────────────────────────────────────────────▶    │
│                                                                                 │
│  Task A (Safe):     [■■■■■■■■■■] ✅ Completed                                   │
│  Task B (Dangerous): [■■■■■] ⏸️  [■■■■■] ✅ Completed after approval           │
│  Task C (Safe):       [■■■■■■■■■■] ✅ Completed                                 │
│  Task D (Safe):         [■■■■■■■■■■] ✅ Completed                               │
│                                                                                 │
│  Key:                                                                           │
│  ■ = Processing    ⏸️ = Waiting for approval    ✅ = Completed                  │
│                                                                                 │
│  Notice how Tasks A, C, and D continue processing while Task B waits for       │
│  approval. The system maintains full parallelism for non-dangerous operations. │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## State Management Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           STATE MANAGEMENT FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. Initial State Capture                                                       │
│     ┌─────────────────┐                                                         │
│     │ Before tool     │                                                         │
│     │ execution:      │                                                         │
│     │ • Get current   │                                                         │
│     │   state from DB │                                                         │
│     │ • Store in      │                                                         │
│     │   approval req  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. State Preservation During Approval                                          │
│     ┌─────────────────┐                                                         │
│     │ ApprovalRequest │                                                         │
│     │ stores:         │                                                         │
│     │ • Complete      │                                                         │
│     │   task_state    │                                                         │
│     │ • Tool context  │                                                         │
│     │ • Message chain │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. State Restoration After Approval                                            │
│     ┌─────────────────┐                                                         │
│     │ On approval:    │                                                         │
│     │ • Restore saved │                                                         │
│     │   state         │                                                         │
│     │ • Check for     │                                                         │
│     │   conflicts     │                                                         │
│     │ • Merge if      │                                                         │
│     │   necessary     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  4. Conflict Resolution                                                          │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ State unchanged │     │ State changed   │                                │
│     │ during approval │     │ during approval │                                │
│     │ ↓               │     │ ↓               │                                │
│     │ Direct restore  │     │ Merge states    │                                │
│     └─────────────────┘     └─────────────────┘                                │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Non-Blocking Implementation Details

### How the System Waits for User Input Non-Blockingly

The key to non-blocking approval is that **the approval-aware ToolNode is NOT used on every step**. Instead, it's strategically placed only at the tool execution point in the agent graph. Here's how it works:

#### 1. **Message Processing Architecture**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        NON-BLOCKING MESSAGE FLOW                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  WebSocket Connection (Real-time)                                               │
│  ┌─────────────────┐                                                            │
│  │ User sends:     │                                                            │
│  │ • Chat messages │                                                            │
│  │ • /approve cmd  │                                                            │
│  │ • /reject cmd   │                                                            │
│  └─────────┬───────┘                                                            │
│            │                                                                    │
│            ▼                                                                    │
│  ┌─────────────────┐                                                            │
│  │ AgentPool       │                                                            │
│  │ Message Router  │                                                            │
│  │ • Routes to     │                                                            │
│  │   correct agent │                                                            │
│  │ • Queues msgs   │                                                            │
│  └─────────┬───────┘                                                            │
│            │                                                                    │
│            ▼                                                                    │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐           │
│  │ Agent 1         │     │ Agent 2         │     │ Agent N         │           │
│  │ (Employee A)    │     │ (Employee B)    │     │ (Employee N)    │           │
│  │                 │     │                 │     │                 │           │
│  │ • Processes     │     │ • Processes     │     │ • Processes     │           │
│  │   messages      │     │   messages      │     │   messages      │           │
│  │ • Runs in       │     │ • Runs in       │     │ • Runs in       │           │
│  │   parallel      │     │   parallel      │     │   parallel      │           │
│  └─────────────────┘     └─────────────────┘     └─────────────────┘           │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 2. **Agent Graph with Approval Integration**

The ApprovalRequiredToolNode is used **only** at the "tools" step:

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           AGENT EXECUTION GRAPH                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  START                                                                          │
│    │                                                                           │
│    ▼                                                                           │
│  ┌─────────────────┐                                                           │
│  │ agent_setup     │                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐                                                           │
│  │ perceive_input  │ ◄─── New message arrives here                            │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐                                                           │
│  │ focus_attention │                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐                                                           │
│  │ choose_action   │                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐ ◄─── APPROVAL-AWARE TOOLNODE ONLY HERE                  │
│  │ tools           │                                                           │
│  │ (Approval-Aware)│                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐                                                           │
│  │ perceive_result │                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  ┌─────────────────┐                                                           │
│  │ end_turn        │                                                           │
│  └─────────┬───────┘                                                           │
│            │                                                                   │
│            ▼                                                                   │
│  END                                                                            │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 3. **Non-Blocking Approval Mechanism**

When a dangerous tool is detected:

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      NON-BLOCKING APPROVAL FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. Agent reaches "tools" node with dangerous tool call                        │
│     ┌─────────────────┐                                                         │
│     │ ApprovalRequired│                                                         │
│     │ ToolNode detects│                                                         │
│     │ dangerous tool  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. Create approval request & save complete state                               │
│     ┌─────────────────┐                                                         │
│     │ • Store task    │                                                         │
│     │   state in DB   │                                                         │
│     │ • Create        │                                                         │
│     │   approval_id   │                                                         │
│     │ • Send approval │                                                         │
│     │   message       │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. Agent execution ENDS (not blocks!)                                         │
│     ┌─────────────────┐                                                         │
│     │ • Agent graph   │                                                         │
│     │   completes     │                                                         │
│     │ • Agent becomes │                                                         │
│     │   available for │                                                         │
│     │   new messages  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  4. System continues processing other messages                                  │
│     ┌─────────────────┐                                                         │
│     │ • New user      │                                                         │
│     │   messages      │                                                         │
│     │   processed     │                                                         │
│     │ • Other tasks   │                                                         │
│     │   execute       │                                                         │
│     │ • No blocking!  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  5. User sends approval command                                                 │
│     ┌─────────────────┐                                                         │
│     │ "/approve abc"  │                                                         │
│     │ arrives as new  │                                                         │
│     │ message         │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  6. Approval handler processes command                                          │
│     ┌─────────────────┐                                                         │
│     │ • Validate ID   │                                                         │
│     │ • Restore state │                                                         │
│     │ • Execute tool  │                                                         │
│     │ • Complete task │                                                         │
│     └─────────────────┘                                                         │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4. **Key Implementation Points**

1. **No Polling**: The system doesn't poll for approval responses
2. **Event-Driven**: Approval commands are processed as regular messages
3. **State Restoration**: Complete agent state is saved and restored
4. **Parallel Processing**: Other agents/tasks continue unaffected
5. **Single Integration Point**: Only the ToolNode needs approval awareness

#### 5. **Message Types and Routing**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           MESSAGE TYPE ROUTING                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  Regular Messages:                                                              │
│  ┌─────────────────┐                                                            │
│  │ "Create vacation│ ──► Normal agent processing                               │
│  │ request"        │                                                            │
│  └─────────────────┘                                                            │
│                                                                                 │
│  Approval Commands:                                                             │
│  ┌─────────────────┐                                                            │
│  │ "/approve abc"  │ ──► Approval handler                                      │
│  │ "/reject abc"   │     • Validates ID                                        │
│  └─────────────────┘     • Restores state                                      │
│                          • Executes/cancels                                    │
│                                                                                 │
│  System Messages:                                                               │
│  ┌─────────────────┐                                                            │
│  │ Approval        │ ──► User notification                                     │
│  │ notifications   │                                                            │
│  └─────────────────┘                                                            │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Key Benefits Illustrated

1. **True Non-Blocking**: Agent execution completes, freeing resources for other tasks
2. **Event-Driven Approval**: No polling or waiting loops
3. **State Preservation**: Complete task context saved and restored seamlessly
4. **Minimal Integration**: Only one node (ToolNode) needs approval awareness
5. **Parallel Processing**: Other agents and tasks continue unaffected
6. **Real-time Responsiveness**: WebSocket ensures immediate command processing

This architecture ensures the system remains fully responsive while providing secure human oversight for critical operations.
