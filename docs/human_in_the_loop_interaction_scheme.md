# Human-in-the-Loop Dangerous Tools: Interaction Scheme

## Overview

This document provides a visual representation of the interaction flow for the human-in-the-loop dangerous tools approval system, showing how the system handles both safe and dangerous tool executions while maintaining parallel processing capabilities.

## System Components Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           INFORMATION FLOWS SYSTEM                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────────────┐ │
│  │   User Input    │    │  Message Queue   │    │    Dynamic Worker Pool      │ │
│  │                 │    │                  │    │                             │ │
│  │ • Chat Messages │───▶│ • Classification │───▶│ • Task-based Routing        │ │
│  │ • Approval Cmds │    │ • Prioritization │    │ • Parallel Execution        │ │
│  │ • Tool Requests │    │ • Queue Mgmt     │    │ • State Management          │ │
│  └─────────────────┘    └──────────────────┘    └─────────────────────────────┘ │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        APPROVAL SYSTEM CORE                                │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │ │
│  │  │ Dangerous Tools │  │ Approval Tool   │  │    Approval Database        │ │ │
│  │  │   Config        │  │     Node        │  │                             │ │ │
│  │  │                 │  │                 │  │ • Pending Requests          │ │ │
│  │  │ • Tool Names    │  │ • Intercepts    │  │ • Approval History          │ │ │
│  │  │ • Patterns      │  │ • Validates     │  │ • Task State Storage        │ │ │
│  │  │ • Messages      │  │ • Routes        │  │ • User Responses            │ │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Detailed Interaction Flow

### 1. Message Processing Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            MESSAGE PROCESSING FLOW                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. User Message Arrives                                                        │
│     ┌─────────────────┐                                                         │
│     │ "Create vacation │                                                         │
│     │ request for      │                                                         │
│     │ next week"       │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. Task Classification                                                         │
│     ┌─────────────────┐                                                         │
│     │ TaskClassifier  │                                                         │
│     │ determines:     │                                                         │
│     │ task_name =     │                                                         │
│     │ "vacation_mgmt" │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. Dynamic Worker Check                                                        │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ Is task already │────▶│ YES: Requeue    │                                │
│     │ in progress?    │     │ message, retry  │                                │
│     └─────────┬───────┘     │ later           │                                │
│               │             └─────────────────┘                                │
│               ▼ NO                                                              │
│  4. Create Worker Task                                                          │
│     ┌─────────────────┐                                                         │
│     │ Mark task as    │                                                         │
│     │ IN_PROGRESS     │                                                         │
│     │ Create async    │                                                         │
│     │ worker task     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  5. Agent Processing                                                            │
│     ┌─────────────────┐                                                         │
│     │ Agent analyzes  │                                                         │
│     │ message and     │                                                         │
│     │ determines tool │                                                         │
│     │ calls needed    │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  6. Tool Execution Check                                                        │
│     ┌─────────────────┐                                                         │
│     │ ApprovalRequired│                                                         │
│     │ ToolNode checks │                                                         │
│     │ if tools are    │                                                         │
│     │ dangerous       │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  7. Route Based on Safety                                                       │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ SAFE TOOLS      │     │ DANGEROUS TOOLS │                                │
│     │ Execute         │     │ Request         │                                │
│     │ immediately     │     │ Approval        │                                │
│     └─────────────────┘     └─────────────────┘                                │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 2. Approval Request Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           APPROVAL REQUEST FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. Dangerous Tool Detected                                                     │
│     ┌─────────────────┐                                                         │
│     │ Tool: submit_   │                                                         │
│     │ vacation        │                                                         │
│     │ Args: {employee,│                                                         │
│     │ start_date, ... }│                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. Create Approval Request                                                     │
│     ┌─────────────────┐                                                         │
│     │ ApprovalService │                                                         │
│     │ creates DB      │                                                         │
│     │ record with:    │                                                         │
│     │ • approval_id   │                                                         │
│     │ • task_state    │                                                         │
│     │ • tool_info     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. Send Approval Message                                                       │
│     ┌─────────────────┐                                                         │
│     │ 🔐 **Approval   │                                                         │
│     │ Required**      │                                                         │
│     │                 │                                                         │
│     │ Tool: submit_   │                                                         │
│     │ vacation        │                                                         │
│     │ Args: ...       │                                                         │
│     │                 │                                                         │
│     │ /approve abc123 │                                                         │
│     │ /reject abc123  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  4. Update Task Status                                                          │
│     ┌─────────────────┐                                                         │
│     │ Task status →   │                                                         │
│     │ "pending_       │                                                         │
│     │ approval"       │                                                         │
│     │                 │                                                         │
│     │ Worker marked   │                                                         │
│     │ as IDLE         │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  5. System Continues Processing Other Tasks                                     │
│     ┌─────────────────┐                                                         │
│     │ Other messages  │                                                         │
│     │ and tasks can   │                                                         │
│     │ be processed    │                                                         │
│     │ in parallel     │                                                         │
│     └─────────────────┘                                                         │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 3. User Response Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            USER RESPONSE FLOW                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  User Response Options:                                                         │
│                                                                                 │
│  ┌─────────────────┐                    ┌─────────────────┐                    │
│  │ /approve abc123 │                    │ /reject abc123  │                    │
│  │                 │                    │ [reason]        │                    │
│  └─────────┬───────┘                    └─────────┬───────┘                    │
│            │                                      │                            │
│            ▼                                      ▼                            │
│  ┌─────────────────┐                    ┌─────────────────┐                    │
│  │ APPROVAL PATH   │                    │ REJECTION PATH  │                    │
│  └─────────┬───────┘                    └─────────┬───────┘                    │
│            │                                      │                            │
│            ▼                                      ▼                            │
│  1. Validate Request                    1. Update Status                       │
│     ┌─────────────────┐                    ┌─────────────────┐                │
│     │ • Check ID      │                    │ • Mark rejected │                │
│     │ • Verify status │                    │ • Store reason  │                │
│     │ • Get task_state│                    │ • Update task   │                │
│     └─────────┬───────┘                    └─────────┬───────┘                │
│               │                                      │                        │
│               ▼                                      ▼                        │
│  2. Update Approval                     2. Cancel Task                         │
│     ┌─────────────────┐                    ┌─────────────────┐                │
│     │ • Status →      │                    │ • Task status → │                │
│     │   "approved"    │                    │   "cancelled"   │                │
│     │ • responded_at  │                    │ • Notify user   │                │
│     └─────────┬───────┘                    └─────────────────┘                │
│               │                                                               │
│               ▼                                                               │
│  3. Restore Task State                                                         │
│     ┌─────────────────┐                                                        │
│     │ • Load saved    │                                                        │
│     │   task_state    │                                                        │
│     │ • Task status → │                                                        │
│     │   "active"      │                                                        │
│     └─────────┬───────┘                                                        │
│               │                                                               │
│               ▼                                                               │
│  4. Execute Approved Tool                                                      │
│     ┌─────────────────┐                                                        │
│     │ • Run original  │                                                        │
│     │   tool with     │                                                        │
│     │   saved args    │                                                        │
│     │ • Complete task │                                                        │
│     └─────────────────┘                                                        │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Parallel Processing Visualization

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         PARALLEL PROCESSING EXAMPLE                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  Timeline: ────────────────────────────────────────────────────────────────▶    │
│                                                                                 │
│  Task A (Safe):     [■■■■■■■■■■] ✅ Completed                                   │
│  Task B (Dangerous): [■■■■■] ⏸️  [■■■■■] ✅ Completed after approval           │
│  Task C (Safe):       [■■■■■■■■■■] ✅ Completed                                 │
│  Task D (Safe):         [■■■■■■■■■■] ✅ Completed                               │
│                                                                                 │
│  Key:                                                                           │
│  ■ = Processing    ⏸️ = Waiting for approval    ✅ = Completed                  │
│                                                                                 │
│  Notice how Tasks A, C, and D continue processing while Task B waits for       │
│  approval. The system maintains full parallelism for non-dangerous operations. │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## State Management Flow

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           STATE MANAGEMENT FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  1. Initial State Capture                                                       │
│     ┌─────────────────┐                                                         │
│     │ Before tool     │                                                         │
│     │ execution:      │                                                         │
│     │ • Get current   │                                                         │
│     │   state from DB │                                                         │
│     │ • Store in      │                                                         │
│     │   approval req  │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  2. State Preservation During Approval                                          │
│     ┌─────────────────┐                                                         │
│     │ ApprovalRequest │                                                         │
│     │ stores:         │                                                         │
│     │ • Complete      │                                                         │
│     │   task_state    │                                                         │
│     │ • Tool context  │                                                         │
│     │ • Message chain │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  3. State Restoration After Approval                                            │
│     ┌─────────────────┐                                                         │
│     │ On approval:    │                                                         │
│     │ • Restore saved │                                                         │
│     │   state         │                                                         │
│     │ • Check for     │                                                         │
│     │   conflicts     │                                                         │
│     │ • Merge if      │                                                         │
│     │   necessary     │                                                         │
│     └─────────┬───────┘                                                         │
│               │                                                                 │
│               ▼                                                                 │
│  4. Conflict Resolution                                                          │
│     ┌─────────────────┐     ┌─────────────────┐                                │
│     │ State unchanged │     │ State changed   │                                │
│     │ during approval │     │ during approval │                                │
│     │ ↓               │     │ ↓               │                                │
│     │ Direct restore  │     │ Merge states    │                                │
│     └─────────────────┘     └─────────────────┘                                │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Key Benefits Illustrated

1. **Non-blocking Architecture**: Safe operations continue while dangerous ones await approval
2. **State Preservation**: Complete task context is maintained during approval process
3. **Parallel Execution**: Multiple tasks can run simultaneously with proper isolation
4. **Configurable Safety**: Easy to add/remove tools from dangerous list
5. **Audit Trail**: All approval decisions are logged for compliance
6. **User-friendly**: Clear approval messages with context and simple commands

This interaction scheme ensures that the system remains responsive and efficient while providing necessary human oversight for critical operations.
