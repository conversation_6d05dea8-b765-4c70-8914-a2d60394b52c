# Approval Command Detection and Matching

## The Challenge

When a user sends `/approve abc-123-def` or `/reject abc-123-def`, the system needs to:

1. **Detect** that this is an approval command (not a regular chat message)
2. **Extract** the approval ID from the command
3. **Validate** that the approval ID exists and is pending
4. **Match** it to the correct approval request
5. **Execute** the appropriate action (approve/reject)

## Where Command Detection Happens

Based on the codebase analysis, approval command detection should be added at the **`perceive_input`** node in the agent graph, right after the message is received but before normal task processing begins.

### Current Message Flow

```python
# Current flow in agent graph:
START → agent_setup → perceive_input → focus_attention → choose_action → tools → ...
```

### Enhanced Flow with Approval Detection

```python
# Enhanced flow:
START → agent_setup → perceive_input → [approval_check] → focus_attention → choose_action → tools → ...
                                           ↓
                                    [approval_handler] → END
```

## Implementation Strategy

### Option 1: Enhanced perceive_input Node (Recommended)

Modify the existing `perceive_input` node to detect approval commands:

```python
@node()
async def perceive_input(state: AgentState, config: AgentConfig) -> PartialAgentState:
    """Convert the received message into an appropriate internal format."""
    with get_telemetry_context().tracer.start_as_current_span("perceive_input") as span:
        message_content = state["received_message"].content
        
        # 1. CHECK FOR APPROVAL COMMANDS FIRST
        if isinstance(message_content, str):
            approval_result = await check_approval_command(
                message_content, 
                config["context"].employee_id
            )
            if approval_result:
                # This is an approval command - handle it and end the turn
                return approval_result
        
        # 2. NORMAL MESSAGE PROCESSING
        agent_logger.assistant_received(
            config["context"].employee_id,
            message_content,
            state["chief_profile"].name,
        )
        await record_measurement(
            measurement.PerceiveMessage(message=state["received_message"])
        )
        
        if state["received_message"].sender_role == "user":
            message = ChiefMessage(message_content, state["chief_profile"].name)
        else:
            employee = await config["context"].employee_profile(
                state["received_message"].sender_id
            )
            message = AssistantMessage(message_content, employee.name)
        
        return {"observations": message}
```

### Option 2: Separate Approval Check Node

Add a dedicated node for approval detection:

```python
def create_agent_graph_with_approval(checkpointer, tools, dangerous_config):
    graph_builder = StateGraph(AgentState)
    
    # Add all existing nodes
    graph_builder.add_node("agent_setup", agent_setup)
    graph_builder.add_node("perceive_input", perceive_input)
    graph_builder.add_node("approval_check", approval_check)  # NEW NODE
    graph_builder.add_node("approval_handler", approval_handler)  # NEW NODE
    graph_builder.add_node("focus_attention", focus_attention)
    # ... rest of nodes
    
    # Modified routing
    graph_builder.add_edge(START, "agent_setup")
    graph_builder.add_edge("agent_setup", "perceive_input")
    graph_builder.add_conditional_edges(
        "perceive_input", 
        route_approval_or_normal,  # NEW ROUTING FUNCTION
        ["approval_check", "focus_attention"]
    )
    graph_builder.add_edge("approval_check", "approval_handler")
    graph_builder.add_edge("approval_handler", END)
    # ... rest of edges
```

## Detailed Implementation

### 1. Approval Command Detection Function

```python
import re
from typing import Optional, Dict, Any
from uuid import UUID

async def check_approval_command(
    message_content: str, 
    employee_id: UUID
) -> Optional[Dict[str, Any]]:
    """
    Check if message is an approval command and process it.
    
    Returns:
        - None if not an approval command
        - Dict with approval result if it is an approval command
    """
    content = message_content.strip()
    
    # 1. DETECT APPROVAL COMMANDS
    approve_match = re.match(r'^/approve\s+([a-f0-9-]{36})(?:\s+(.*))?$', content, re.IGNORECASE)
    reject_match = re.match(r'^/reject\s+([a-f0-9-]{36})(?:\s+(.*))?$', content, re.IGNORECASE)
    
    if not (approve_match or reject_match):
        return None  # Not an approval command
    
    # 2. EXTRACT APPROVAL ID AND REASON
    if approve_match:
        approval_id_str = approve_match.group(1)
        reason = approve_match.group(2)
        approved = True
    else:  # reject_match
        approval_id_str = reject_match.group(1)
        reason = reject_match.group(2) or "No reason provided"
        approved = False
    
    try:
        approval_id = UUID(approval_id_str)
    except ValueError:
        # Invalid UUID format
        await send_message(
            f"❌ Invalid approval ID format: {approval_id_str}",
            receiver_id=employee_id
        )
        return {"approval_processed": True, "success": False}
    
    # 3. PROCESS THE APPROVAL
    result = await process_approval_command(
        approval_id=approval_id,
        employee_id=employee_id,
        approved=approved,
        reason=reason
    )
    
    return {"approval_processed": True, "success": result}
```

### 2. Approval Processing Function

```python
async def process_approval_command(
    approval_id: UUID,
    employee_id: UUID,
    approved: bool,
    reason: Optional[str] = None
) -> bool:
    """
    Process an approval/rejection command.
    
    Returns:
        True if successfully processed, False otherwise
    """
    from information_flows.services.approval_service import ApprovalService
    from information_flows.database.session import AsyncSessionLocal
    
    approval_service = ApprovalService(AsyncSessionLocal)
    
    try:
        # 1. GET AND VALIDATE APPROVAL REQUEST
        approval_request = await approval_service.get_approval_request(str(approval_id))
        
        if not approval_request:
            await send_message(
                f"❌ Approval request {approval_id} not found",
                receiver_id=employee_id
            )
            return False
        
        if approval_request.status != "pending":
            await send_message(
                f"❌ Approval request {approval_id} is already {approval_request.status}",
                receiver_id=employee_id
            )
            return False
        
        # 2. VERIFY OWNERSHIP (Security check)
        # Only the employee who received the approval request can respond
        if approval_request.employee_id != employee_id:
            await send_message(
                f"❌ You are not authorized to respond to approval {approval_id}",
                receiver_id=employee_id
            )
            return False
        
        # 3. UPDATE APPROVAL STATUS
        await approval_service.update_approval_status(
            approval_id=str(approval_id),
            status="approved" if approved else "rejected",
            reason=reason
        )
        
        if approved:
            # 4. RESTORE STATE AND EXECUTE TOOL
            await resume_approved_task(approval_request, employee_id)
            await send_message(
                f"✅ Approval {approval_id} granted. Executing {approval_request.tool_name}...",
                receiver_id=employee_id
            )
        else:
            # 5. CANCEL TASK
            await cancel_rejected_task(approval_request, employee_id)
            await send_message(
                f"❌ Approval {approval_id} rejected. Task cancelled. Reason: {reason}",
                receiver_id=employee_id
            )
        
        return True
        
    except Exception as e:
        logger.error(f"Error processing approval {approval_id}: {e}")
        await send_message(
            f"❌ Error processing approval {approval_id}: {str(e)}",
            receiver_id=employee_id
        )
        return False
```

### 3. Task Resumption Function

```python
async def resume_approved_task(approval_request, employee_id: UUID):
    """Resume execution of an approved task."""
    from information_flows.agents.core_agent.tools import get_tool_by_name
    
    try:
        # 1. RESTORE COMPLETE STATE
        saved_state = approval_request.task_state
        
        # 2. GET THE TOOL AND EXECUTE IT
        tool = get_tool_by_name(approval_request.tool_name)
        if not tool:
            raise ValueError(f"Tool {approval_request.tool_name} not found")
        
        # 3. EXECUTE THE APPROVED TOOL
        result = await tool.ainvoke(approval_request.tool_args)
        
        # 4. UPDATE TASK STATUS
        from information_flows.api.chat.chat_service import ChatService
        from information_flows.database.session import AsyncSessionLocal
        
        chat_service = ChatService(AsyncSessionLocal)
        await chat_service.update_task_status(
            task_id=approval_request.task_id,
            status="completed"
        )
        
        # 5. SEND COMPLETION MESSAGE
        await send_message(
            f"✅ Task completed: {result}",
            receiver_id=employee_id
        )
        
    except Exception as e:
        logger.error(f"Error resuming task {approval_request.task_id}: {e}")
        await send_message(
            f"❌ Error executing approved task: {str(e)}",
            receiver_id=employee_id
        )
```

### 4. Enhanced Approval Request Creation

When creating approval requests, we need to store the employee_id for security:

```python
# In ApprovalRequiredToolNode._request_approval()
async def _request_approval(self, state, config, tool_call):
    employee_id = config["configurable"]["employee_id"]
    
    # Create approval request with employee_id for security
    approval_id = await self.approval_service.create_approval_request(
        task_id=state.get("current_task").identifier,
        chat_id=state.get("chat_id"),
        employee_id=employee_id,  # Store for security validation
        tool_name=tool_call["name"],
        tool_args=tool_call["args"],
        task_state=state
    )
    
    # Send approval message with clear instructions
    approval_message = f"""🔐 **Approval Required**

The assistant wants to execute a potentially dangerous action:

**Tool:** `{tool_call['name']}`
**Arguments:**
{self._format_tool_args(tool_call['args'])}

**To approve:** `/approve {approval_id}`
**To reject:** `/reject {approval_id} [optional reason]`

⚠️  **Note:** Only you can respond to this approval request.
"""
    
    await send_message(approval_message, receiver_id=employee_id)
    
    return {
        "observations": [ToolMessage(
            content=f"⏸️ Waiting for approval: {approval_id}",
            tool_call_id=tool_call["id"],
            name=tool_call["name"],
            artifact={"approval_required": True, "approval_id": str(approval_id)}
        )],
        "pending_approval": approval_id
    }
```

## Security Considerations

### 1. Approval ID Validation
- Use UUID format for approval IDs (harder to guess)
- Validate UUID format before database lookup
- Check approval exists and is pending

### 2. Authorization
- Only the employee who received the approval can respond
- Store `employee_id` in approval request for validation
- Reject unauthorized approval attempts

### 3. State Integrity
- Validate that saved state is complete and uncorrupted
- Handle cases where tools or arguments have changed
- Graceful error handling for invalid state

## Example Usage Flow

```
1. User: "Create a vacation request for next week"
   → Agent detects dangerous tool, creates approval request

2. System: "🔐 Approval Required... /approve abc-123-def"
   → User sees approval request with ID

3. User: "/approve abc-123-def"
   → System detects approval command, validates ID, executes tool

4. System: "✅ Vacation request created successfully"
   → Task completes normally
```

This implementation ensures secure, reliable approval command detection and processing while maintaining the non-blocking architecture.
