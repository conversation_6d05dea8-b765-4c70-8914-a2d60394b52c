<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="431px" height="556px" viewBox="-0.5 -0.5 431 556" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36&quot; version=&quot;26.2.15&quot; pages=&quot;3&quot; scale=&quot;1&quot; border=&quot;10&quot;&gt;&#10;  &lt;diagram name=&quot;Agent&quot; id=&quot;uAlnnndt1yLP9aOt3FAl&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1028&quot; dy=&quot;654&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;1654&quot; math=&quot;1&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vGL_069NwZ8Ov3Fn9BZg-4&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-6&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-4&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;150&quot; width=&quot;160&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-12&quot; value=&quot;Tool 1&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-1&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-5&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-1&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#CCFFCC;strokeWidth=1.9685;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;240&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-3&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-6&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-3&quot; value=&quot;Message&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;90&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-11&quot; value=&quot;Tool 2&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;curved=1;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-5&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-6&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;330&quot; y=&quot;310&quot; /&gt;&#10;              &lt;mxPoint x=&quot;330&quot; y=&quot;370&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-14&quot; value=&quot;Tool 4&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;curved=1;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-5&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-6&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;390&quot; y=&quot;310&quot; /&gt;&#10;              &lt;mxPoint x=&quot;390&quot; y=&quot;370&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-19&quot; value=&quot;Tool 3&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;labelBorderColor=#FFFFFF;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-5&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-13&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;460&quot; y=&quot;310&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-5&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;300&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-13&quot; value=&quot;Tool 5&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;verticalAlign=middle;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-6&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-12&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-6&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;360&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-24&quot; value=&quot;Tool 6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;labelBorderColor=#FFFFFF;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-12&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-14&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;460&quot; y=&quot;430&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-16&quot; value=&quot;Tool 7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;d92WWKpjqdz5JtbbvLQR-12&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-12&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;420&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;240&quot; y=&quot;80&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;240&quot; y=&quot;600&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-16&quot; value=&quot;$$t$$&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=13;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; y=&quot;570&quot; width=&quot;40&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-22&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#FFCCCC;strokeWidth=1.9685;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;350&quot; y=&quot;560&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-28&quot; value=&quot;Start state&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;230&quot; width=&quot;60&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;d92WWKpjqdz5JtbbvLQR-29&quot; value=&quot;End state&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;280&quot; y=&quot;550&quot; width=&quot;60&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-22&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#808080;&quot; parent=&quot;1&quot; source=&quot;vGL_069NwZ8Ov3Fn9BZg-19&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-1&quot; value=&quot;Task&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=1.1810999999999998;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-2&quot; value=&quot;Task&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;510&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-3&quot; value=&quot;Task&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=1.9685;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;560&quot; y=&quot;160&quot; width=&quot;40&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-10&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vGL_069NwZ8Ov3Fn9BZg-6&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-6&quot; value=&quot;Contextualize message&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;160&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-13&quot; value=&quot;Message&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;295&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-14&quot; value=&quot;Message&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;460&quot; y=&quot;415&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-17&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;vGL_069NwZ8Ov3Fn9BZg-15&quot; target=&quot;d92WWKpjqdz5JtbbvLQR-22&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-15&quot; value=&quot;Update task&amp;lt;div&amp;gt;execution state&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;310&quot; y=&quot;480&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-18&quot; value=&quot;Active tasks&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;120&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-20&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;strokeColor=#808080;&quot; parent=&quot;1&quot; source=&quot;vGL_069NwZ8Ov3Fn9BZg-19&quot; target=&quot;vGL_069NwZ8Ov3Fn9BZg-3&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;vGL_069NwZ8Ov3Fn9BZg-19&quot; value=&quot;Current task, determined based on message&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;strokeColor=#808080;dashed=1;dashPattern=8 8;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;450&quot; y=&quot;230&quot; width=&quot;160&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;c2sBj0vkezfLQQYq3yO_&quot; name=&quot;Database&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1028&quot; dy=&quot;654&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;1654&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;3zUhrEp-ACBIg1C2XNX--4&quot; value=&quot;EMPLOYEE&quot; style=&quot;shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;320&quot; y=&quot;240&quot; width=&quot;180&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3zUhrEp-ACBIg1C2XNX--5&quot; value=&quot;&quot; style=&quot;shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;&quot; parent=&quot;3zUhrEp-ACBIg1C2XNX--4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;30&quot; width=&quot;180&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3zUhrEp-ACBIg1C2XNX--6&quot; value=&quot;PK&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;3zUhrEp-ACBIg1C2XNX--5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;30&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;30&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3zUhrEp-ACBIg1C2XNX--7&quot; value=&quot;employee_id&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;3zUhrEp-ACBIg1C2XNX--5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;30&quot; width=&quot;150&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;150&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-3&quot; value=&quot;RELATION&quot; style=&quot;shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=0;fontStyle=1;align=center;resizeLast=1;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;720&quot; y=&quot;240&quot; width=&quot;220&quot; height=&quot;120&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-4&quot; value=&quot;&quot; style=&quot;shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;30&quot; width=&quot;220&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-5&quot; value=&quot;PK,FK1&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;60&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-6&quot; value=&quot;from_employee_id&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;160&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-10&quot; value=&quot;&quot; style=&quot;shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-3&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry y=&quot;60&quot; width=&quot;220&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-11&quot; value=&quot;PK,FK2&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;60&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-12&quot; value=&quot;to_employee_id&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=5;overflow=hidden;whiteSpace=wrap;html=1;&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-10&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;160&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;a5XAhW3AoiRnR90dcw8Y-1&quot; style=&quot;shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=1;&quot; vertex=&quot;1&quot; parent=&quot;_-SEc9y1RQ705uE-0lWC-3&quot;&gt;&#10;          &lt;mxGeometry y=&quot;90&quot; width=&quot;220&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;a5XAhW3AoiRnR90dcw8Y-2&quot; value=&quot;&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;a5XAhW3AoiRnR90dcw8Y-1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;60&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;a5XAhW3AoiRnR90dcw8Y-3&quot; value=&quot;&amp;lt;span style=&amp;quot;text-align: center;&amp;quot;&amp;gt;description&amp;lt;/span&amp;gt;&quot; style=&quot;shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;a5XAhW3AoiRnR90dcw8Y-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; width=&quot;160&quot; height=&quot;30&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxRectangle width=&quot;160&quot; height=&quot;30&quot; as=&quot;alternateBounds&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-9&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=ERzeroToMany;endFill=0;startArrow=ERmandOne;startFill=0;&quot; parent=&quot;1&quot; source=&quot;3zUhrEp-ACBIg1C2XNX--5&quot; target=&quot;_-SEc9y1RQ705uE-0lWC-10&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;580&quot; y=&quot;285&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;_-SEc9y1RQ705uE-0lWC-13&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;endArrow=ERmandOne;endFill=0;startArrow=ERzeroToMany;startFill=0;&quot; parent=&quot;1&quot; source=&quot;_-SEc9y1RQ705uE-0lWC-4&quot; target=&quot;3zUhrEp-ACBIg1C2XNX--4&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;580&quot; y=&quot;262.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;  &lt;diagram id=&quot;zzXfTPRsr5ScI0CA2Yfc&quot; name=&quot;System&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1028&quot; dy=&quot;654&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;1654&quot; math=&quot;1&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-6&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeWidth=1;absoluteArcSize=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;340&quot; width=&quot;320&quot; height=&quot;220&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;kwp42Z53Xkqw4IQ91Ns0-9&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=none;rounded=1;dashed=1;dashPattern=8 8;strokeColor=#808080;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;580&quot; y=&quot;450&quot; width=&quot;100&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-51&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;absoluteArcSize=1;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;785&quot; y=&quot;160&quot; width=&quot;310&quot; height=&quot;210&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-27&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=none;strokeWidth=1;dashed=1;dashPattern=8 8;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;340&quot; width=&quot;220&quot; height=&quot;220&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-2&quot; value=&quot;Agent executor&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;540&quot; y=&quot;370&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-72&quot; style=&quot;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;dashPattern=8 8;strokeColor=#808080;curved=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;&quot; parent=&quot;1&quot; source=&quot;kwp42Z53Xkqw4IQ91Ns0-9&quot; target=&quot;bzoXeDN2utCihaEL7oIc-51&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;500&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint x=&quot;690&quot; y=&quot;500&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-3&quot; value=&quot;Agent executor&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;640&quot; y=&quot;370&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-4&quot; value=&quot;Agent executor&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;490&quot; y=&quot;460&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-5&quot; value=&quot;Agent executor&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;590&quot; y=&quot;460&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-7&quot; value=&quot;Agent pool&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;310&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;dOYyTp9EuNf0X0ZRjTFO-11&quot; value=&quot;Agent executor&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;440&quot; y=&quot;370&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-23&quot; value=&quot;Message queue (persistent)&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fontStyle=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;160&quot; width=&quot;660&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-24&quot; value=&quot;User&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;360&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-25&quot; value=&quot;User&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; y=&quot;360&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-28&quot; value=&quot;Users&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;310&quot; width=&quot;50&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-29&quot; value=&quot;User&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;100&quot; y=&quot;460&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-31&quot; value=&quot;User&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;200&quot; y=&quot;460&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-43&quot; value=&quot;REST API&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;240&quot; width=&quot;220&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;kwp42Z53Xkqw4IQ91Ns0-2&quot; value=&quot;Callback&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;labelBorderColor=#FFFFFF;flowAnimation=1;verticalAlign=middle;curved=0;&quot; parent=&quot;1&quot; source=&quot;dOYyTp9EuNf0X0ZRjTFO-6&quot; target=&quot;bzoXeDN2utCihaEL7oIc-43&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-44&quot; value=&quot;A2A&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;420&quot; y=&quot;240&quot; width=&quot;320&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-50&quot; value=&quot;Agent executor&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;880&quot; y=&quot;160&quot; width=&quot;120&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-56&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=none;dashed=1;strokeColor=#808080;dashPattern=8 8;glass=0;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;200&quot; width=&quot;240&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-79&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=oval;endFill=1;curved=0;&quot; parent=&quot;1&quot; source=&quot;bzoXeDN2utCihaEL7oIc-53&quot; target=&quot;bzoXeDN2utCihaEL7oIc-54&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;895&quot; y=&quot;290&quot; /&gt;&#10;              &lt;mxPoint x=&quot;955&quot; y=&quot;290&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-53&quot; value=&quot;Memory (persistent)&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#7CA3DB;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;830&quot; y=&quot;230&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-76&quot; value=&quot;Output&amp;lt;div&amp;gt;queue&amp;lt;/div&amp;gt;&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; source=&quot;bzoXeDN2utCihaEL7oIc-54&quot; target=&quot;bzoXeDN2utCihaEL7oIc-74&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-54&quot; value=&quot;Graph&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;890&quot; y=&quot;305&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-80&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=oval;endFill=1;curved=0;&quot; parent=&quot;1&quot; source=&quot;bzoXeDN2utCihaEL7oIc-55&quot; target=&quot;bzoXeDN2utCihaEL7oIc-54&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;1000&quot; y=&quot;290&quot; /&gt;&#10;              &lt;mxPoint x=&quot;940&quot; y=&quot;290&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-55&quot; value=&quot;MCP&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#F8DBDA;strokeColor=#D98787;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;950&quot; y=&quot;230&quot; width=&quot;100&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-57&quot; value=&quot;Context&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;820&quot; y=&quot;200&quot; width=&quot;65&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-58&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=1;exitDx=0;exitDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;520&quot; y=&quot;340&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;520&quot; y=&quot;280&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-59&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=1;exitDx=0;exitDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;640&quot; y=&quot;280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;640&quot; y=&quot;340&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-60&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=1;exitDx=0;exitDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;140&quot; y=&quot;340&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;140&quot; y=&quot;280&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-66&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;140&quot; y=&quot;240&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;140&quot; y=&quot;200&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-69&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;520&quot; y=&quot;240&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;520&quot; y=&quot;200&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-70&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;640&quot; y=&quot;200&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;640&quot; y=&quot;240&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-71&quot; value=&quot;System architecture&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=0&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;340&quot; y=&quot;80&quot; width=&quot;480&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-75&quot; value=&quot;Input&amp;lt;div&amp;gt;queue&amp;lt;/div&amp;gt;&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; source=&quot;bzoXeDN2utCihaEL7oIc-73&quot; target=&quot;bzoXeDN2utCihaEL7oIc-54&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-73&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;780&quot; y=&quot;295&quot; width=&quot;15&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;bzoXeDN2utCihaEL7oIc-74&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1085&quot; y=&quot;295&quot; width=&quot;15&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;kwp42Z53Xkqw4IQ91Ns0-4&quot; value=&quot;&quot; style=&quot;endArrow=classic;html=1;rounded=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=1;exitDx=0;exitDy=0;flowAnimation=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;239.55&quot; y=&quot;280&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;239.55&quot; y=&quot;340&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;kwp42Z53Xkqw4IQ91Ns0-7&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;160&quot; width=&quot;20&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;kwp42Z53Xkqw4IQ91Ns0-8&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;720&quot; y=&quot;160&quot; width=&quot;20&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><style xmlns="http://www.w3.org/1999/xhtml" id="MJX-SVG-styles">&#xa;mjx-container[jax="SVG"] {&#xa;  direction: ltr;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] &gt; svg {&#xa;  overflow: visible;&#xa;  min-height: 1px;&#xa;  min-width: 1px;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] &gt; svg a {&#xa;  fill: blue;&#xa;  stroke: blue;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][display="true"] {&#xa;  display: block;&#xa;  text-align: center;&#xa;  margin: 1em 0;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][display="true"][width="full"] {&#xa;  display: flex;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][justify="left"] {&#xa;  text-align: left;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][justify="right"] {&#xa;  text-align: right;&#xa;}&#xa;&#xa;g[data-mml-node="merror"] &gt; g {&#xa;  fill: red;&#xa;  stroke: red;&#xa;}&#xa;&#xa;g[data-mml-node="merror"] &gt; rect[data-background] {&#xa;  fill: yellow;&#xa;  stroke: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; line[data-line], svg[data-table] &gt; g &gt; line[data-line] {&#xa;  stroke-width: 70px;&#xa;  fill: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; rect[data-frame], svg[data-table] &gt; g &gt; rect[data-frame] {&#xa;  stroke-width: 70px;&#xa;  fill: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; .mjx-dashed, svg[data-table] &gt; g &gt; .mjx-dashed {&#xa;  stroke-dasharray: 140;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; .mjx-dotted, svg[data-table] &gt; g &gt; .mjx-dotted {&#xa;  stroke-linecap: round;&#xa;  stroke-dasharray: 0,140;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; g &gt; svg {&#xa;  overflow: visible;&#xa;}&#xa;&#xa;[jax="SVG"] mjx-tool {&#xa;  display: inline-block;&#xa;  position: relative;&#xa;  width: 0;&#xa;  height: 0;&#xa;}&#xa;&#xa;[jax="SVG"] mjx-tool &gt; mjx-tip {&#xa;  position: absolute;&#xa;  top: 0;&#xa;  left: 0;&#xa;}&#xa;&#xa;mjx-tool &gt; mjx-tip {&#xa;  display: inline-block;&#xa;  padding: .2em;&#xa;  border: 1px solid #888;&#xa;  font-size: 70%;&#xa;  background-color: #F8F8F8;&#xa;  color: black;&#xa;  box-shadow: 2px 2px 5px #AAAAAA;&#xa;}&#xa;&#xa;g[data-mml-node="maction"][data-toggle] {&#xa;  cursor: pointer;&#xa;}&#xa;&#xa;mjx-status {&#xa;  display: block;&#xa;  position: fixed;&#xa;  left: 1em;&#xa;  bottom: 1em;&#xa;  min-width: 25%;&#xa;  padding: .2em .4em;&#xa;  border: 1px solid #888;&#xa;  font-size: 90%;&#xa;  background-color: #F8F8F8;&#xa;  color: black;&#xa;}&#xa;&#xa;foreignObject[data-mjx-xml] {&#xa;  font-family: initial;&#xa;  line-height: normal;&#xa;  overflow: visible;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] path[data-c], mjx-container[jax="SVG"] use[data-c] {&#xa;  stroke-width: 3;&#xa;}&#xa;</style></defs><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-8"><g><path d="M 260 117 L 226.37 117" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 221.12 117 L 228.12 113.5 L 226.37 117 L 228.12 120.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-4"><g><rect x="260" y="87" width="160" height="60" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-12"><g><path d="M 170 197 L 170 230.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 235.88 L 166.5 228.88 L 170 230.63 L 173.5 228.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 217px; margin-left: 170px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Tool 1</div></div></div></foreignObject><image x="155.5" y="211" width="29" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-1"><g><ellipse cx="170" cy="187" rx="10" ry="10" fill="#ccffcc" stroke="#000000" stroke-width="1.97" pointer-events="all" style="fill: rgb(204, 255, 204); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-7"><g><path d="M 170 57 L 170 90.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 95.88 L 166.5 88.88 L 170 90.63 L 173.5 88.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-3"><g><rect x="130" y="27" width="80" height="30" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 42px; margin-left: 131px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Message</div></div></div></foreignObject><image x="131" y="35.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-11"><g><path d="M 160 247 Q 140 247 140 277 Q 140 307 153.63 307" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 158.88 307 L 151.88 310.5 L 153.63 307 L 151.88 303.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 277px; margin-left: 140px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Tool 2</div></div></div></foreignObject><image x="125.5" y="271" width="29" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-14"><g><path d="M 180 247 Q 200 247 200 277 Q 200 307 186.37 307" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 181.12 307 L 188.12 303.5 L 186.37 307 L 188.12 310.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 277px; margin-left: 200px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Tool 4</div></div></div></foreignObject><image x="185.5" y="271" width="29" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-19"><g><path d="M 180 247 L 263.63 247" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 268.88 247 L 261.88 250.5 L 263.63 247 L 261.88 243.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 225px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; border-color: #FFFFFF; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); border-width: 1px; border-style: solid; border-color: inherit; border: 1px solid #FFFFFF; white-space: nowrap; ">Tool 3</div></div></div></foreignObject><image x="209.5" y="240" width="31" height="17.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-5"><g><ellipse cx="170" cy="247" rx="10" ry="10" fill="#000000" stroke="#000000" pointer-events="all" style="fill: rgb(0, 0, 0); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-13"><g><path d="M 170 317 L 170 350.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 355.88 L 166.5 348.88 L 170 350.63 L 173.5 348.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 337px; margin-left: 170px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Tool 5</div></div></div></foreignObject><image x="155.5" y="331" width="29" height="15.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-6"><g><ellipse cx="170" cy="307" rx="10" ry="10" fill="#000000" stroke="#000000" pointer-events="all" style="fill: rgb(0, 0, 0); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-24"><g><path d="M 180 367 L 263.63 367" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 268.88 367 L 261.88 370.5 L 263.63 367 L 261.88 363.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 367px; margin-left: 225px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; border-color: #FFFFFF; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); border-width: 1px; border-style: solid; border-color: inherit; border: 1px solid #FFFFFF; white-space: nowrap; ">Tool 6</div></div></div></foreignObject><image x="209.5" y="360" width="31" height="17.75" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-16"><g><path d="M 170 377 L 170 410.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 415.88 L 166.5 408.88 L 170 410.63 L 173.5 408.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 397px; margin-left: 170px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Tool 7</div></div></div></foreignObject><image x="155.5" y="391" width="29" height="15.75" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHQAAAA/CAYAAAAxBeyIAAAAAXNSR0IArs4c6QAAB19JREFUeF7tm11IFUEUx49RYWllEZSIQVIPZRBlmKJ9mhVCRaVR2seDFYKFSA9aPWgPhqIWWUJGQdl3UQ8lRh9q9OUHBSKmD1aaWfmgmVIZWRn/gV2ue9e7uzl753KZ89Tt7p05c35zzpxzZvUZHBwcJCleYwEfCdRrWLKFSKDexVMC9TKeEqgE6m0W8LL1yDNUAvUyC3jZcnw+ffo0GBERQe3t7bYtbcaMGVRTU0OBgYG2zWHnwK9evaKlS5fSjx8/2DQ7d+6kCxcujGjKz58/E2+75+bmkgRqAosEqjGS9FDnXWObh/b09AzCVb98+WK4Vx8+fEhtbW3qc2FhYbRw4ULD302ZMoUyMzMpICDA8FlPfMAOD/369SuZtbueTWpra6mhoUH9avbs2QQ+lrLcXbt2UWlpqToIFMrIyPBEBlx1sgPoSBSsq6ujNWvWEDYFBI5y//59Cg8Pl0DNGNaTgL5//55iY2OppaVFVb2goIAOHDjAPksPNUHUU4AODAzQ3r176fz586rWCQkJdPnyZRozZowEaoIle8RTgF6/fp22bdtGyo0nysAnT57QrFmz1KVIDzVB1ROAfvjwgVauXElv3rxRNb548SJt3759yAqEAO3u7qZLly7R1atXqbGxkb5//86UGjVqFIWEhNCmTZsoOTmZkLn5+PiYMPnQR/78+UPIAs+cOUNVVVVDmiaTJ09myQPGj4uLIz8/P8PxRQOFR6anp9OJEydUXXGO3r59m/z9/cUB7evro8OHD9OpU6cMjYgH0Ek5d+4czZ0719TzWDgA4px5+/at4W/GjRtHR44coX379hH+PZyIBqqdf+zYsfTo0SNasmSJk8pu89D6+npav349IXRYERz2OPTj4+NdeisShoMHD1JhYaGV4dmzq1evZtEC9bKeiASKdSUlJdHNmzdV1RBdTp8+TaNHjxYDVC/VhibLly9noQ87Dbvu9evXhHMB4fjv37+qsgjFN27coM2bN+saHJ6Zn5/vVBNPnDiRUlJS2O+Cg4Pp27dvrPg+duyYkwfHxMTQrVu3aNKkSU5ziAT69OlTWrVqFf369YvphZoT/zdv3jxdW9juoTAizkQYUpGpU6fSlStXmKJ6Z2RHRwclJiYyxR1/8/jxYwoNDXVayJ07d2jjxo1DNgEa6CdPniRA1QrOWKT+CM2OGyctLY2OHz/upJMooL9//6YdO3bQtWvX1CWkpqaydQ2XW9gOFMog1VbEsavhKjb29vYyz6qoqHAZavAcuiZIghTZs2cPFRcXq7XZcPPAI7ds2aJCHT9+PCsD0NJ0FFFAq6urWRQz653Q2VagP3/+ZFDKy8tV+1hpFwISUnXl2gpn3LNnz2jOnDnqeEgOcAYqtRlqssrKShZijUSvUN+/fz8VFRUJB4r1QBdsTEW2bt3KjiS9s1N5xlag7969o6ioKOrs7GTzBQUFsTA6c+ZMI1uz7xFysAh4kiI4X5EkKIKs+ejRo+rnrKwsys7ONjU+HsIGWbFiBZsLsmjRInrw4AGhvFFEhIe2tray3OLjx49MDYRYOMbatWtdrs1WoPfu3WO1niLwVoRgVztMq21JSQlLbBRBEnX27Fn2EfXrhg0b1LCMcVG2REdHmwba1dVFy5Yto6amJvabCRMmsE03f/58oUC16168eDFrwOslbY6LtRWoVqlDhw5RTk6OaWPjQW1IxQaBx/r6+pIWxvTp0+n58+esOWFWtMcCPAEeioRNlIf29/ezJA8AFUEihHrZSGwFmpeXx+5BFbFyfg5nTHhTWVkZ65BoL4n/9yJdey2IrHndunXCgKJ7hnCrXI9Z2agSKBF5GlBtZLNyVEmgHgbUTCLoKuzaCpTHGeoqseJxhppJrNyZ5SKrRWWA7hpEr1QTBlSb0FgJHYrS2k3hWCdqExoeWa6eAd0JVGszx5zBKCHC97Z66EjrUL3GBO86VGtA0XWoNpG0WhnYCnSknSJtY9qOTpH2JkNkp0jv/ESJhl64WbEVKJTg2cvVa33x7OXixgcXAJGRkUPs566Q29PTw9qYL1++HLbJYQTWdqC8blvQ1EePdsGCBU5r4nXbgo4UCnhtJ8tdQJubm1mXS3lHGg2SFy9e0LRp04w4qt/bDhQzWbkPxb0nrrbQOFeE530oNgWg4e7VUTzhPlTbV7aaENmeFDkabCRvLKB3i3tBV+8XjeSNBcBEsoWOjJ64y0Pv3r3L3upQ5H/+KMotHqooKN8pch05tRmuXoJmFHvdClRRxtVbf3gjAY3p3bt3m7rT1Fug0Vt/uGNFuw9XUcoLyq4M5S4P5dH7tgTUaHfI78VbQAIVz4CrBhIoV3OKH0wCFc+AqwYSKFdzih9MAhXPgKsGEihXc4ofTAIVz4CrBhIoV3OKH0wCFc+AqwYSKFdzih9MAhXPgKsG1v/enev0cjDeFpBAeVtU8HgSqGAAvKeXQHlbVPB4EqhgALynl0B5W1TweBKoYAC8p5dAeVtU8HgSqGAAvKeXQHlbVPB4EqhgALyn/wdE35U+9bb22gAAAABJRU5ErkJggg=="/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-12"><g><ellipse cx="170" cy="367" rx="10" ry="10" fill="#000000" stroke="#000000" pointer-events="all" style="fill: rgb(0, 0, 0); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-15"><g><path d="M 50 17 L 50 530.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 50 535.88 L 46.5 528.88 L 50 530.63 L 53.5 528.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-16"><g><rect x="10" y="507" width="40" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 522px; margin-left: 11px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 13px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><mjx-container class="MathJax" jax="SVG" display="true"><svg xmlns="http://www.w3.org/2000/svg" width="0.817ex" height="1.441ex" role="img" focusable="false" viewBox="0 -626 361 637" xmlns:xlink="http://www.w3.org/1999/xlink" style="vertical-align: -0.025ex;"><defs><path id="MJX-4-TEX-I-1D461" d="M26 385Q19 392 19 395Q19 399 22 411T27 425Q29 430 36 430T87 431H140L159 511Q162 522 166 540T173 566T179 586T187 603T197 615T211 624T229 626Q247 625 254 615T261 596Q261 589 252 549T232 470L222 433Q222 431 272 431H323Q330 424 330 420Q330 398 317 385H210L174 240Q135 80 135 68Q135 26 162 26Q197 26 230 60T283 144Q285 150 288 151T303 153H307Q322 153 322 145Q322 142 319 133Q314 117 301 95T267 48T216 6T155 -11Q125 -11 98 4T59 56Q57 64 57 83V101L92 241Q127 382 128 383Q128 385 77 385H26Z"/></defs><g stroke="currentColor" fill="currentColor" stroke-width="0" transform="scale(1,-1)"><g data-mml-node="math"><g data-mml-node="mi"><use data-c="1D461" xlink:href="#MJX-4-TEX-I-1D461"/></g></g></g></svg></mjx-container></div></div></div></foreignObject><image x="11" y="501.5" width="38" height="45.25" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-22"><g><ellipse cx="170" cy="507" rx="10" ry="10" fill="#ffcccc" stroke="#000000" stroke-width="1.97" pointer-events="all" style="fill: rgb(255, 204, 204); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-28"><g><rect x="90" y="167" width="60" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 187px; margin-left: 91px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Start state</div></div></div></foreignObject><image x="91" y="180.5" width="58" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="d92WWKpjqdz5JtbbvLQR-29"><g><rect x="90" y="487" width="60" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 507px; margin-left: 91px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">End state</div></div></div></foreignObject><image x="91" y="500.5" width="58" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-22"><g><path d="M 260 187 L 186.37 187" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(128, 128, 128);"/><path d="M 181.12 187 L 188.12 183.5 L 186.37 187 L 188.12 190.5 Z" fill="#808080" stroke="#808080" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(128, 128, 128); stroke: rgb(128, 128, 128);"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-1"><g><ellipse cx="290" cy="117" rx="20" ry="20" fill="#ffffff" stroke="#000000" stroke-width="1.18" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 117px; margin-left: 271px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Task</div></div></div></foreignObject><image x="271" y="110.5" width="38" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-2"><g><ellipse cx="340" cy="117" rx="20" ry="20" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 117px; margin-left: 321px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Task</div></div></div></foreignObject><image x="321" y="110.5" width="38" height="17" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJgAAABECAYAAAB9Pvx1AAAAAXNSR0IArs4c6QAACBJJREFUeF7tnHXINUUUh5/PLuxOLBQVFWxsUbFFxU6wEbEDGxEDUREVRRDsLuzC7tY/VEzs7m73h7M4DLO7s/e9e991vjPw/fHdOztxznPPnDnn7DsBayaBDiUwocOxbWiTAAaYQdCpBAywTsVrgxtgxkCnEjDAOhWvDW6AGQOdSsAA61S8NrgBZgx0KoFRAzYX8CQwf6e7+nfwn4A1gOdGMFeXUxwBnOpNcCmwa5cTDnNsA2yY0uxmLAOshVzNgrUQlutqgLWQmQHWQlgGWHthtX3iEmAX76GHgE2AH9oO9D/ubxasQ+UZYGCAGWAdSsAA61S4ZsEMMAOsUwkYYJ2Kd5gWbCpgLWALYE1gIWByb/W/FxeKt4G7gSuLy8SzwJ8td6fxVgT2BNYOAso/Am8CtwPa1xvA3wnjD+KDTQtcUMy3UzD+RcD+wM8J8w6ly6gDrW0XPQzApi9AORY4IACqaS1vAXsVMD6QAILkuA1wDjBb08Du+2eA3YrnXmno3xYwQX6eg9wfeuRwafLcAVsWuAWYL1HpsW6HAmfWQDZpkZY6HDh5gDn+AnZ3Fq3KmrUBrAquy4F9AFnRkbacAVsAuBdYNCLR91xO9Fv3nY7LlQEdLWFrymluBVwLTBI8+BHwKFDOsQywfKSfxl8HeKpC86mACa6jix/C8cE44wZXzhZsMueDyDr47Xx3XH4ZUaZ+bKsU1k5HyeLB9zpy5LuEVmaWIqF+P7C01/8Rl4x+JzKHAJZFPCH47mpgZ+CPyDMpgGnthwGnBc9fD+zhQT5S65UzYEsBUvSMnkRPAo5L8KfkQ93hrE35uBz+9Qs/7utAQ+sC93iuxmvuIvFpjSYFw5HBkfoVsBrw6gCA9RaunAHb21mwUl8pivd1uyOgo6VsH7oj9IMAgIOBM7zPUktp5ihAfDCwlLrxXdESMMGl0h1ZXf+IHnfLVe4jVx9M1ka+0UouVKCjUf5Jaluu8IseBqZxD1T5YeHxdQOwXcVR588tueuGvIELjTzv/h/zw+qOyJj/d5dbQ+n7pe65k365AjZWYaUCtiUgqMqWcitsu7YqwHoPV85HZFslhv1TAVvQ+XrzBAO8CJxb+HG3AXX+WMo6Y4BdBlwX+JhPABtH/MSUOTrrYxbsP9Eq0q/boCyD/KG5PalXHZGS34nFkXhMjYY+A24C5BcpbPFLS22GgL0LzBDApSF/K8bXpUOXm960iQ0wBUUVWlgCkNWRj6bUjm6dsRhYqai6WJiUrTCD/KmUphup0jg3F6GPWLgkHCMErG4OwbXpeIYlwsVNDIAJnK1d/GnJFAIifZqCrVO7OJlCIX5+s2k6vQBzIPB0TfikDjDlSvWj8dtR7iWRlDxn0/rG/H3OgEnwyg/KWigf2aYpCq/y7lI+TYCVY5cwK861WIsJlStVkFQJ91QLJku4g0tjqcq3bN+4zMALLebvrGuugMmKnAIc0iA53fqUNpIFUTm2/r3ufLGUMEXd8Do69dqcYmoCoO4I1jiK8PsxtXLsmAUTXKoKUVwuvJDoOV0u9OMaWdVElSByBWzbwoJcFUnm3+cCqI8XSeb3axzu1Ftk6i9fcpZF3NAlnZWTDFtVMDgE7CUHbBn0rbpo7BsEm1PXOtR+OQImy6GaLjnwZfsC2AzQVT6lDRuwcM55Xc3Z6sEXGxXrvjP4LCUXObvLifo+5sfOgqoGbdxajoApp6caLiW8y6ZEsp/6aRJ4GECN+WAzO0ui4sVV3XzKIKhoMaWpyuOxoH5MvluYsE4BTPPpB6RwiJ8yutjVtMV8u5Q1jrlPjoCFecS6RHJMgJKJCgf3876MAaYwhwBRWVDZNnf1ZymKmc75SgK0bGMBTH7nha6IsRxPN8ntgWtSFtRFnxwBCxPd3wM6iuS7pDT1la82RQNgspCKfykwWzZF1wV4isWIAaqxbgwWmWrB9NgiLocqf69s8u3WcxeClP0PtU+OgMmRVrmN31JjQ6oHUwXsrMHzuo3J0qjM2W9KbOsy4TcBcXpDWVCsZOcTd9SGR2wbwLQOVa4que83hWpUzxarNxsqUOFgOQIWyw8qHKFqirMrru4qqRaEUk5Vk49za/ClsgJyylcIPlceUkWFsUi9YnL6azm65flNazsoAmZbwHTJ0Tr9C4T2r0yDKnxH2nIErC4/qKNLpTEvOynr9qVY1UyB1L9zf/5pTu/z2PGlr3X8qEQmLJnWd3pxRH7ar0VcbkpnoRaOaFhvGGkc5RnD1hYwPR875mV9Zd1T0lNDgzBHwCSctvlBX6AKZShCrgS2X3ItCxh7saOq6C9VSfKRdGuNVbNqjEEA05rOcm9S+etIrepNXXtjv1wB08YVOVc0X75HSlNEX3Xtqu9Sji+8LDT94RUdk6poDev5q+bWsaVaf4Esi1nVBgFMYynWpiPRX0/TCyYpcmrVJ2fASkHIv9KLDwohqIqiTEZLwXKodSGIvWgb1vWnlMNInopvqYxZtVm61fkpos/d8az4lPyklKrTQQHT/mMZDUEnizmSv1DUd8Ba/Vqsc/8kYID1TydZrcgAy0qd/duMAdY/nWS1IgMsK3X2bzMGWP90ktWKDLCs1Nm/zRhg/dNJVisywLJSZ/82Y4D1TydZrcgAy0qd/duMAdY/nWS1IgMsK3X2bzMGWP90ktWKDLCs1Nm/zRhg/dNJVisywLJSZ/82Y4D1TydZrcgAy0qd/duMAdY/nWS1IgMsK3X2bzMGWP90ktWKDLCs1Nm/zRhg/dNJViv6B6hD+lQkq3y6AAAAAElFTkSuQmCC"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-3"><g><ellipse cx="390" cy="117" rx="20" ry="20" fill="#ffffff" stroke="#000000" stroke-width="1.97" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 117px; margin-left: 371px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Task</div></div></div></foreignObject><image x="371" y="110.5" width="38" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-10"><g><path d="M 170 137 L 170 170.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 175.88 L 166.5 168.88 L 170 170.63 L 173.5 168.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-6"><g><rect x="120" y="97" width="100" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 117px; margin-left: 121px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Contextualize message</div></div></div></foreignObject><image x="121" y="103" width="98" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-13"><g><rect x="270" y="232" width="80" height="30" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 247px; margin-left: 271px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Message</div></div></div></foreignObject><image x="271" y="240.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-14"><g><rect x="270" y="352" width="80" height="30" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 367px; margin-left: 271px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Message</div></div></div></foreignObject><image x="271" y="360.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-17"><g><path d="M 170 457 L 170 490.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 170 495.88 L 166.5 488.88 L 170 490.63 L 173.5 488.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-15"><g><rect x="120" y="417" width="100" height="40" rx="6" ry="6" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 437px; margin-left: 121px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Update task<div>execution state</div></div></div></div></foreignObject><image x="121" y="423" width="98" height="32" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAYgAAACACAYAAAARZ/7/AAAAAXNSR0IArs4c6QAAIABJREFUeF7tnWW4NL1thhVs8oWZGRpmZmZmhoa5oYax4YahYWZmZmZsqGmYmRk7d2Knjip75Nk5Z2d35et6f7xnZzz2Y3gkWZIPJFECgUAgEAgEAgEDgQMFKoFAIBAIBAKBgIVAEETMi0AgEAgEAgETgSCImBiBQCAQCAQCQRAxBwKBQCAQCAT8CIQG4ccqngwEAoFAYKcQCILYqeGOzgYCgUAg4EcgCMKPVTwZCAQCgcBOIRAEsVPDHZ0NBAKBQMCPQBBEG6szisi7ROSA4rHLiMir/RCbTx5aRF4jIuctfn2niFxKRH65Yt3rfv2ZInKdohF3FpEHr7tR8f2tQ8BaQ3Osza0DapUOBUEEQawyf6x3N4EgTiEitxORu4nI9+YGYE31HXsQLu4gIk8XkU+uqQ37+dkgiH1AOwgiCGLuabZkgjiGiNxTRG4qIl8XkbOJyHfmBmCf6zusiPyriNx9ILw/iMh5ROSj+9yGdXwuCGIfUA+CCIKYe5otlSBOO5j03i4iR0gd3gaCOKqIvEdETpL69OsgiJXNv3Ovh42uLwgiCGLuCbxUgtDnSdtAEGhEHxCR4wZB/BWBOIOYeTUHQQRBzDylJAhibkTr9QVB/KOjRxDEzHMvCCIIYuYpFQQxN6CN+oIggiD2dLoFQQRBzD3BQoOYG9HQICwE4pB6H+ZZEEQQxNzTLAhibkSDIIIg9m9O/cOXgiA2gyAuLSKvKppaBp8dcvB7v7yI3Hp45gwicrD03E9E5E0i8mgR+aCI/GniHGOO4CVzAxG5ooicKNXz5yGw7yODV9CjROTlIvKb9PdVCIK2c5h8lSFA8QIicmIROVTRbr7J4TJBhc9LXkm4dtaKxq0FgdcDiPZcIuFxlsIrirppG55ST1oR81Y7reDN1vNjdnnG93hpbC8uIqcRkaOoCn8gIp8SkeeLyCtE5EcT5tLhBvdbxuN6aZ5mbzKqmlL/VA3i5ClI9YRqXl1DRF4kIn+Z0LetfSUIYrMJ4sxp0eZNu9abL6UNjY21pxB8BcFAQK1C/VcXkQ+LTDqDgORuKCL3ExH8+r3l5ymmgYVtEeCcBEEbb5XamEl4DJMbJ8KYc9OZiyBY++dOBH86L+DpucemIEPwHysQ6v0Tdgcee7ij/ikEARG+uXAL5nMIHUEOlYEJgthcgni3iLxWRA7vXHQshNsM0uHjnFISEvwLReTIzvp/OhDEJYfAs5t0ptqAhNBAzuT8jvXYE5IGpbWJuQji+CLy4oltfJiI3CUFsa3Qxb+/OgdBQHAPFJHbr9Cgz6Tx/lqjjiOKyEsGje/8E77z5ZR65nOVd3sJokYOaMZovXOS+ITuLvOVIIjNJIinJBNMqSazQWNS+lkyzZDnSUtskATmm5eOTEdMJ2+skA9mpY8PG8w/pVxSLLxcWNQ/FBHez6WViwmzA225oNEezA7vE5Hvp98ICiNKuDRN5NdY3FcazCMvU/UgGd88/Q2zyWVFJM/536fN61fpd0xkjxhMIF9VdVgbS37k2ylQDczpy/lEhHbq8uTBjHKLmUgCsrrtkN8KjQbpnH4fPH0QHF6ZTDa5Df8pIp8oGkT/71jJjwUWn07mJF5hjM9ZmBV1v56aNLg/Gn2GhBBGbqR+Yw5CLmibaH3WPMqvIARB8uCrSw9BHD2Zj9CYcqEdQQ4GsOWfgiDaAC0lWV9LEq6ZWdg8yM1zb9VFiATtgE3eKkcSkdcP2gnmq7I8K+Uv0vbnEwxE8sgUpGTV1yKIO4nIg9RLnLWQOuIrlfb9c7LxQxZlgdAwheWzEP36lEC5GoGh8SB96zaynk6dJFJttmEs0CbmLFPcXK05/Y20Wb6tYqrD7HfXAVvGqyzMJTZdSEUXyPKtSkjBhMh4Z1Iu32EeQaRaWMB0+QKjfi9BWGPYq03POWYbVVcQRHu4lk4QY2o444vGwIFuqU2w4K495O+xJD/yFD1ewcLm8JDGQXdNWqSaGkEcbZC43zEs/pMV38KMc02HpI30/Ox0sJpf/26SdsHEKlMIwiKwMSz4NuSM2etaRUPI+QSpfXHGHaKXIJgPj0naTG6Gt128e92UDLDsAn18rtEnzq44s8kFbYL/t0w5bOZkSi4l/delcf6t+oaHIIIcVpxsQRCbSxB43aAJ4KHUKowxm/QDiodqkh8LCkn8rBM2bWtxtwjiQskkludgSxq1+neudAB80PQjmgNmNUwXcxCERWA9piJLE7vP0DCt0a2yhHsJgvMkHBXIZpsLmzaHzp6CeQ8zZnleREbccm5RDwSJqavUBsa8qfL3LzaYKCGFPC9qxD9GEBZJh+bgGeXimSCIzSUIj0SWe3ecQXrFfIDbaC6WZK83Xez0mAre75xXenG3CILkediAOa/gLAVtAm8SS6uxPq83R55pbUK9GsTVkodY/rZX0i7bquv4bCIxzmnmKL0EwaExZzJs8PwDa4SMmtZltVG7MWN6RLMoi7V51zQN/Q2cFpgLh0hu1JivcKXmTGrsG3n8axotZr6Hx4G0f+oFQWwmQfRu3PRSq/yW6q5NKhxIX2TwfiKmwlMsCXOvLgzaS4JgXTwt+eznfj9jiHP4l87NBeJ772Bu4pCUwoaMRw8ZWOcovQQxxzc9BAF+z0mEn785Zg7tbVtNg3hD5XA8yKEX4UKNm/DqTryy1DOI3o2bwbqC8l5iwZ6juDAHUw1nEwTD5YK9mgC8noKHFZpBLptIEBbRcSiN9NlTDpPMJWhmuXDG88SeShrPLpUgaLLWnvgbJh7cXrnUCC1Bnyv0wGIRBB5dOAncS1XEHSAckIcraw/CQRCjaC2VIDh0RmXvmfC6Lzpy2NrMpmyK3NRWeuvMSRCYDk6a3FW51hSPprLMZWLSkj/fIMBKu8COTSDayzWyZSwJ9nrs9nOU/SAItAG+gykKxwbOjkqHB8vERN9q3nBlv3F3ZS7jFfaFzmh/iyC4KIl5rgvnUkSJT4kAn2OcNraOMDG1h26pBGEdDI5NwjFzR6/Jpva9VlqQsTbyO3MSWznR4bSZ9CH5nxUDsRcE0RuM5ulXfqa2ofbUkZ+dkyDYcKnvlImEwYAb9/JdE7X2tfrDOQcu055gS4IciWPBtEe6klYKFdpiEUQLQ7zPcLHtEaqmjMlWvRMEsZkEMUUqHyOAsd+9E38KQSBpc8DNGcjZjQA/77fn0iB2gSBY++QlImjuyirnlRdvnhsjPG+6lvKbmKJwE8YsVLsSdowgCMI7SFGp1+uvp+9b/2wQRD9BeL0xWjVbk7u10KZsuvr7YwQw9rt3MfS0lfnHoS3J7cbySenvE8XM4W9p7giC8N1JPWXTBn9ckcG7zJc1RhB53PCkIwCSg35vehi8va6aPPD0+LcIAhMnh+SYBUvthf9zFvdL72Te9eeCINozgIXElY7HKh6bIr3rr+BjTxqJMlXGXhME0iLeM5hvKPoMYr8JgrlH+glcGMeSuCFFfihhxuEmmUUxN5XXbdKnvSIIb6bX/d5PppiYiPAmWp0Nu1XwXONc4F3J5AP+2PA9XkytenP22IsmssAM1Rr/WuR/jSDK3FdWoOPNknay32O1kd8LgmgPmxVYRP4ZMo+uUvRmTV0t4tFS+ZQ2kDqDICmikCk/FhG8a3IyNGvBTSFDIqGR3nKp1UEwHrEZByggyRtEQBobE4fCNWmvl9B64iCI0SAPEAf3uZDiG3v6kkovQdSCGYkxwLOI/v1Xmhs1W/2qBKHxy44HOB2gnR/TANiK+bHmKylf/q04v7AOytFKiO3hgDzKCAJBEG2ACNYhmRybQy5ssnimrKKmIukSaVoW3Et1srn8uyaIWvqBVm/0xq2Dtizf9Sm+/6R25kCwRRC41JIqA1fIXDiUpI24QXoOEveSICwNbwpZ7vUG1EsQluspLrck/6vlsNJ9mJsgyvqZg8Td4G5dmqG0SzbvjEVS53ovPBy2ExtRainelC57PX6Lrz8IYnyI9IbXmxJCf8HKh6Olef2OJohvpXw1tYR21jc9gV/aRbU38terhVhupBxI4q/uIQf6pzUi/jaXickSDMaSAVozCaIhYR1mPbDkH9rfJ8enneuJHoKw4lx63T8tV2jLNIqGzJwlzoZIebQxyN8bJc+lQmg0uVgmPi9B0G/ieYg/yYU5RhJA0tlHaSAQBDE+PXTOIN7Abo7U5d3Myq+Q7gLzCYs7lzGtxMrmWstyafWITJks0vIsxXpfm2FqabRrqOGBxBlBTj/Nc56UHnwHyfEt48Px9ydumRZ++cpcBEGdun6i15kL4OgtmEzQlMqNqbefrW/1EIQV/NcbCHmq1P9SurcIQmvI3BlB2nAEG08Zi9mhDi9B8Cwp0iHq8swPrYRcUb2xLZ72b80zQRDjQ2klsPPeq6Brr+WIGUuYZhEENlSCl/J9CbWeMMbcc8BlQbl8Ptlhv6de4nyCoCUOEHPxSpn0jayeuEyWxSIIi3R7bPwWyfLNOQmCeADIrvSCYZPBFGjdT6Dxx8EBr5kyWy2H64zZXAFbPQSx6nlabXwtgrA0RILsyrOp1srTBAOxEJPxzeKlHoLgNU3W/A1XWtaeV7MZ3y227IkgCN+AWimwIQkC1tAmPPZbNgwO25j8ZfFs9LX7IFhwtM3Kr883ap5CLXu6ZbMd+w7+5hwO6qyetMH6lnUIzHkHV3SOBUhBDtyLzAauS+scR0ulY+nBLWLle2NY8AxEi3Rephzh73N70GiCaOV6sjKseqVo+oOQwW2BulhR/dYZE6ZZBA+8oVrFOkjnHJDzk3Ij7yUI+sDVtJwf5sIa5g6R8r53346wI08FQfgGmsnIATKbpy54gOA9wcEXF6/k/DJsmtwuhhsf0gt5Yqbe8Na6MAgJH5st9u2y4KtO5CibUlnGNIKallP7Dp4i5CjCC8UqFkFYWhnvcg8Fz1t3HfMd3GLvPtxxXLsTukV8lufY2N0OlhZAO/G0gpjZ7LSZkVTakJ2+cKlH+/DNyr9pNzp9N2ccSMWW0EKqcZ2niL5wPqDnD20AZ66RfWgjTqVmHuVOB0yGpbmRcSWbLBu1JQiAHVpoedkSmzhBlGhjZeklCN61POc8App3PLbuuSAI/5By9SR3QFuSq7+W/3uyJze9Jgg2JT122HlZrL8bPKROX7k/uUeKq10Fmr+DNMeCG8OjtmlbKj/o6CspIRPccbX7IxsMwXLllaetFCTWZsr30L5wEqA+iFafMbSuX+VdNkHMfFydienMctNEcGCjxYV0zmIdplM/fUFDooBJPgepmeZ4rrw+FeEGgmNstVDDhUcENeb5R2wN5sFfqI7VNLDcvo8VV5u2rpOt3cExhSBo032TkFE2t9dBYs4xXHRdQRB9w0PkLm6B2kzUV8vfJGSkNgjHc9CtCYJNjBgCLQ222sEmRbvL+4lbz1v3+I71Ex9zoqLH3FypB+mUFOSld8lY/fn3LyUvFKTUMjFgK4lha8PK9dai5L3BZVb7e3H3YpCfs8yfZR06OSCRxLiR1rSw2vchHVJmQwiczeQYEet8INdhXdrT0z80MZwFLBPqFILg25ARa6cUbKY4IPT0Y2OfDYLoHzqkK+yYmG/KA0hPTUjHnENgJrHMKLU6NEGgKbDZc4UlKnmZ+sCqgxvDkCR7vkk92G0xVyBhjW0oXCrEhT+kWy5tui2zD3WSfgFX4rH6aQ/tBztSimNC0YfdY94yY6TXyrQKxuCAmWss8juPwVTcPXPJuwlb9ntu3sMU5U1vwkZ9j3RIbGlil23Y8VkvmDm5sjYHaY71D7MtThWYosipZJWpBEFdrB2cMcpxROhinXkcEMbavzW/B0FMH8oyZQCphDlr4NCwnHRIPnhesKEzIafmwLcIIgfrsXHdSESuX0hF2UzDgSrmhVrCM2/vsf9jfsETBTtx3sxJx8AVlGgCXH3KYu7JxZS/T/1I77je4kqJ5JnL11OqB7QDneXTuhZ0zFuGTQqskLy1CcXaTDVGmLzoI3icZiD8oxQP5PQUmEU4SJ/LW2lsnNiEL5cS75H5tiTb2t0hOUEit8EhaOh+YAJ6vtEPK47HE3jG98i7hRABQZElNq8V5ivjzDoBO852xpwVViEI2oKmyxiWBQ2p1EjHcN/634MgNmOIWwSxGT2IVgYCgcDGIRAEsRlDFgSxGeMUrQwEtgqBIIjNGM4giM0Yp2hlILBVCARBbMZwBkFsxjhFKwOBrUIgCGIzhjMIYjPGKVoZCGwVAkEQmzGcQRCbMU7RykBgqxAIgtiM4QyC2IxxilYGAluFQBDEZgxnEMRmjFO0MhDYKgSCIDZjOIMgNmOcopWBwFYhEASxGcMZBLEZ4xStDAS2CoEgiK0azuhMIBAIBALzIRAEMR+WUVMgEAgEAluFQBDEVg1ndCYQCAQCgfkQCIKYD8uoKRAIBAKBrUIgCGKrhjM6EwgEAoHAfAgEQcyHZdQUCAQCgcBWIRAEsVXDGZ0JBAKBQGA+BIIg5sMyagoEAoFAYKsQCILYquGMzgQCgUAgMB8CQRDzYRk1BQKBQCCwVQgEQWzVcEZnAoFAIBCYD4EgiPmwjJrWi8ChReQ1InLeohmXEZFXr7dZ8fVAYHMRCILY3LHbpZafQkRuJyJ3E5HvVToeBLH8GXEQEbmiiJxaRO6xwOYuvX37DlkQxL5DHh/sQOAYInJPEbmpiHxdRM4mIt8JguhAcBmPss+cRUQeIyJnFpFnich1l9G0v7Zi6e1bG1RBEGuDPj48gsBpB3PR20XkCOm5IIjNnTK3HsbxUUXzl0YQS2/f2kY+CGJt0MeHRxA4o4i8S0QOCILY+LlyJxF50IIJYuntW9sECIJYG/Tx4ZkJIgBdLgJL34CX3r61jWwQxNqgjw8HQezMHFj6Brz09q1togRBrA36+HAQxM7MgaVvwEtv39omShDE2qCPDwdB7MwcWPoGvPT2rW2ibBJB0NaTiMgNROQKg6vcCUXkwAm5X4nIp0Xk+SLyHBH5kQNRgqheXtTBK58XkQuLyDcd7584HaLiipnL/6T3vzbyPv7WZxKR64vIxUXkuMXzPxGRj4nIM1L76Nsq5UgicjkRubqInKHwCvqziHx5cD98nYg8UUQ+JyJ/aXyIfn5AtbUnEM3z/qWHNr7K2dlfi8h5ROSj6fk54iCOk3C6hogQe3GwVHeJ1VNE5LODH/+fnO3Um0+JGfVfTERukdxAs8fWH9I3np3mgWc+O5vTfIx5Sb9vKCKXUGuMNjFf3igizxORj4xg8Mzh+es4G/XOob5LicgvG88fQkTOJyKXT8GQrP88Pry27vbRhrzWbiQipxKRQxnzx7PWnLDt/WObQhBMWjZMfKg95bEpqOrnjYcPmvyy8bEvC+54tx3ZLJmYzxWRKxcvsokweVsbHHifX0SeJCIncnSESX/31M7fOJ4vHzmsiNwvbT6ZSFtVvDst6K9WHvJs8K36Pe+viyCYX49M5O6BGUGCTR033BapUleNIIj4fqpzHjw0xYP0zgFPX/IztOcJInIy50tfEpEbNzCYiyCYxwTV3UYRwlgz96t9tIM23l9EbjnWqPT7h0XkekkIcL6ynseWThBINID+cCXpe9D6xiCJI619ovHwsUXkzWpR/F5ELiQibJi1cq1hYSPdlYXNmKCu2oZxyDSJIJ/egrQG+Xg0G+o+eyKqI3d+6KeJ9N5ivOfZ4DeNIJj/1xYRtIJSGvXCxsb14CS91t7RBHHZpIUhiHiIO9f7hkHDvdogvPzM2zjnc2BwlaQV9LQnV3+fNK8RZsoyB0GcLs1jNLup5Q5p/9Drco720SaE1pcOZN/bRgRKSO9xDiFjat9Xfm/JBEHb7pgWoO4okxEzzKfSDwRVYbLRE5wN76KDuvyhBlKYlFh85buQA9KstRhPOai67xCRcvNtPc+n2XyYCKieumBCwnSD+g4hnlVE+IYuXvMVEauYAQ5v1IHpC3X+d8l8gIlGb4xgdgER+bh6fz8Igg3h5um7RxnawGaa5yjE/ZJhTmSTG9L0IwZJLGs8vSam1vyiCZ8ZzH8fTGaU2vziOaRuAq30Bpnh0wTxwkTC5XzzjAv13bmyHlbZCGrzBVMncSjfT5Vj0rHmCxsv5kv6VRZMwcxlCqZN4lpyYcwQzHJhbhNl/dvib8dLz2BW1oWgSdZMXp+0jSj7bNIpn9emyPzbqu2jntZaQ4N5b1prR01Cp9U+5sd/LJUklkwQ5Gx5kdq4MRmxgfB3vSABn4WKJF8uvrFzBTBgo4HNy4Lk8TD1N7QAvo29NJcxEqptREygmw2L7m2GLRdb5r+n38smsKg4f6nZai2NiPc5a7n9kN7gK6o/YIYEyG9lIekdUmVp0tgPgijbsNeBctYZFN9HI3jAkPtJmycZ+1ul+aVJ1ZoruS+aIMo+1saFbyEcMTZlYS5jh6/lozL20uafsOtzbscZVS4IKqy9TxqbFv3mXOHxSrCASBHEatpN7yEw5l+Il028LHwXrc06k2GdoTljttNmMoQzxq6m3fe2jzbVCAzz9r2NNiL8IXjRvlLb8Jime8d1tueXShAAyMbJQXAuSBkcnH1xpPeYh16sJOgx8w8Mz/dKyf2HaTEiSebCRoo9uCytzYHn9EbH37BdX2kgtB83+sLYkK+GCVUSHiYRDuKtwsS8l/rhrkM08kMaB4oWgbGQwBrNKpdtIghrvJE0IcXXjswvy+xhzZVcTY0gpowLdaJVeQ/yxzaKkw+S7XtE5IjpQYSdcyeHj9a7V03EkvcP5stFRMQyTVJP7wbMAS9aeakFj63h3F40TxwvsCjkgomW9qEVWaW3fRaBsdHj3IAA2TqXOnp6Bpxz4UwCZ5X9ckYYmxd//32pBMG5AypnLmNSuu6wPiP4Vpr4WoIu37NMTRDNNZO2cvpEIuWktSTtsk7wpR8caObiNRXxPO/fNx1U5/dr0trRkumrlJ442OcgsWb+yHVa5pkXJPv8H9ND20QQ2PKRnMvSIl49v1jcbIYHL36oOTdYBDE2b3K11piS0RYNZ47CpsRmmovHm4hn0XDQftAacqFNtM0qvRvwTZIGkevq1ZxYs6UQxfrHBFU7w+ttn0VgPaYiywMSMx1rblFliQRxuGRDz/ZLAPNKDxlcqw68lXAxqxVrM87qH9rFy5SXC6r4BQsbuFXvCZIkdKzix56NiNd0HTVpDZPJK4vv1Gyvtf7rTZP+naMwZ2wLQWBW4VARDSkXzmxwAvB6CVnEzznCOYdzJjajslgE4dUCkFTZNDD55DJnojvtNTYmaZf9QgNCe+Y8AFfjVzTu3ujdgJH26TN7AC7gmJZq5GPNZ621j62F3vZpTX2KBqDrgKjpc3kOs3ayWCJB9A5uDUTczpjEubApsAlmidh6D9v/65U7LYMPQTCJcoE4MEdQZ6voTbu2ibTqsDYJS1p7dLKzTt30OOh7U8IHLQXTA/3LZrBtIQj6yeEhqn4uaJy4LfeUcyVTIeNDqRG33ny+m4gEAvYUPY/nJAg8cNAa0AhyqXkledpae6Z3A17lW7zbu4f0tM/Stqc4D+j50zsvVsXI9f4SCUKrh1qSdXUsHeaWGzjBTfh6Yy9uFct8oJ9/cjIbjZlu9OJ+a7Ih9wa/QXTUlYuWNjhsRntAo8llTlMEdW4LQWizCgTIYiVQsKfgxcbmSgxFLtZGoTcfiBft5RfOj/VsXs4q//6YpWnzIw4UxOpgT0eoGYv1GPvuXvbB+vZeEoQlYDCeCJY9BYcSPLGydWHsHKen7tmeXSJB6E2VDR2b7dhmrEE5vjIJeRnaMjWVdY95ReVnLclfu/d5B1K7CWpTgLV5o65iFpurbAtBaPu2V3DQOHrNP3pz7BUS9npztWJ6yr4izLD+0LBwe50Sh7HXfaC9mA5Pk8w09OmYRSfmNDFZmiPC2Q86FxpCHY4q5TnWFE2287N9jy+RIHoCWHp6OzZJyrqQrLjLuPQ04HdPEF2ux1JFe9rbelZfnqOlEd6dItW0vrktBKE3K+/BrIWNnquW+WfVzXHV98fmHK6rmCd1RoHae/89nAs8LR3yE4zqKXP1AVdRzMBobUjenFEQi1CmtbDaM7b2e9rXE+3vwaZ8ZoqpqvcbXc8HQdTh4vyAg7cSI4LH8HbyuKPtJ0H0qtRdkyQ9HATx/1HTBGGRTc/mY43Lqu97xpqNlzM1Yg9IG+EtaNNsamgYrdxUq/QBSZuUNhyIWwGknrYGQXhQMp4JgrCBsw6r85Nej6ogiH/Etpdg9ipQbi81CMsRYpXNEQRXfb9na8jJAznzwi3UW3CnxjW9drY2pQ9TSYs2fzudmeX9LQjCO5LquU0giDm9NjwwjZ1BeM1Mc3k7eNo818FZ61u9G7yuq/f9TSSITTQx1cYcmz72duzkuAATXNgqaB9EK1tegr0EAVE90Ijw19/HmxBzK6l00N7494V0FlFeV7uXBDF2V7pn/S72mSUSBInTyhD73kO9VcG2vJhQn5FociG6mrD5nKfG+qblWURUNKmU5y5xSC3izcU01yG1FU9hjW/v5qjnxqrvzzXX0KqZ88xfshVYec9qUdi9fdCR2rkPBCcSAPe+YY/g/KMWM9Brcu1pn/aCwxuNfpOaZOvKEgnidioH0pTYgakDZZmWCPnHswqPoAOKij1pwTXZrXIg2urTYVJELBJfLlPcXJ8uIpdMiQNJhoiNnZgIiuXW2XMfBAnvwJK25tJ6f680CDY34j3y3J+6wIlyZqNCe8uFVCxkHi5Lz+ZjjfGq709dC633OKcgeI3UEmWx+s/vPX2wXG/xZGSuvN/Zmb0kCGseewMfnc1fzmNLJAi9gK28QB4EsYk+KIXXs8kR8IaEV4uWtZL2oZoiNaHC6pQXqLdc9lJmpdTt0tKqN9dNWU+O2mUx5qhVNjjs3aU6r8moNzKTy2qot8xhU7rdWRJ6jyutlrzo4zoIYt2Bcr0m057N1bMu8jMExzF/WW9EgEN4HAYTp+GSp6I6AAAHc0lEQVQpCFPMfVLQ5FLrW08ftBspdfdmHyChZRkDNaeJyRKUxpIBWnjieQV+WCdwW8c0RqI/1vhiyhIJwso/U+ZE8oBnaQJjG6aVi6k8kLYSvI2ZmlZJOpb7aeVtIb8TmWvLolNl9JIRmTBJY579snUAmUUQPVqKjvReF0FYpqFezc5K1uZNtbEUgpjjjMzj5turQehA2d5ARisNypwEYdX/nZQKfSyRaF6v1jlnbz89++DKzyyRICxJ3pvaIgNiZV3FdooGYRXv5m+liG6ZmqakBy/bZ916BxZETLOZl8XK++RN1mdteDo/kRUY5s1hZJHcugiC7+5Fsr6aFNkjPVtzc9X3W5uEDkrtySnUIzD09EFr3b0mQOsMcU6CAE9LmPRmV+B9K/EnLvUk7ItcTA5asy7lad12VlZJgjlSNpdZV1uSfo2QrOtD8a4gBQHXBeYyls/dmkykD+FOiVZ6B9pl3fTVygRqpftmcbYuJLG+g1kP7xUdia0Xuoe4SSXNRT9ctapLj4lpLBLee0hNGyyBgEh9iGMs+txK9127ZKlXet5vgtBaI9/3bnT6IBlzJ2Nsmaj0vGnlRbNMkbjdYi4eS/lRu0kRszJpdiBAD8ZjedsswY96PXmsrPXgMVc7ts35H1miBpF7aWkBAMlZABf8eC90GdvEeqUBSxpupd+o3X2NVIOZiAvg9blI7Y7bsbTn1sYHnrWLVvC0IgAJYilLjYSsuy3oBzflcaNYGSyVL0jh27X7t1sEoe8qoH2tOxR6CIK6ahcG1e4zZ1O4frq7Wl8Y1CLhHunZs3n1mqhau4Z1tzrP1y4y4jdw4HIttI/Sk6kluFhncThDcMivi6UJs4YxZ6KtW2eI3B/D3GhFg7fmWk/7cnstLYDfMGXjaEPEeVnYa2v30XtJeX4GGKlxyQTRuqaTbpVXQrJpIyFYd+pyWQ6Ty/LPtm5g86TxtvLXtPzA8cxAKimT6eWhYfJzSJXd5Jh45UFx+RwH72y4rdK6BtFzjWbrvoraTV+0B/LikJtcPdYVi9SLWay8drW1aK3DQL5DMBb2WiR+NLl8d3gvQTD3a1fa6vnVunJ0bHEvmSDop6Wt5/lFwBkaAWMK4ZP9led7r/bVjie5fvIXYVJhM0V7I0NBKw5JXzXMPOMaVBwsyoLwiOBSZuxtOVT0tK/8jnXrZf4dF3jmO9ix/klhbl0DjAMMN/pxjrG4smSCACxvwEwNWK4MvUsl0V9Nsvd4TFgq5pipiUmCpxGmm97Se8H51MvePaYvSBUJ0yKxWr9yvRACpoJcWgRhmf50/WNeVmNuuHyDa18fadzN7RkjhI+7jySSXDpB0E/rFkZP/3kGF1TMTaTEr5WaZpuf1+ZD1gr3YOBl1VtwhcXjj3Ep46laDhW97SvbhCZE1tvSBd7bZgRDTNm1i4y89ezZc0snCDqeVTMOAfVdszVgSFfMTWpc7VmzW1omBu9NX3zXUjHHMr3m9AGoylyN6CnYTZGUyTraU2pmqlodNdOK9XxP3ZAJ94izCejNcmwDt65nLNtT3ovRq0GU9ZD8DQcGb3oJxoQ+scDHyiYQBH0g+zHmK52gstU/vAsxp3g2uBYJWecXmD+JpiY621NwD0UjRFPH1KnNRmOear3tK9uE0ET8C27CnoImBIHhjei9pMpT7+zPbAJB5E6zubKQ8UbiqkP82bMtOIfc41VDsBcLt5U8zDItte4VtoAHOxKV6esfx0wO1EW7MQVhbsE0xq1ZWW1n8iBxk0KYTQvTzNjhXGti4PKLpI2HRJn1EswwObGBo9l4M3OW38L2y0JEgmQ8ch9YrOTHx7WVg/jc/l6C4FvZ9o99WZs3ysPEVQiC7zCeJ0n3gCMVMtfy/GJMIGicH3Dt7BmTTSGIPK6MKc4RbHYnVeYb7nTGX5/rWpHwv9e5I7Hu0OgxqZTpuKmmluqa9rDmCUYrx4T5yzrB5s85nl7z2sXckyJnSvtKCHDRx1TGWtPYYU4j+JQ1TZt774TphHqexzeJIObpcdQSCAQCgUAg4EIgCMIFUzwUCAQCgcDuIRAEsXtjHj0OBAKBQMCFQBCEC6Z4KBAIBAKB3UMgCGL3xjx6HAgEAoGAC4EgCBdM8VAgEAgEAruHQBDE7o159DgQCAQCARcCQRAumOKhQCAQCAR2D4EgiN0b8+hxIBAIBAIuBIIgXDDFQ4FAIBAI7B4CQRC7N+bR40AgEAgEXAgEQbhgiocCgUAgENg9BIIgdm/Mo8eBQCAQCLgQCIJwwRQPBQKBQCCwewgEQezemEePA4FAIBBwIRAE4YIpHgoEAoFAYPcQCILYvTGPHgcCgUAg4EIgCMIFUzwUCAQCgcDuIRAEsXtjHj0OBAKBQMCFQBCEC6Z4KBAIBAKB3UMgCGL3xjx6HAgEAoGAC4EgCBdM8VAgEAgEAruHQBDE7o159DgQCAQCARcCQRAumOKhQCAQCAR2D4EgiN0b8+hxIBAIBAIuBIIgXDDFQ4FAIBAI7B4CQRC7N+bR40AgEAgEXAgEQbhgiocCgUAgENg9BP4X5Y1gJhSXl6kAAAAASUVORK5CYII="/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-18"><g><rect x="300" y="57" width="80" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 72px; margin-left: 301px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Active tasks</div></div></div></foreignObject><image x="301" y="65.5" width="78" height="17" xlink:href="data:image/png;base64,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"/></switch></g></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-20"><g><path d="M 340 167 L 371.36 135.64" fill="none" stroke="#808080" stroke-miterlimit="10" pointer-events="stroke" style="stroke: rgb(128, 128, 128);"/><path d="M 375.07 131.93 L 372.59 139.36 L 371.36 135.64 L 367.64 134.41 Z" fill="#808080" stroke="#808080" stroke-miterlimit="10" pointer-events="all" style="fill: rgb(128, 128, 128); stroke: rgb(128, 128, 128);"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-19"><g><rect x="260" y="167" width="160" height="40" fill="none" stroke="#808080" stroke-dasharray="8 8" pointer-events="all" style="stroke: rgb(128, 128, 128);"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 187px; margin-left: 261px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Current task, determined based on message</div></div></div></foreignObject><image x="261" y="173" width="158" height="32" xlink:href="data:image/png;base64,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"/></switch></g></g></g></g></g></g></svg>