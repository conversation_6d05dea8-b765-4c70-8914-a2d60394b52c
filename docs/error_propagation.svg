<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="451px" height="501px" viewBox="-0.5 -0.5 451 501" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36&quot; version=&quot;26.2.15&quot; scale=&quot;1&quot; border=&quot;10&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;yzYbAGAyQCWOBUJL61zP&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;894&quot; dy=&quot;569&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1169&quot; pageHeight=&quot;1654&quot; math=&quot;1&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;2&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;curved=1;flowAnimation=1;&quot; edge=&quot;1&quot; source=&quot;14&quot; target=&quot;17&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;800&quot; y=&quot;510&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;900&quot; y=&quot;510&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;3&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=none;dashed=1;dashPattern=8 8;strokeColor=#808080;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;180&quot; width=&quot;120&quot; height=&quot;290&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;4&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; edge=&quot;1&quot; source=&quot;5&quot; target=&quot;7&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;5&quot; value=&quot;Message&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;120&quot; width=&quot;120&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; edge=&quot;1&quot; source=&quot;7&quot; target=&quot;9&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;7&quot; value=&quot;Call LLM&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700.01&quot; y=&quot;200&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; edge=&quot;1&quot; source=&quot;9&quot; target=&quot;12&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;9&quot; value=&quot;Call LLM&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;310&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;10&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;719.16&quot; y=&quot;250&quot; width=&quot;41.67&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;11&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;&quot; edge=&quot;1&quot; source=&quot;12&quot; target=&quot;14&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;740&quot; y=&quot;490&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;12&quot; value=&quot;Call LLM&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;700&quot; y=&quot;420&quot; width=&quot;80&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;13&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;719.16&quot; y=&quot;360&quot; width=&quot;41.67&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;14&quot; value=&quot;Message&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;680&quot; y=&quot;490&quot; width=&quot;120&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;15&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;860&quot; y=&quot;480&quot; width=&quot;31.25&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;16&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;flowAnimation=1;&quot; edge=&quot;1&quot; source=&quot;17&quot; target=&quot;20&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;17&quot; value=&quot;Agent 2&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;860&quot; y=&quot;360&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;18&quot; value=&quot;Agent 1&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=default;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;650&quot; y=&quot;250&quot; width=&quot;60&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;19&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;flowAnimation=1;&quot; edge=&quot;1&quot; source=&quot;20&quot; target=&quot;22&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;20&quot; value=&quot;Agent 3&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;860&quot; y=&quot;160&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;21&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.932;entryY=0.244;entryDx=0;entryDy=0;flowAnimation=1;entryPerimeter=0;&quot; edge=&quot;1&quot; source=&quot;22&quot; target=&quot;17&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;22&quot; value=&quot;Agent 4&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;1000&quot; y=&quot;260&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;23&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;884.38&quot; y=&quot;290&quot; width=&quot;31.25&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;24&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;951&quot; y=&quot;232&quot; width=&quot;31.25&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;25&quot; value=&quot;&quot; style=&quot;shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://www.cnjg.org/sites/default/files/coronavirus_0.png;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;961&quot; y=&quot;337&quot; width=&quot;31.25&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;26&quot; value=&quot;Error propagation&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;760&quot; y=&quot;50&quot; width=&quot;240&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs><style>@keyframes ge-flow-animation-uK8T7SLRDmE0BXtQLVgE {&#xa;  to {&#xa;    stroke-dashoffset: 0;&#xa;  }&#xa;}</style><style xmlns="http://www.w3.org/1999/xhtml" id="MJX-SVG-styles">&#xa;mjx-container[jax="SVG"] {&#xa;  direction: ltr;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] &gt; svg {&#xa;  overflow: visible;&#xa;  min-height: 1px;&#xa;  min-width: 1px;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] &gt; svg a {&#xa;  fill: blue;&#xa;  stroke: blue;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][display="true"] {&#xa;  display: block;&#xa;  text-align: center;&#xa;  margin: 1em 0;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][display="true"][width="full"] {&#xa;  display: flex;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][justify="left"] {&#xa;  text-align: left;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"][justify="right"] {&#xa;  text-align: right;&#xa;}&#xa;&#xa;g[data-mml-node="merror"] &gt; g {&#xa;  fill: red;&#xa;  stroke: red;&#xa;}&#xa;&#xa;g[data-mml-node="merror"] &gt; rect[data-background] {&#xa;  fill: yellow;&#xa;  stroke: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; line[data-line], svg[data-table] &gt; g &gt; line[data-line] {&#xa;  stroke-width: 70px;&#xa;  fill: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; rect[data-frame], svg[data-table] &gt; g &gt; rect[data-frame] {&#xa;  stroke-width: 70px;&#xa;  fill: none;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; .mjx-dashed, svg[data-table] &gt; g &gt; .mjx-dashed {&#xa;  stroke-dasharray: 140;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; .mjx-dotted, svg[data-table] &gt; g &gt; .mjx-dotted {&#xa;  stroke-linecap: round;&#xa;  stroke-dasharray: 0,140;&#xa;}&#xa;&#xa;g[data-mml-node="mtable"] &gt; g &gt; svg {&#xa;  overflow: visible;&#xa;}&#xa;&#xa;[jax="SVG"] mjx-tool {&#xa;  display: inline-block;&#xa;  position: relative;&#xa;  width: 0;&#xa;  height: 0;&#xa;}&#xa;&#xa;[jax="SVG"] mjx-tool &gt; mjx-tip {&#xa;  position: absolute;&#xa;  top: 0;&#xa;  left: 0;&#xa;}&#xa;&#xa;mjx-tool &gt; mjx-tip {&#xa;  display: inline-block;&#xa;  padding: .2em;&#xa;  border: 1px solid #888;&#xa;  font-size: 70%;&#xa;  background-color: #F8F8F8;&#xa;  color: black;&#xa;  box-shadow: 2px 2px 5px #AAAAAA;&#xa;}&#xa;&#xa;g[data-mml-node="maction"][data-toggle] {&#xa;  cursor: pointer;&#xa;}&#xa;&#xa;mjx-status {&#xa;  display: block;&#xa;  position: fixed;&#xa;  left: 1em;&#xa;  bottom: 1em;&#xa;  min-width: 25%;&#xa;  padding: .2em .4em;&#xa;  border: 1px solid #888;&#xa;  font-size: 90%;&#xa;  background-color: #F8F8F8;&#xa;  color: black;&#xa;}&#xa;&#xa;foreignObject[data-mjx-xml] {&#xa;  font-family: initial;&#xa;  line-height: normal;&#xa;  overflow: visible;&#xa;}&#xa;&#xa;mjx-container[jax="SVG"] path[data-c], mjx-container[jax="SVG"] use[data-c] {&#xa;  stroke-width: 3;&#xa;}&#xa;</style></defs><rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="zSOogmlxu3_piHCDcWwU-25"><g><path d="M 160 470 Q 260 470.04 260 406.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-uK8T7SLRDmE0BXtQLVgE; stroke-dashoffset: 16;"/><path d="M 260 401.12 L 263.5 408.12 L 260 406.37 L 256.5 408.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-9"><g><rect x="40" y="140" width="120" height="290" fill="none" stroke="#808080" stroke-dasharray="8 8" pointer-events="all" style="stroke: rgb(128, 128, 128);"/></g></g><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-8"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-4"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-12"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-1"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-7"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-3"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-11"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-14"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-19"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-5"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-13"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-6"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-24"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-16"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-12"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-15"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-16"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-22"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-28"/><g data-cell-id="d92WWKpjqdz5JtbbvLQR-29"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-22"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-1"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-2"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-3"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-10"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-6"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-13"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-14"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-17"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-15"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-18"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-20"/><g data-cell-id="vGL_069NwZ8Ov3Fn9BZg-19"/><g data-cell-id="zSOogmlxu3_piHCDcWwU-5"><g><path d="M 100 120 L 100.01 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 100.01 158.88 L 96.51 151.88 L 100.01 153.63 L 103.51 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-1"><g><rect x="40" y="80" width="120" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 100px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Message</div></div></div></foreignObject><text x="100" y="104" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Message</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-7"><g><path d="M 100.01 190 L 100 263.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 100 268.88 L 96.5 261.88 L 100 263.63 L 103.5 261.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-4"><g><rect x="60.01" y="160" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 175px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Call LLM</div></div></div></foreignObject><text x="100" y="179" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Call LLM</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-13"><g><path d="M 100 300 L 100 373.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 100 378.88 L 96.5 371.88 L 100 373.63 L 103.5 371.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-6"><g><rect x="60" y="270" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 285px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Call LLM</div></div></div></foreignObject><text x="100" y="289" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Call LLM</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-2"><g><image x="78.66" y="209.5" width="41.67" height="40" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-20"><g><path d="M 100 410 L 100 443.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 100 448.88 L 96.5 441.88 L 100 443.63 L 103.5 441.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-10"><g><rect x="60" y="380" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 395px; margin-left: 61px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Call LLM</div></div></div></foreignObject><text x="100" y="399" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Call LLM</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-11"><g><image x="78.66" y="319.5" width="41.67" height="40" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-14"><g><rect x="40" y="450" width="120" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 470px; margin-left: 41px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Message</div></div></div></foreignObject><text x="100" y="474" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Message</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-16"><g><image x="219.5" y="439.5" width="31.25" height="30" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-26"><g><path d="M 260 320 L 260 206.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-uK8T7SLRDmE0BXtQLVgE; stroke-dashoffset: 16;"/><path d="M 260 201.12 L 263.5 208.12 L 260 206.37 L 256.5 208.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-21"><g><ellipse cx="260" cy="360" rx="40" ry="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 360px; margin-left: 221px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Agent 2</div></div></div></foreignObject><text x="260" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Agent 2</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-22"><g><rect x="10" y="210" width="60" height="40" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 230px; margin-left: 11px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Agent 1</div></div></div></foreignObject><text x="40" y="234" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Agent 1</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-27"><g><path d="M 288.28 188.28 L 366.07 228.78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-uK8T7SLRDmE0BXtQLVgE; stroke-dashoffset: 16;"/><path d="M 370.72 231.2 L 362.9 231.07 L 366.07 228.78 L 366.13 224.86 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-23"><g><ellipse cx="260" cy="160" rx="40" ry="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 160px; margin-left: 221px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Agent 3</div></div></div></foreignObject><text x="260" y="164" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Agent 3</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-28"><g><path d="M 371.72 288.28 L 299.86 336" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="8" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); animation: 500ms linear 0s infinite normal none running ge-flow-animation-uK8T7SLRDmE0BXtQLVgE; stroke-dashoffset: 16;"/><path d="M 295.49 338.9 L 299.39 332.11 L 299.86 336 L 303.26 337.94 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-24"><g><ellipse cx="400" cy="260" rx="40" ry="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 260px; margin-left: 361px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Agent 4</div></div></div></foreignObject><text x="400" y="264" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Agent 4</text></switch></g></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-29"><g><image x="243.88" y="249.5" width="31.25" height="30" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-30"><g><image x="310.5" y="191.5" width="31.25" height="30" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-31"><g><image x="320.5" y="296.5" width="31.25" height="30" xlink:href="https://www.cnjg.org/sites/default/files/coronavirus_0.png" preserveAspectRatio="none"/></g></g><g data-cell-id="zSOogmlxu3_piHCDcWwU-32"><g><rect x="120" y="10" width="240" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 30px; margin-left: 121px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 20px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Error propagation</div></div></div></foreignObject><text x="240" y="36" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="20px" text-anchor="middle">Error propagation</text></switch></g></g></g></g></g></g></svg>