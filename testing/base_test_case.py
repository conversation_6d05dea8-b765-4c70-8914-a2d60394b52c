import traceback
import unittest
from uuid import <PERSON>UID

from psycopg_pool import Async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rich.console import Console

from information_flows.api.employee.service import EmployeeService
from information_flows.core.pool import AgentPool
from information_flows.database.setup import async_engine
from information_flows.settings import settings
from testing.cleanup_mixin import CleanupMixin


class BaseTestCase(CleanupMixin, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()

        print()
        self.console = Console()

    async def asyncSetUp(self):
        await super().asyncSetUp()

        try:
            # a connection pool for LangGraph checkpointer
            self.connection_pool = AsyncConnectionPool(
                settings.database_url,
                max_size=10,
                kwargs={
                    "autocommit": True,
                    "prepare_threshold": 0,
                },
                open=False,
            )
            await self.connection_pool.open()

            self.agent_pool = AgentPool(self.connection_pool, async_engine)
            self.agent_pool.start()

            self.employee_service = EmployeeService(async_engine, self.agent_pool)

            await self.setup_cleanup_monitoring()
        except Exception:
            # If setup fails, ensure cleanup still happens
            await self._emergency_cleanup()
            raise

    def assert_agent_success(
        self,
        agent_results: dict[UUID, Exception | None],
        raise_exceptions: bool = True,
    ) -> None:
        """Raise an exception group with agent errors if there are any. Must
        be called at the end of a test, preferably in the teardown method."""
        errors = [
            result for result in agent_results.values() if isinstance(result, Exception)
        ]
        if errors:
            exception = ExceptionGroup("Some agents failed with errors:", errors)
            if raise_exceptions:
                raise exception
            else:
                traceback.print_exception(exception)

    async def asyncTearDown(self):
        # block and wait until agents complete or faile their turns
        agent_results = await self.agent_pool.shutdown()

        await super().asyncTearDown()

        self.assert_agent_success(agent_results, raise_exceptions=True)
