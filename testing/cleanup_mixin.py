import asyncio
import signal
import threading
import traceback

from information_flows.core.logger import agent_logger
from information_flows.database.setup import async_engine
from testing.database_cleanup import TestDataTracker, cleanup_test_data, find_test_data


class CleanupMixin:
    """
    Mixin class that provides database cleanup and interrupt handling.

    This should be mixed with unittest.IsolatedAsyncioTestCase to provide
    automatic cleanup of test data and proper handling of CTRL+C interrupts.
    """

    def setUp(self):
        """Initialize test data tracker and interrupt handling."""
        super().setUp()
        self.test_data_tracker = TestDataTracker()
        self._cleanup_done = False
        self._signal_handler_set = False
        self._original_sigint_handler = None
        self._interrupt_event = threading.Event()
        self._cleanup_task = None

    async def setup_cleanup_monitoring(self):
        """
        Set up signal handler and start interrupt monitoring.
        Should be called from asyncSetUp after connection pool is initialized.
        """
        self._setup_signal_handler()

        # Clean up any existing test data before starting
        # This cleans up data from previous test runs that may have been interrupted
        existing_test_data = await find_test_data(self.connection_pool)
        await cleanup_test_data(self.connection_pool, existing_test_data)

        # Start monitoring for interrupts
        self._cleanup_task = asyncio.create_task(self._monitor_interrupt())

    def _setup_signal_handler(self):
        """Set up signal handler"""
        if not self._signal_handler_set:
            self._original_sigint_handler = signal.signal(
                signal.SIGINT, self._handle_interrupt
            )
            self._signal_handler_set = True

    def _handle_interrupt(self, signum, frame):
        """Handle interrupt - sets flag for async cleanup."""
        print("\n\nTest interrupted! Cleanup will be performed...")
        # Set the interrupt event that the async monitor is waiting for
        self._interrupt_event.set()

    async def _monitor_interrupt(self):
        """Monitor for interrupt signal and perform cleanup when detected."""
        while not self._cleanup_done:
            # Check every 0.1 seconds if interrupt was signaled
            if self._interrupt_event.is_set():
                print("Interrupt detected, performing cleanup...")
                try:
                    await self._emergency_cleanup()
                except Exception as e:
                    print(f"Error during interrupt cleanup: {e}")
                finally:
                    # Restore original handler
                    if self._original_sigint_handler:
                        signal.signal(signal.SIGINT, self._original_sigint_handler)
                    # Now raise the KeyboardInterrupt to stop the test
                    raise KeyboardInterrupt("Test interrupted by user")
            await asyncio.sleep(0.1)

    async def _emergency_cleanup(self):
        """Emergency cleanup that can be called from signal handlers."""
        if self._cleanup_done:
            return

        print("Performing emergency cleanup...")
        try:
            # Try to clean up test data
            if hasattr(self, "connection_pool") and self.connection_pool:
                await cleanup_test_data(self.connection_pool, self.test_data_tracker)
                print("Test data cleaned up successfully.")
        except Exception as e:
            print(f"Error cleaning up test data: {e}")

    async def perform_base_cleanup(self):
        """
        Perform base cleanup operations common to all test cases.
        Subclasses should override this if they need different cleanup behavior.
        """
        if self._cleanup_done:
            return

        try:
            # Clean up test data using tracked IDs
            if hasattr(self, "connection_pool") and self.connection_pool:
                await cleanup_test_data(self.connection_pool, self.test_data_tracker)

            self._cleanup_done = True

            # Cancel the interrupt monitor task
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass

            # Restore original signal handler
            if self._signal_handler_set and self._original_sigint_handler:
                signal.signal(signal.SIGINT, self._original_sigint_handler)

        except Exception as e:
            print(f"Error during base cleanup: {e}")
            traceback.print_exc()
            # Still try emergency cleanup
            await self._emergency_cleanup()

    async def asyncTearDown(self):
        """Clean up resources and test data."""
        await self._perform_cleanup()

    async def _perform_cleanup(self):
        """
        Perform full cleanup of resources and test data.
        Override this method if you need custom cleanup logic.
        """
        try:
            # Perform base cleanup
            await self.perform_base_cleanup()

            # Close connection pool if present
            if hasattr(self, "connection_pool") and self.connection_pool:
                await self.connection_pool.close()

            # Checkpoint trajectory if available
            if hasattr(agent_logger, "checkpoint_trajectory"):
                agent_logger.checkpoint_trajectory()

            # Dispose async engine
            await async_engine.dispose()

        except Exception as e:
            print(f"Error during cleanup: {e}")
            # Emergency cleanup is handled by perform_base_cleanup
