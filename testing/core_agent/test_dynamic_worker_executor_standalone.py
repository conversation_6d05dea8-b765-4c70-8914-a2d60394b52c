"""
Standalone tests for Dynamic Worker Executor without BaseTestCase dependencies.
"""

import asyncio
import pytest
import pytest_asyncio
from uuid import UUID, uuid4
from typing import Dict, Any
from datetime import datetime

from information_flows.agents.core_agent.dynamic_worker_executor import DynamicWorkerExecutor, EnhancedMessage, TaskStatus
from information_flows.core.types import Message


class MockAgent:
    """Mock agent for testing."""
    def __init__(self, agent_id: UUID):
        self.agent_id = agent_id
        self.processed_messages = []
        self.processing_delay = 0.5
        self.should_fail = False
        
    async def process_message(self, enhanced_message: EnhancedMessage, state: Dict[str, Any]) -> Dict[str, Any]:
        """Mock process_message method."""
        if self.should_fail:
            raise Exception("Test error")
        
        # Extract original message
        message = enhanced_message.message
        
        # Track the message
        self.processed_messages.append({
            "message": enhanced_message,
            "state": state.copy(),
            "timestamp": datetime.now()
        })
        
        # Simulate processing time
        await asyncio.sleep(self.processing_delay)
        
        # Return new state
        new_state = state.copy()
        new_state.setdefault("messages", []).append({
            "content": message.content,
            "task_name": enhanced_message.task_name,
            "timestamp": datetime.now().isoformat()
        })
        new_state["response"] = f"Processed: {message.content}"
        new_state["last_processed"] = message.content
        
        return new_state


class TestDynamicWorkerExecutor:
    """Test the Dynamic Worker Executor."""
    
    @pytest_asyncio.fixture
    async def setup(self):
        """Set up test environment."""
        # Create test agent
        agent_id = uuid4()
        agent = MockAgent(agent_id)
        
        # Create executor with mock agent
        executor = DynamicWorkerExecutor(
            agent=agent,
            state_resolver=None,  # Will use mock
            max_queue_size=100
        )
        
        # Start executor
        await executor.start()
        
        yield agent, executor
        
        # Cleanup
        await executor.stop()
    
    @pytest.mark.asyncio
    async def test_basic_message_processing(self, setup):
        """Test basic message processing."""
        agent, executor = setup
        
        # Send a message
        message = Message(
            content="Test message",
            sender_id=uuid4(),
            receiver_id=agent.agent_id,
            sender_role="user"
        )
        
        await executor.add_message(message, thread_id="test_thread_1")
        
        # Wait for processing
        await asyncio.sleep(1)
        
        # Check stats
        stats = executor.get_stats()
        assert stats["messages_received"] == 1
        assert stats["messages_processed"] == 1
        assert stats["errors"] == 0
        
        # Check agent processed the message
        assert len(agent.processed_messages) == 1
        processed = agent.processed_messages[0]
        assert processed["message"].message.content == "Test message"
    
    @pytest.mark.asyncio
    async def test_task_isolation(self, setup):
        """Test that only one worker processes a task at a time."""
        agent, executor = setup
        
        # Send multiple messages to same task
        task_name = "isolation_test"
        messages_sent = 5
        
        for i in range(messages_sent):
            message = Message(
                content=f"Message {i}",
                sender_id=uuid4(),
                receiver_id=agent.agent_id,
                sender_role="user"
            )
            await executor.add_message(message, thread_id="thread_1", task_name=task_name)
        
        # Wait for processing
        await executor.wait_for_completion(timeout=100)
        
        # All messages should be processed sequentially
        stats = executor.get_stats()
        assert stats["messages_processed"] == messages_sent
        
        # Check that all messages for the task were processed
        task_messages = [
            m for m in agent.processed_messages 
            if m["message"].task_name == task_name
        ]
        
        # Verify all messages were processed
        assert len(task_messages) == messages_sent
        
        # Extract message contents
        message_contents = [m["message"].message.content for m in task_messages]
        
        # Verify all expected messages are present (order may vary due to requeue)
        for i in range(messages_sent):
            assert f"Message {i}" in message_contents
    
    @pytest.mark.asyncio
    async def test_parallel_tasks(self, setup):
        """Test that different tasks process in parallel."""
        agent, executor = setup
        
        # Make processing faster for this test
        agent.processing_delay = 0.2
        
        # Send messages to different tasks
        task_names = ["task_a", "task_b", "task_c"]
        start_time = asyncio.get_event_loop().time()
        
        tasks = []
        for task_name in task_names:
            for i in range(3):
                message = Message(
                    content=f"{task_name} message {i}",
                    sender_id=uuid4(),
                    receiver_id=agent.agent_id,
                    sender_role="user"
                )
                tasks.append(executor.add_message(message, thread_id=f"thread_{task_name}", task_name=task_name))
        
        await asyncio.gather(*tasks)
        
        # Wait for processing
        await executor.wait_for_completion(timeout=5)
        
        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time
        
        # With parallel processing, should be faster than sequential
        # Each message takes 0.2s, so 9 messages sequentially = 1.8s
        # With parallel tasks, should take ~0.6s (3 messages per task)
        assert processing_time < 1.5
        
        # Verify all processed
        assert len(agent.processed_messages) == 9
    
    @pytest.mark.asyncio
    async def test_requeue_mechanism(self, setup):
        """Test that messages are requeued when task is busy."""
        agent, executor = setup
        
        # Send two messages to same task quickly
        task_name = "requeue_test"
        
        message1 = Message(
            content="First message",
            sender_id=uuid4(),
            receiver_id=agent.agent_id,
            sender_role="user"
        )
        
        message2 = Message(
            content="Second message",
            sender_id=uuid4(),
            receiver_id=agent.agent_id,
            sender_role="user"
        )
        
        # Send both quickly
        await executor.add_message(message1, thread_id="thread_1", task_name=task_name)
        await executor.add_message(message2, thread_id="thread_1", task_name=task_name)
        
        # Wait for processing
        await executor.wait_for_completion(timeout=5)
        
        # Check requeue stats
        stats = executor.get_stats()
        assert stats["messages_requeued"] > 0
        assert stats["messages_processed"] == 2
    
    @pytest.mark.asyncio
    async def test_state_conflict_resolution(self, setup):
        """Test state conflict resolution with mock state resolver."""
        agent, executor = setup
        
        # Since StateResolver is now a pure mock with random results,
        # we just test that the methods can be called without errors
        state_resolver = executor.state_resolver
        thread_id = str(agent.agent_id)
        
        # Test save_state - should not raise
        test_state = {"test": "data"}
        await state_resolver.save_state(thread_id, test_state)
        
        # Test get_state - returns random state
        state1 = await state_resolver.get_state(thread_id)
        state2 = await state_resolver.get_state(thread_id)
        
        # States should be dictionaries (but contents are random)
        assert isinstance(state1, dict)
        assert isinstance(state2, dict)
        
        # Test compare_states - returns random boolean
        are_equal = await state_resolver.compare_states(state1, state2)
        assert isinstance(are_equal, bool)
        
        # Test merge_states - returns one of the input states randomly
        merged = await state_resolver.merge_states(state1, state2, {"new": "state"})
        assert isinstance(merged, dict)
        
        # Test merge_and_save_state - should not raise
        await state_resolver.merge_and_save_state(thread_id, state1, {"updated": "state"})
    
    @pytest.mark.asyncio
    async def test_error_handling(self, setup):
        """Test error handling in message processing."""
        agent, executor = setup
        
        # Make agent fail
        agent.should_fail = True
        
        # Send message
        message = Message(
            content="Error test",
            sender_id=uuid4(),
            receiver_id=agent.agent_id,
            sender_role="user"
        )
        
        await executor.add_message(message, thread_id="error_test_thread")
        
        # Wait for processing
        await asyncio.sleep(1)
        
        # Check error stats
        stats = executor.get_stats()
        assert stats["errors"] == 1
        assert stats["messages_processed"] == 1  # Still counts as processed
    
    @pytest.mark.asyncio
    async def test_task_status_tracking(self, setup):
        """Test task status tracking."""
        agent, executor = setup
        
        # Check initial state
        stats = executor.get_stats()
        assert len(stats["active_tasks"]) == 0
        
        # Send a slow message
        agent.processing_delay = 2.0
        
        message = Message(
            content="Slow message",
            sender_id=uuid4(),
            receiver_id=agent.agent_id,
            sender_role="user"
        )
        
        await executor.add_message(message, thread_id="thread_1", task_name="slow_task")
        
        # Wait a bit for processing to start
        await asyncio.sleep(0.5)
        
        # Check task is active
        stats = executor.get_stats()
        assert "slow_task" in stats["active_tasks"]
        assert executor.task_status["slow_task"] == TaskStatus.IN_PROGRESS
        
        # Wait for completion
        await executor.wait_for_completion(timeout=5)
        
        # Check task is idle
        stats = executor.get_stats()
        assert "slow_task" not in stats["active_tasks"]
        assert executor.task_status["slow_task"] == TaskStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_automatic_task_classification(self, setup):
        """Test automatic task classification."""
        agent, executor = setup
        
        # Send multiple messages
        messages_to_send = 10
        
        for i in range(messages_to_send):
            message = Message(
                content=f"Test message {i}",
                sender_id=uuid4(),
                receiver_id=agent.agent_id,
                sender_role="user"
            )
            await executor.add_message(message, thread_id=f"auto_thread_{i}")
        
        # Wait for processing
        await executor.wait_for_completion(timeout=5)
        
        # Check all messages were processed
        assert len(agent.processed_messages) == messages_to_send
        
        # Check tasks were classified (should be one of the random tasks)
        valid_tasks = ['task_1', 'task_2', 'task_3', 'task_4', 'task_5']
        for processed in agent.processed_messages:
            assert processed["message"].task_name in valid_tasks
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown(self, setup):
        """Test graceful shutdown preserves message queue."""
        agent, executor = setup
        
        # Send messages
        for i in range(5):
            message = Message(
                content=f"Shutdown test {i}",
                sender_id=uuid4(),
                receiver_id=agent.agent_id,
                sender_role="user"
            )
            await executor.add_message(message, thread_id="shutdown_thread")
        
        # Stop immediately (messages still in queue)
        await executor.stop()
        
        # Check that we can get queue size even after stop
        stats = executor.get_stats()
        # Some messages might have been processed, but not all
        assert stats["queue_size"] + len(agent.processed_messages) >= 5
