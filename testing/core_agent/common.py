from langchain_mcp_adapters.client import Connection

from information_flows.settings import settings

MCP_SERVERS: dict[str, Connection] = {
    "policy": {
        "command": "python",
        "args": ["-m", "information_flows.agents.core_agent.mcp.policy_adv.server"],
        "transport": "stdio",
    },
    # "clickup": {
    #     "command": "python",
    #     "args": ["-m", "information_flows.agents.core_agent.mcp.clickup"],
    #     "transport": "stdio",
    # },
    "clickup": {
        "url": f"http://localhost:{settings.CLICKUP_MCP_PORT}/sse",
        "transport": "sse",
    },
    "database": {
        "command": "python",
        "args": [
            "-m",
            "information_flows.agents.core_agent.mcp.text2pandas.server",
        ],
        "transport": "stdio",
    },
    "coding_agent": {
        "url": f"http://localhost:{settings.CODING_AGENT_MCP_PORT}/sse",
        "transport": "sse",
    },
}
