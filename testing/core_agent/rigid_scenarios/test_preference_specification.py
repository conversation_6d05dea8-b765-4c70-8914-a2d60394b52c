from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test_preference_specification(self):
        kristina_id = uuid4()
        kristina = await self.add_employee(
            id=kristina_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=kristina_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        artem_id = uuid4()
        artem = await self.add_employee(
            id=artem_id,
            name="<PERSON><PERSON>",
            integration_id="asr",
            agent_factory=AgentFactory(
                employee_id=artem_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await self.add_relation(kristina, artem)

        await self.update_preferences(
            artem,
            "When anyone asks about my availability, pass the request to me directly.",
        )

        await kristina.send_message("Ask <PERSON><PERSON> if he is available, please.")

        await artem.receive_message()
        await artem.send_message("Yes, I am available.")

        await kristina.receive_message()
