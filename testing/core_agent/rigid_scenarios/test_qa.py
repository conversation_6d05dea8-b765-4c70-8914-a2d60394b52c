from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test_simple_qa(self):
        employee_id = uuid4()
        employee = await self.add_employee(
            id=employee_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await employee.send_message(
            # "I would like to take a vacation from 25 July to 30 July, what should I do?"
            "Hi, how are you doing?"
            # "What do you know about me from the database?"
        )
        await employee.receive_message()
