from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test_multitasking(self):
        employee_id = uuid4()
        employee = await self.add_employee(
            id=employee_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await employee.send_message(
            # "Hi, how are you doing?"
            "Hi, how are you doing? I would like to know what time is now (without date)."
            # "Hi, how are you doing? I would like to know latest news on the new project and also to get a list of all today's meetings."
            # "Hi, how are you doing? I would like to know latest news on the new project and to get a list of all today's meetings. "
            # "And yes, is there anyone new in the project's team. Also, notify <PERSON>, that we are done."
        )
        await employee.send_message(
            "About the task with time, please add one hour to the returned time, and I want to know what date is today as a second task."
        )
        await employee.receive_message()
        await employee.receive_message()
        await employee.receive_message()

        # await employee.send_message(
        #     "Check again, please, and return both of them together."
        # )
        # await employee.receive_message()
