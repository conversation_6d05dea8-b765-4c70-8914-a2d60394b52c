from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.agents.core_agent.measurement import MeasurementResult
from information_flows.core.measurement import measurement_session
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test(self):
        employee_id = uuid4()
        employee = await self.add_employee(
            id=employee_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await measurement_session.start(
            tag="greeting",
            result_schema=MeasurementResult,
        )

        await employee.send_message("Hi, how are you doing?")
        await employee.receive_message()

        measurement_session.stop()
