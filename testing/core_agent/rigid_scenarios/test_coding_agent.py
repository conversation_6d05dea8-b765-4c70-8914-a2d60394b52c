from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test_coding_agent(self):
        employee_id = uuid4()
        employee = await self.add_employee(
            id=employee_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await employee.send_message("Find a square root of 3421 using <PERSON>'s method!")
        await employee.receive_message()
