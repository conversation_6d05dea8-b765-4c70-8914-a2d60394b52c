from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test_simle_vacation_scenario(self):
        kristina_id = uuid4()
        kristina = await self.add_employee(
            id=kristina_id,
            name="<PERSON><PERSON>",
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=kristina_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        artem_id = uuid4()
        artem = await self.add_employee(
            id=artem_id,
            name="<PERSON><PERSON>",
            integration_id="asr",
            agent_factory=AgentFactory(
                employee_id=artem_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await self.add_relation(kristina, artem)

        await kristina.send_message(
            "I would like to take a vacation from 22.05 to 25.05, what should I do?"
        )
        await kristina.receive_message()

        await kristina.send_message(
            "First I would like you to ask <PERSON><PERSON>, as he is my team lead for approval."
        )

        await artem.receive_message()

        await artem.send_message(
            "Ok, assign it in the ClickUp form to me, please, I will approve it."
        )

        await kristina.receive_message()
