# database_qa

Check how well the agent uses the company database MCP and the accuracy of information retrieved by the database integration. In this scenario an arbitrary employee asks its assistant to find some information in the database or asks a question implying search of some information in the database. A question should require the agent and the database to perform some complex querying logic, e.g. joins of multiple tables, filtering, understanding of semantics of each table. The returned result must be accurate and contain correct values, so it is best for the evaluation to compare actual and expected outputs exactly.
