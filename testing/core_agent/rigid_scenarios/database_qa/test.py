from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.agents.core_agent.measurement import MeasurementResult
from information_flows.core.measurement import measurement_session
from testing.core_agent.common import MCP_SERVERS
from testing.rigid.scenario import ScenarioTestCase


class TestCoreAgent(ScenarioTestCase):
    async def test(self):
        employee_id = uuid4()
        employee = await self.add_employee(
            id=employee_id,
            name="Valentina Zhmura",
            integration_id="vzh",
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await measurement_session.start(
            tag="database_qa",
            result_schema=MeasurementResult,
        )

        await employee.send_message(
            "What other employees are involved on a project the same as Sylvestr? From which team is each of them, and how many vacation days has available?"
        )
        await employee.receive_message()

        measurement_session.stop()
