import asyncio
import atexit
import time
from contextlib import asynccontextmanager
from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from testing.rigid.scenario import ScenarioTestCase

# Module-level storage for test results
TEST_RESULTS = {}


def print_final_statistics():
    """Print final statistics at the very end of the test run."""
    if TEST_RESULTS:
        print("\n\n" + "="*80)
        print("FINAL STATISTICS SUMMARY - SYNCHRONOUS VS PARALLEL PROCESSING")
        print("="*80)
        
        if 'test1' in TEST_RESULTS:
            test1 = TEST_RESULTS['test1']
            print("\nTEST 1: Multiple Tasks in One Message (5 tasks)")
            print("-" * 60)
            print(f"Synchronous Processing Time: {test1['sync_time']:.2f} seconds")
            print(f"Parallel Processing Time:    {test1['par_time']:.2f} seconds")
            print(f"Speed Improvement:           {test1['speedup']:.2f}x faster")
            print(f"Time Saved:                  {test1['time_saved']:.2f} seconds")
            print(f"Improvement Percentage:      {test1['improvement_percent']:.1f}%")
        
        if 'test2' in TEST_RESULTS:
            test2 = TEST_RESULTS['test2']
            print("\nTEST 2: Multiple Messages Sent Sequentially (5 tasks)")
            print("-" * 60)
            print(f"Synchronous Processing Time: {test2['sync_time']:.2f} seconds")
            print(f"Parallel Processing Time:    {test2['par_time']:.2f} seconds")
            if test2['speedup'] > 0:
                print(f"Speed Improvement:           {test2['speedup']:.2f}x faster")
                print(f"Time Saved:                  {test2['time_saved']:.2f} seconds")
                print(f"Improvement Percentage:      {test2['improvement_percent']:.1f}%")
            else:
                print("Speed Improvement:           No significant improvement")
                print("Note: Sequential message sending limits parallel benefits")


# Register the function to run at program exit
atexit.register(print_final_statistics)


class SynchronousAgentFactory(AgentFactory):
    """Agent factory that disables parallel processing."""
    @asynccontextmanager
    async def context(self, checkpointer):
        async with super().context(checkpointer) as agent:
            agent.enable_parallel_processing = False
            yield agent


class TestParallelProcessing(ScenarioTestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.test_results = {}
        self.agent_factories = []
    
    async def asyncSetUp(self):
        """Set up test resources."""
        await super().asyncSetUp()
        self.agent_factories = []
    
    async def _cleanup_agents(self):
        """Clean up all agent factories and their resources."""
        # Give async tasks time to complete
        await asyncio.sleep(0.5)
        
        # Clean up each agent factory
        for factory in self.agent_factories:
            try:
                # If the factory has a close method, use it
                if hasattr(factory, 'close'):
                    await factory.close()
                # If it has an http client, close it
                if hasattr(factory, '_client'):
                    try:
                        await factory._client.close()
                    except RuntimeError as e:
                        if "Event loop is closed" not in str(e):
                            raise
            except Exception as e:
                print(f"Warning: Error cleaning up agent factory: {e}")
        
        self.agent_factories.clear()
    
    async def test_multiple_tasks_in_one_message(self):
        """Test 1: Compare sync vs parallel when multiple tasks are in one message."""
        print("\n" + "="*80)
        print("TEST 1: Multiple Tasks in One Message (5 tasks)")
        print("="*80)
        
        # Test with SYNCHRONOUS processing
        print("\n--- SYNCHRONOUS PROCESSING ---")
        sync_employee_id = uuid4()
        sync_factory = SynchronousAgentFactory(
            employee_id=sync_employee_id,
            specification_dir="./information_flows/agents/core_agent/specification",
            mcp_servers={},
        )
        self.agent_factories.append(sync_factory)
        sync_employee = await self.add_employee(
            id=sync_employee_id,
            name="Sync Worker",
            integration_id="sync_worker",
            agent_factory=sync_factory,
        )
        
        sync_start = time.time()
        await sync_employee.send_message(
            "Please help me with these tasks: "
            "1. What time is it? (task: time_check) "
            "2. What's today's date? (task: date_check) "
            "3. Calculate 10 + 20 (task: math_one) "
            "4. Calculate 30 + 40 (task: math_two) "
            "5. Generate a random number (task: random_num)"
        )
        
        # Receive all 5 responses
        for i in range(5):
            response = await sync_employee.receive_message()
            print(f"  Response {i+1} received")
        
        sync_time = time.time() - sync_start
        print(f"\nSynchronous total time: {sync_time:.2f} seconds")
        
        # Test with PARALLEL processing
        print("\n--- PARALLEL PROCESSING ---")
        par_employee_id = uuid4()
        par_factory = AgentFactory(  # Default is parallel=True
            employee_id=par_employee_id,
            specification_dir="./information_flows/agents/core_agent/specification",
            mcp_servers={},
        )
        self.agent_factories.append(par_factory)
        par_employee = await self.add_employee(
            id=par_employee_id,
            name="Parallel Worker",
            integration_id="par_worker",
            agent_factory=par_factory,
        )
        
        par_start = time.time()
        await par_employee.send_message(
            "Please help me with these tasks: "
            "1. What time is it? (task: time_check) "
            "2. What's today's date? (task: date_check) "
            "3. Calculate 10 + 20 (task: math_one) "
            "4. Calculate 30 + 40 (task: math_two) "
            "5. Generate a random number (task: random_num)"
        )
        
        # Receive all 5 responses
        for i in range(5):
            response = await par_employee.receive_message()
            print(f"  Response {i+1} received")
        
        par_time = time.time() - par_start
        print(f"\nParallel total time: {par_time:.2f} seconds")
        
        # Store results
        self.test_results['test1'] = {
            'sync_time': sync_time,
            'par_time': par_time,
            'speedup': sync_time/par_time,
            'time_saved': sync_time - par_time,
            'improvement_percent': ((sync_time - par_time)/sync_time)*100
        }
        
        # Compare results
        print(f"\n{'='*50}")
        print("COMPARISON:")
        print(f"Synchronous: {sync_time:.2f}s")
        print(f"Parallel: {par_time:.2f}s")
        print(f"Speed improvement: {sync_time/par_time:.2f}x faster")
        print(f"Time saved: {sync_time - par_time:.2f}s ({((sync_time - par_time)/sync_time)*100:.1f}%)")
        print(f"{'='*50}")

    async def test_multiple_messages_sent_sequentially(self):
        """Test 2: Compare sync vs parallel when tasks are sent in separate messages."""
        print("\n" + "="*80)
        print("TEST 2: Multiple Messages Sent One After Another")
        print("="*80)
        
        # Test with SYNCHRONOUS processing
        print("\n--- SYNCHRONOUS PROCESSING ---")
        sync_employee_id = uuid4()
        sync_factory2 = SynchronousAgentFactory(
            employee_id=sync_employee_id,
            specification_dir="./information_flows/agents/core_agent/specification",
            mcp_servers={},
        )
        self.agent_factories.append(sync_factory2)
        sync_employee = await self.add_employee(
            id=sync_employee_id,
            name="Sync Worker 2",
            integration_id="sync_worker2",
            agent_factory=sync_factory2,
        )
        
        sync_start = time.time()
        
        # Send 5 separate messages
        messages = [
            "What time is it now? (task: time_check)",
            "What's today's date? (task: date_check)",
            "Calculate 15 + 25 for me (task: math_calc)",
            "Generate a random number (task: random_gen)",
            "Tell me a fun fact (task: fun_fact)"
        ]
        
        for msg in messages:
            await sync_employee.send_message(msg)
        
        # Receive all 5 responses
        for i in range(5):
            response = await sync_employee.receive_message()
            print(f"  Response {i+1} received")
        
        sync_time = time.time() - sync_start
        print(f"\nSynchronous total time: {sync_time:.2f} seconds")
        
        # Test with PARALLEL processing
        print("\n--- PARALLEL PROCESSING ---")
        par_employee_id = uuid4()
        par_factory2 = AgentFactory(  # Default is parallel=True
            employee_id=par_employee_id,
            specification_dir="./information_flows/agents/core_agent/specification",
            mcp_servers={},
        )
        self.agent_factories.append(par_factory2)
        par_employee = await self.add_employee(
            id=par_employee_id,
            name="Parallel Worker 2",
            integration_id="par_worker2",
            agent_factory=par_factory2,
        )
        
        par_start = time.time()
        
        # Send same 5 separate messages
        for msg in messages:
            await par_employee.send_message(msg)
        
        # Receive all 5 responses
        for i in range(5):
            response = await par_employee.receive_message()
            print(f"  Response {i+1} received")
        
        par_time = time.time() - par_start
        print(f"\nParallel total time: {par_time:.2f} seconds")
        
        # Store results
        self.test_results['test2'] = {
            'sync_time': sync_time,
            'par_time': par_time,
            'speedup': sync_time/par_time if par_time < sync_time else 0,
            'time_saved': sync_time - par_time if par_time < sync_time else 0,
            'improvement_percent': ((sync_time - par_time)/sync_time)*100 if par_time < sync_time else 0
        }
        
        # Compare results
        print(f"\n{'='*50}")
        print("COMPARISON:")
        print(f"Synchronous: {sync_time:.2f}s")
        print(f"Parallel: {par_time:.2f}s")
        if par_time < sync_time:
            print(f"Speed improvement: {sync_time/par_time:.2f}x faster")
            print(f"Time saved: {sync_time - par_time:.2f}s ({((sync_time - par_time)/sync_time)*100:.1f}%)")
        else:
            print("Note: Parallel may not show improvement when messages are sent sequentially")
            print("because each message is processed before the next is sent.")
        print(f"{'='*50}")
        
        # Store results globally
        global TEST_RESULTS
        TEST_RESULTS.update(self.test_results)
    
    async def asyncTearDown(self):
        """Called after all tests in the class have run."""
        # Clean up agents before calling super tearDown
        await self._cleanup_agents()
        
        # Now call parent tearDown
        await super().asyncTearDown()
        
        # Print final statistics if we have results
        if hasattr(self, 'test_results') and self.test_results:
            self._print_final_statistics()
    
    def _print_final_statistics(self):
        """Print comprehensive statistics for both test approaches."""
        print("\n" + "="*80)
        print("FINAL STATISTICS SUMMARY - SYNCHRONOUS VS PARALLEL PROCESSING")
        print("="*80)
        
        if 'test1' in self.test_results:
            test1 = self.test_results['test1']
            print("\nTEST 1: Multiple Tasks in One Message (5 tasks)")
            print("-" * 60)
            print(f"Synchronous Processing Time: {test1['sync_time']:.2f} seconds")
            print(f"Parallel Processing Time:    {test1['par_time']:.2f} seconds")
            print(f"Speed Improvement:           {test1['speedup']:.2f}x faster")
            print(f"Time Saved:                  {test1['time_saved']:.2f} seconds")
            print(f"Improvement Percentage:      {test1['improvement_percent']:.1f}%")
        
        if 'test2' in self.test_results:
            test2 = self.test_results['test2']
            print("\nTEST 2: Multiple Messages Sent Sequentially (5 tasks)")
            print("-" * 60)
            print(f"Synchronous Processing Time: {test2['sync_time']:.2f} seconds")
            print(f"Parallel Processing Time:    {test2['par_time']:.2f} seconds")
            if test2['speedup'] > 0:
                print(f"Speed Improvement:           {test2['speedup']:.2f}x faster")
                print(f"Time Saved:                  {test2['time_saved']:.2f} seconds")
                print(f"Improvement Percentage:      {test2['improvement_percent']:.1f}%")
            else:
                print("Speed Improvement:           No significant improvement")
                print("Note: Sequential message sending limits parallel benefits")
