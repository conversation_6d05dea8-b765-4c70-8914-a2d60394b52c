context: |
  Anadea is a software development company you are an employee of. Anadea has a diverse organizational structure with many specialized teams and managerial and business wings leaded by the CEO. Each main unit, typically referred to as a **team**, functions as a group under the guidance of a **team lead**. While termed **teams**, these groups focus on domains or development directions rather than specific projects. However, a dedicated team is composed for each project out of members of these main specialized teams, and you should distinguish project teams from the company teams. Examples of the company teams include the mobile developers' team, the deep learning (DL) team, and the sales team.

  - Our company has the **company's policy** that is defined in a set of documents with rules and policies on different topics, ranging from salaries, vacations, recruiting to project management and sales endeavors.
  - Our company's central store of information is the **company's database** that contains information about employees, projects and other usual business affairs.
  - Almost every employee in our company has a personal AI assistant. The key purpose of personal assistants is to facilitate and simplify communication between employees of our company, and to enhance the flows of information among them by serving as an intermediaries that perform time-consuming conversations on behalf of employees.

actors:
  - id: kristina
    name: <PERSON><PERSON>
    pretext: |
      You are an engineer in one of the teams. You want to take a vacation and you have to get permission from your team lead, <PERSON><PERSON>.
    script: |
      Steps:
      1. You tell your assistant: I would like to take a vacation from August 22 to August 28, what should I do?.
      2. Your assistant describes the rules from the company's policy document and possibly your current accumulated vacation days. The rules should tell that it is required to ask permission a team lead, so your assistant must ask you whether you want it to contact the team lead on your behalf.
      3. You tell your assistant that you first would like you to ask <PERSON><PERSON> for approval. Your assistant communicates with the assistant of <PERSON><PERSON> and passes your request to him.
      5. <PERSON><PERSON>'s assistant receives your request, passes it to <PERSON>m and tells your assistant the result. Your assistant returns to you telling that <PERSON><PERSON> will approve the request and asks whether you want to make <PERSON>lickUp request.
      6. You confirm that you want your assistant to make the ClickUp vacation request on your behalf.
    finish: |
      Your assistant creates a ClickUp request for your vacation.

  - id: artem
    name: Artem Strzhyhotsky
    pretext: |
      You are a team lead of one of the teams. One of your engineers, Kristina Kurhanska, would like to take a vacation and you have to make a decision whether to approve it or not.
    script: |
      Steps:
      1. Assistant of Kristina tells you that Kristina requests a vacation and describes any additional details.
      2. You tell the assistant that you agree to approve the vacation and you will approve a ClickUp vacation request after it is created as soon as possible.
    finish: |
      You confirm your assistant that you will approve the request of Kristina.
