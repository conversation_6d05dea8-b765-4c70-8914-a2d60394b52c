from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.agents.core_agent.measurement import MeasurementResult
from testing.core_agent.common import MCP_SERVERS
from testing.evaluation.behavioral_aspects import (
    CommunicateWithAssistantAspect,
    CommunicateWithChiefAspect,
    ToolCallAspect,
)
from testing.simulation.scenario import SimulationScenario
from testing.simulation.simulation import SimulationTestCase


class TestCoreAgent(SimulationTestCase):
    async def test_vacation_scenario(self):
        scenario = SimulationScenario.from_file(
            "./testing/core_agent/simulation_scenarios/duo_vacation/scenario.yaml"
        )
        kristina_id = uuid4()
        kristina = await self.add_employee(
            id=kristina_id,
            name="<PERSON><PERSON>",
            scenario=scenario.for_actor("kristina"),
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=kristina_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        artem_id = uuid4()
        artem = await self.add_employee(
            id=artem_id,
            name="<PERSON><PERSON>",
            scenario=scenario.for_actor("artem"),
            integration_id="asr",
            agent_factory=AgentFactory(
                employee_id=artem_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await self.add_relation(kristina, artem)

        self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina.id,
                tool_name="get_company_policy",
                behavior="An agent reads company policy on vacations.",
                reason="""
                    The assistant reads company's policy to find out rules on vacations and
                    all necessary steps and procedures of requesting and taking a vacation.
                    """,
                effect="""
                    The assistant finds out relevant details on vacation policy. The details
                    explain that it is necessary to have enough paid vacation days accured, then
                    to get approval from a team lead, and submit a ClickUp vacation request.
                    """,
            ),
        )

        self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina.id,
                tool_name="ask_company_database",
                behavior="An agent checks company's database for Kristina's accumulated days.",
                reason="""
                    An agent learned from the vacation policy that it is necessary to have
                    enough vacation days accumulated to take a vacation, so it check the database
                    for the current number of accured days.
                    """,
                effect="""
                    The database returns the number of days and the agent takes it into consideration.
                    """,
            ),
        )

        self.expect_behavior(
            CommunicateWithChiefAspect(
                agent_id=kristina.id,
                behavior="An agent explains Kristina a company policy on vacations.",
                reason="""
                    The assistant read company's policy to find out rules on vacations
                    and decided to communicate them to its chief Kristina.
                    """,
                effect="""
                    The message explains that it is necessary to have enough paid vacation days
                    accured, then to get approval from a team lead, and submit a ClickUp vacation
                    request.
                    """,
            )
        )

        self.expect_behavior(
            CommunicateWithAssistantAspect(
                agent_id=kristina.id,
                receiver_id=artem.id,
                behavior="""
                    An agent of Kristina asks Artem's assistant whether Artem will approve the
                    vacation request of Kristina.""",
                reason="""
                    The company's policy requires to get approval from a team lead to get a vacation,
                    and Kristina confirms that her assistant should go an asks Artem for approval.
                    """,
                effect="""
                    Kristina's assistant passes all necessary details about the vacation request to
                    Artem's assistant, and asks whether Artem will approve the request.
                    """,
            )
        )

        self.expect_behavior(
            CommunicateWithChiefAspect(
                agent_id=artem.id,
                behavior="An agent passes the request to approve vacation from Kristina's assistant to Artem.",
                reason="""
                    The assistant of Artem received a message from Kristina's assistant asking
                    to get approval/rejection from Artem, so it passes the request to Artem.
                    """,
                effect="""
                    The message contains all nessary details received from Kristina's assistant,
                    and asks Artem's decision.
                    """,
            )
        )

        completion_criteria = self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina.id,
                tool_name="clickup_vacation_request",
                behavior="An agent submits a ClickUp vacation request for Kristina.",
                reason="""
                    Kristina confirms that she wants her assistant to submit a ClickUp vacation
                    request after Artem has approved the vacation verbally.
                    """,
                effect="""
                    The assistant submits the request with correct dates and other details,
                    and it is successfully recorded by ClickUp.
                    """,
            ),
        )

        await self.run_simulation(
            tag="duo_vacation",
            result_schema=MeasurementResult,
            initiating_employees=[kristina],
            completion_criteria=completion_criteria,
        )

        self.print_finish_reason()
