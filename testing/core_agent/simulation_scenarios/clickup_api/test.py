from uuid import uuid4

from langchain_mcp_adapters.client import Connection

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.agents.core_agent.measurement import MeasurementResult
from information_flows.settings import settings
from testing.evaluation.behavioral_aspects import (
    CommunicateWithAssistantAspect,
    CommunicateWithChiefAspect,
    ToolCallAspect,
)
from testing.simulation.scenario import SimulationScenario
from testing.simulation.simulation import SimulationTestCase

MCP_SERVERS: dict[str, Connection] = {
    "policy": {
        "command": "python",
        "args": ["-m", "information_flows.agents.core_agent.mcp.policy_adv.server"],
        "transport": "stdio",
    },
    # "clickup": {
    #     "command": "python",
    #     "args": ["-m", "information_flows.mcp.clickup.server"],
    #     "transport": "stdio",
    # },
    "clickup": {
        "url": f"http://localhost:{settings.CLICKUP_MCP_PORT}/sse",
        "transport": "sse",
    },
    "database": {
        "command": "python",
        "args": [
            "-m",
            "information_flows.agents.core_agent.mcp.text2pandas.server",
        ],
        "transport": "stdio",
    },
    "coding_agent": {
        "url": f"http://localhost:{settings.CODING_AGENT_MCP_PORT}/sse",
        "transport": "sse",
    },
}


class TestCoreAgent(SimulationTestCase):
    async def test_simple_qa(self):
        scenario = SimulationScenario.from_file(
            "./testing/core_agent/simulation_scenarios/clickup_api/scenario.yaml"
        )
        employee_id = uuid4()
        integration_id = "krk"
        kristina_employee = await self.add_employee(
            id=employee_id,
            scenario=scenario.for_actor("kristina"),
            name="Kristina Kurhanska",
            integration_id=integration_id,
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )
        employee_id = uuid4()
        integration_id = "asr"
        artem_employee = await self.add_employee(
            id=employee_id,
            scenario=scenario.for_actor("artem"),
            name="Artem Strzhyhotsky",
            integration_id=integration_id,
            agent_factory=AgentFactory(
                employee_id=employee_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        await self.add_relation(kristina_employee, artem_employee)

        self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina_employee.id,
                tool_name="get_company_policy",
                behavior="An agent reads company policy on vacations.",
                reason="""
                    The assistant reads company's policy to find out rules on vacations and
                    all necessary steps and procedures of requesting and taking a vacation.
                    """,
                effect="""
                    The assistant finds out relevant details on vacation policy. The details
                    explain that it is necessary to have enough paid vacation days accured, then
                    to get approval from a team lead, and submit a ClickUp vacation request.
                    """,
            ),
        )

        self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina_employee.id,
                tool_name="ask_company_database",
                behavior="An agent checks company's database for Kristina's accumulated days.",
                reason="""
                    An agent learned from the vacation policy that it is necessary to have
                    enough vacation days accumulated to take a vacation, so it check the database
                    for the current number of accured days.
                    """,
                effect="""
                    The database returns the number of days and the agent takes it into consideration.
                    """,
            ),
        )

        self.expect_behavior(
            CommunicateWithChiefAspect(
                agent_id=kristina_employee.id,
                behavior="An agent explains Kristina a company policy on vacations.",
                reason="""
                    The assistant read company's policy to find out rules on vacations
                    and decided to communicate them to its chief Kristina.
                    """,
                effect="""
                    The message explains that it is necessary to have enough paid vacation days
                    accured, then to get approval from a team lead, and submit a ClickUp vacation
                    request.
                    """,
            )
        )

        self.expect_behavior(
            CommunicateWithAssistantAspect(
                agent_id=kristina_employee.id,
                receiver_id=artem_employee.id,
                behavior="""
                    An agent of Kristina asks Artem's assistant whether Artem will approve the
                    vacation request of Kristina.""",
                reason="""
                    The company's policy requires to get approval from a team lead to get a vacation,
                    and Kristina confirms that her assistant should go an asks Artem for approval.
                    """,
                effect="""
                    Kristina's assistant passes all necessary details about the vacation request to
                    Artem's assistant, and asks whether Artem will approve the request.
                    """,
            )
        )

        self.expect_behavior(
            ToolCallAspect(
                agent_id=artem_employee.id,
                tool_name="find_all_user_tasks",
                behavior="An agent finds current tasks of Kristina.",
                reason="""
                    Artem wants to know current tasks of Kristina before making a decision,
                    and asks his assistant to find the list of tasks in ClickUp.
                    """,
                effect="""
                    The assistant uses ClickUp to find the tasks of Kristina and then present
                    them to Artem.
                    """,
            ),
        )

        self.expect_behavior(
            CommunicateWithChiefAspect(
                agent_id=artem_employee.id,
                behavior="An agent passes the request to approve vacation from Kristina's assistant to Artem.",
                reason="""
                    The assistant of Artem received a message from Kristina's assistant asking
                    to get approval/rejection from Artem, so it passes the request to Artem.
                    """,
                effect="""
                    The message contains all nessary details received from Kristina's assistant,
                    and asks Artem's decision.
                    """,
            )
        )

        completion_criteria = self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina_employee.id,
                tool_name="submit_vacation",
                behavior="An agent submits a ClickUp vacation request for Kristina.",
                reason="""
                    Kristina confirms that she wants her assistant to submit a ClickUp vacation
                    request after Artem has approved the vacation verbally.
                    """,
                effect="""
                    The assistant submits the request with correct dates and other details,
                    and it is successfully recorded by ClickUp.
                    """,
            ),
        )

        await self.run_simulation(
            tag="clickup_api",
            result_schema=MeasurementResult,
            initiating_employees=[kristina_employee],
            completion_criteria=completion_criteria,
        )

        self.print_finish_reason()

        # await self.add_relation(kristina_employee, artem_employee)

        # await kristina_employee.send_message(
        #     "I would like to take a vacation starting from June 27, 2025, to June 28, 2025 my clickup_id 81772234. My teamlead Artem.artem clickup_id 81772234 "
        # )

        # await artem_employee.receive_message()

        # await artem_employee.send_message(
        #     "Yes, please go ahead and approve Kristina's vacation."
        # )
        # await kristina_employee.receive_message()
