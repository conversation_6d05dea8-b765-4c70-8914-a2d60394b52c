from uuid import uuid4

from information_flows.agents.core_agent.agent_factory import AgentFactory
from information_flows.agents.core_agent.measurement import MeasurementResult
from testing.core_agent.common import MCP_SERVERS
from testing.evaluation.behavioral_aspects import (
    CommunicateWithChiefAspect,
    ToolCallAspect,
)
from testing.simulation.scenario import Simulation<PERSON>cenario
from testing.simulation.simulation import SimulationTestCase


class TestCoreAgent(SimulationTestCase):
    async def test(self):
        scenario = SimulationScenario.from_file(
            "./testing/core_agent/simulation_scenarios/policy_qa/scenario.yaml"
        )
        kristina_id = uuid4()
        kristina = await self.add_employee(
            id=kristina_id,
            name="<PERSON><PERSON>",
            scenario=scenario.for_actor("kristina"),
            integration_id="krk",
            agent_factory=AgentFactory(
                employee_id=kristina_id,
                specification_dir="./information_flows/agents/core_agent/specification",
                mcp_servers=MCP_SERVERS,
            ),
        )

        self.expect_behavior(
            ToolCallAspect(
                agent_id=kristina.id,
                tool_name="get_company_policy",
                behavior="An agent reads company policy on vacations.",
                reason="""
                    The assistant reads company's policy to find out rules on vacations and
                    all necessary steps and procedures of requesting and taking a vacation.
                    """,
                effect="""
                    The assistant finds out relevant details on vacation policy. The details
                    explain that it is necessary to have enough paid vacation days accured, then
                    to get approval from a team lead, and submit a ClickUp vacation request.
                    """,
            ),
        )

        completion_criteria = self.expect_behavior(
            CommunicateWithChiefAspect(
                agent_id=kristina.id,
                behavior="An agent explains Kristina a company policy on vacations.",
                reason="""
                    The assistant read company's policy to find out rules on vacations
                    and decided to communicate them to its chief Kristina.
                    """,
                effect="""
                    The message explains that it is necessary to have enough paid vacation days
                    accured, then to get approval from a team lead, and submit a ClickUp vacation
                    request.
                    """,
            )
        )

        await self.run_simulation(
            tag="policy_qa",
            result_schema=MeasurementResult,
            initiating_employees=[kristina],
            completion_criteria=completion_criteria,
        )

        self.print_finish_reason()
