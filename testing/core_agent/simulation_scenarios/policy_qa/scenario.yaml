context: |
  Anadea is a software development company you are an employee of. Anadea has a diverse organizational structure with many specialized teams and managerial and business wings leaded by the CEO. Each main unit, typically referred to as a **team**, functions as a group under the guidance of a **team lead**. While termed **teams**, these groups focus on domains or development directions rather than specific projects. However, a dedicated team is composed for each project out of members of these main specialized teams, and you should distinguish project teams from the company teams. Examples of the company teams include the mobile developers' team, the deep learning (DL) team, and the sales team.

  - Our company has the **company's policy** that is defined in a set of documents with rules and policies on different topics, ranging from salaries, vacations, recruiting to project management and sales endeavors.
  - Our company's central store of information is the **company's database** that contains information about employees, projects and other usual business affairs.
  - Almost every employee in our company has a personal AI assistant. The key purpose of personal assistants is to facilitate and simplify communication between employees of our company, and to enhance the flows of information among them by serving as an intermediaris that perform time-consuming conversations on behalf of employees.

actors:
  - id: kristina
    name: <PERSON><PERSON>
    pretext: |
      You are an engineer in one of the teams. You want to take a vacation and you have to get permission from your team lead, <PERSON><PERSON>.
    script: |
      Steps:
      1. You ask you assistant what is the company's policy on vacations.
      2. Your assistant returns you a detailed description of the policy.
    finish: You receive a summary of a company's policy on vacations.
