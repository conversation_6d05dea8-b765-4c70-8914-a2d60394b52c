import asyncio
from typing import Any
from uuid import UUID

from rich.markdown import Markdown

from information_flows.api.employee.service import EmployeeService
from information_flows.core.agent import BaseAgentFactory
from information_flows.core.executor import AgentDiedMessage
from information_flows.core.logger import agent_logger
from information_flows.core.pool import <PERSON><PERSON><PERSON>
from information_flows.core.types import Message
from testing.base_test_case import BaseTestCase


class ScenarioError(Exception):
    pass


class Employee:
    """A utility class to imitate users in conversations with agents."""

    def __init__(
        self,
        id: UUID,
        name: str,
        *,
        pool: AgentPool,
        employee_service: EmployeeService,
    ):
        self.id = id
        self.name = name
        self.pool = pool
        self.employee_service = employee_service
        self.in_queue = asyncio.Queue[Message]()
        self.pool.add_message_callback(self.id, self.receive_callback)

    async def receive_callback(self, message: Message) -> None:
        await self.in_queue.put(message)

    async def send_message(self, content: Any) -> None:
        """Send a message to an assistant via the agent pool."""
        await self.pool.put_message(
            Message(
                content=content,
                receiver_id=self.id,
                sender_id=self.id,
                sender_role="user",
            )
        )

    async def receive_message(self) -> Message:
        """Block and wait until the next incoming message arrives, then return it."""
        in_message = await self.in_queue.get()
        self.in_queue.task_done()
        if isinstance(in_message, AgentDiedMessage):
            raise ScenarioError(f"{self.name}'s agent died, stopping the scenario")
        agent_logger.employee_received(self.id, in_message.content, self.name)
        return in_message


class ScenarioTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()

        self.console.print(Markdown("# Scenario"))

    async def add_employee(
        self,
        id: UUID,
        name: str,
        integration_id: str,
        agent_factory: BaseAgentFactory,
    ) -> Employee:
        self.test_data_tracker.add_employee_id(id)
        self.test_data_tracker.add_integration_id(integration_id)

        id = await self.employee_service.create_employee(id)
        await self.employee_service.create_integration(id, integration_id, name)
        employee = Employee(
            id,
            name,
            pool=self.agent_pool,
            employee_service=self.employee_service,
        )
        await self.agent_pool.spawn(id, agent_factory)
        return employee

    async def add_relation(
        self,
        from_employee: Employee,
        to_employee: Employee,
    ) -> None:
        await self.employee_service.create_relation(from_employee.id, to_employee.id)

    async def update_preferences(
        self, employee: Employee, updated_preferences: str | None
    ) -> None:
        await self.employee_service.update_static_preferences(
            employee.id, updated_preferences
        )
