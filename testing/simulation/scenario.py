from typing import Annotated

import yaml
from pydantic import BaseModel, StringConstraints, model_validator


class ActorScript(BaseModel):
    id: str
    name: str
    context: str
    pretext: Annotated[str, StringConstraints(strip_whitespace=True)]
    script: Annotated[str, StringConstraints(strip_whitespace=True)]
    finish: str


class SimulationScenario(BaseModel):
    actors: list[ActorScript]

    @model_validator(mode="before")
    @classmethod
    def validate(cls, data: dict) -> dict:
        actors = []
        for actor in data["actors"]:
            actor["context"] = data["context"]
            actors.append(actor)
        data.pop("context")
        data["actors"] = actors
        return data

    @classmethod
    def from_file(cls, filepath: str) -> "SimulationScenario":
        with open(filepath) as file:
            content = yaml.safe_load(file)
        return cls(**content)

    def for_actor(self, actor_id: str) -> ActorScript:
        for actor in self.actors:
            if actor.id == actor_id:
                return actor
        raise ValueError(f"a script for `actor_id` does not exist: {actor_id}")
