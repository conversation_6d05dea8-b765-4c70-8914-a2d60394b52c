import asyncio
import logging
import os
import traceback
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from enum import StrEnum, auto
from pathlib import Path
from uuid import UUID

import aiofiles
from pydantic import BaseModel
from rich.console import Console
from rich.markdown import Markdown
from rich.style import Style

from information_flows.agents.core_agent.measurement import (
    Measurement,
    MeasurementResult,
)
from information_flows.core.agent import BaseAgentFactory
from information_flows.core.measurement import (
    BaseMeasurementResult,
    measurement_session,
)
from testing.base_test_case import BaseTestCase
from testing.evaluation.behavioral_aspects import (
    AspectEvaluationResult,
    AspectEvaluator,
    BehavioralAspect,
)
from testing.simulation.employee import (
    INITIATE_SIMULATION_MESSAGE,
    KILL_EMPLOYEE_MESSAGE,
    Deviation,
    EmployeeActor,
    EmployeeActorEvent,
    SimulationError,
)

logger = logging.getLogger("simulation")
logger.setLevel(logging.INFO)

EVALUATION_DIR = "evaluations"

# a format of filename of evaluation result files
evaluation_filename_format = "{tag}-{timestamp}.json"


class FinishSimulationReason(StrEnum):
    SUCCESS = auto()
    """Completion critia was met."""
    ERROR = auto()
    """An exception occurred in an agent or an employee."""
    IDLE = auto()
    """Idle agents and employees exceed timeout, no one is processing a message."""
    DEVIATION = auto()
    """An agent deviates too much from the scenario and is not going to recover."""
    TIMEOUT = auto()
    """No measurement emitted in a specified interval, the system is not responsive."""


class DeviatedAgent(BaseModel):
    id: UUID
    name: str
    deviations: list[Deviation]


class SimulationResult(BaseModel):
    finish_reason: FinishSimulationReason | None = None
    aspect_evaluation: AspectEvaluationResult
    scenario_deviations: list[DeviatedAgent]
    raised_exception: str | None = None


class SimulationTestCase(BaseTestCase):
    employees: dict[UUID, EmployeeActor]

    def setUp(self):
        super().setUp()

        self.console.print(Markdown("# Simulation"))

        self.employees = {}
        self.aspect_evaluator = AspectEvaluator()
        self.monitor_kill_event = None

        # these attributes must be initialized when simulation starts
        self.tag = None
        self.completion_criteria = None
        self.started_at = None
        self.last_measurement_at = None
        self.tasks_to_cancel: list[asyncio.Task] = []

        # these attributes will be saved in the simulation result
        self.finish_reason = None
        self.raised_exception = None
        self.deviations: dict[UUID, list[Deviation]] = defaultdict(list)

        os.makedirs(EVALUATION_DIR, exist_ok=True)

    async def add_employee(
        self,
        id: UUID,
        name: str,
        scenario: str,
        integration_id: str,
        agent_factory: BaseAgentFactory,
    ) -> EmployeeActor:
        self.test_data_tracker.add_employee_id(id)
        self.test_data_tracker.add_integration_id(integration_id)

        id = await self.employee_service.create_employee(id)
        await self.employee_service.create_integration(id, integration_id, name)

        employee = EmployeeActor(
            id=id,
            name=name,
            script=scenario,
            pool=self.agent_pool,
            langsmith_trace=True,
        )
        employee.add_event_callback(self.handle_employee_event)
        self.employees[id] = employee
        await self.agent_pool.spawn(id, agent_factory)
        return employee

    async def add_relation(
        self,
        from_employee: EmployeeActor,
        to_employee: EmployeeActor,
    ) -> None:
        await self.employee_service.create_relation(from_employee.id, to_employee.id)

    async def update_preferences(
        self, employee: EmployeeActor, updated_preferences: str | None
    ) -> None:
        await self.employee_service.update_static_preferences(
            employee.id, updated_preferences
        )

    def expect_behavior[T: BehavioralAspect](self, aspect: T) -> T:
        """Record a new expected aspect of behavior in the simulation."""
        return self.aspect_evaluator.expect(aspect)

    def start_monitor(self) -> None:
        """Start simulation health monitoring to interrupt it on any issues."""
        if self.monitor_kill_event:
            raise SimulationError("the simulation monitor have already started")
        self.monitor_kill_event = asyncio.Event()
        asyncio.create_task(self.monitor(self.monitor_kill_event))

    def stop_monitor(self) -> None:
        """Stop monitoring simulation health and properties."""
        if not self.monitor_kill_event:
            raise SimulationError("the simulation monitor have not started yet")
        self.monitor_kill_event.set()
        self.monitor_kill_event = None

    async def monitor(self, kill_event: asyncio.Event, frequence: int = 1) -> None:
        """Monitor critical properties of the simulation and stop it when necessary."""
        while not kill_event.is_set():
            if self.check_deviated_theshold():
                # stop simulation as deviation exceeded a threshold
                await self.stop_simulation(FinishSimulationReason.DEVIATION)
            if self.check_system_timeout():
                # stop simulation as its duration exceeded the limit
                await self.stop_simulation(FinishSimulationReason.TIMEOUT)
            if self.check_idle_timeout():
                # stop simulation as everyone is idle for too long
                await self.stop_simulation(FinishSimulationReason.IDLE)
            await asyncio.sleep(frequence)

    def check_deviated_theshold(self) -> bool:
        """Check if behavior of agents deviated from the scenario more than a threshold."""
        counter = Counter({"low": 0, "medium": 0, "high": 0})
        for deviations in self.deviations.values():
            counter.update(deviation.significance for deviation in deviations)

        if counter["low"] > 2:
            return True
        elif counter["medium"] >= 1:
            return True
        elif counter["high"] >= 1:
            return True
        else:
            return False

    def check_idle_timeout(self, idle_timeout: float = 10) -> bool:
        """Check if all employees and agents are idle for longer than timeout."""
        idle_timeout = timedelta(seconds=idle_timeout)

        idle_employees = 0
        idle_agents, running_agents = 0, 0
        for employee in self.employees.values():
            if employee.activity_recorder.idle_for() > idle_timeout:
                idle_employees += 1
            if self.agent_pool.has_running_agent(employee.id):
                running_agents += 1
                agent = self.agent_pool.get_running_agent(employee.id)
                if agent.activity_recorder.idle_for() > idle_timeout:
                    idle_agents += 1

        all_employees_idle = idle_employees == len(self.employees)
        all_agents_idle = idle_agents == running_agents

        if all_employees_idle and all_agents_idle:
            return True
        return False

    def check_system_timeout(
        self,
        no_response_timeout: float = 2 * 60,
        max_simulation_duration: float = 10 * 60,
    ) -> bool:
        """Check if the system is responsive and active, or stop the simulation otherwise."""
        if self.last_measurement_at:
            no_response_duration = datetime.now() - self.last_measurement_at
            no_response_timeout = timedelta(seconds=no_response_timeout)
            if no_response_duration > no_response_timeout:
                # no measurement emitted in `timeout` seconds, system is inresponsive
                return True

        simulation_duration = datetime.now() - self.started_at
        max_simulation_duration = timedelta(seconds=max_simulation_duration)
        if simulation_duration > max_simulation_duration:
            # simulation is running for longer than the limit, likely an error
            return True
        return False

    def simulation_result(self) -> SimulationResult:
        """Return a current simulation result."""
        exception = None
        if self.raised_exception:
            exception = "".join(traceback.format_exception(self.raised_exception))
        scenario_deviations = []
        for employee_id, deviations in self.deviations.items():
            scenario_deviations.append(
                DeviatedAgent(
                    id=employee_id,
                    name=self.employees[employee_id].name,
                    deviations=deviations,
                )
            )
        aspect_evaluation = self.aspect_evaluator.evaluation_result()
        return SimulationResult(
            finish_reason=self.finish_reason,
            aspect_evaluation=aspect_evaluation,
            raised_exception=exception,
            scenario_deviations=scenario_deviations,
        )

    async def save_current_result(self) -> None:
        """Save a current simulation result (snapshot) to a file."""
        if not self.started_at or not self.tag:
            raise ValueError("simulation has not started yet")
        timestamp = self.started_at.strftime("%Y-%m-%dT%H-%M-%S")
        filename = evaluation_filename_format.format(tag=self.tag, timestamp=timestamp)
        filename = Path(EVALUATION_DIR) / filename
        async with aiofiles.open(filename, "w") as file:
            await file.write(self.simulation_result().model_dump_json(indent=4))

    async def post_simulation_timeout(self, timeout: int = 30) -> None:
        """Kill tasks that not do want to stop themselves after the simulation completed."""
        started_at = datetime.now()
        while datetime.now() - started_at < timedelta(seconds=timeout):
            await asyncio.sleep(1)
        for task in self.tasks_to_cancel:
            if not task.done():
                task.cancel()
        logger.info("Killing tasks running after simulation completion")

    def start_simulation(self, tag: str, completion_criteria: BehavioralAspect) -> None:
        """Initialize simulation parameters, must be called on the start of a simulation."""
        self.tag = tag
        self.started_at = datetime.now()
        self.completion_criteria = completion_criteria

        self.start_monitor()

    async def stop_simulation(self, reason: FinishSimulationReason) -> None:
        """Stop employee actors and save current simulation result."""
        if self.finish_reason:
            # a repeated call to stop simulation
            exit(1)
        self.finish_reason = reason
        await self.save_current_result()
        for employee in self.employees.values():
            await employee.put_message(KILL_EMPLOYEE_MESSAGE)
        self.stop_monitor()
        if self.finish_reason == FinishSimulationReason.TIMEOUT:
            exit(1)
        asyncio.create_task(self.post_simulation_timeout())

    async def handle_measurement(
        self, measurement: Measurement, result: MeasurementResult
    ) -> None:
        """A callback executed on each new measurement."""
        self.last_measurement_at = datetime.now()
        detected = await self.aspect_evaluator.evaluate_measurement(measurement, result)
        if detected:
            await self.save_current_result()
        for aspect, _ in detected:
            if aspect == self.completion_criteria:
                await self.stop_simulation(reason=FinishSimulationReason.SUCCESS)

    async def handle_employee_event(self, event: EmployeeActorEvent) -> None:
        """A callback executed on each new event from employee actors."""
        if event.type == "completed":
            pass
        else:
            self.deviations[event.employee_id].append(event.deviation)

    async def run_simulation(
        self,
        tag: str,
        result_schema: type[BaseMeasurementResult],
        initiating_employees: list[EmployeeActor],
        completion_criteria: BehavioralAspect,
    ) -> None:
        """Spin up virtual employees and wait until simulation completes. Must
        be called only once during a test because the simulation is stateful. The
        `initiator` employee sends the first message and the simulation starts."""
        if not initiating_employees:
            raise ValueError("there must be at least one initiating employee, found: 0")

        self.start_simulation(tag=tag, completion_criteria=completion_criteria)

        await self.save_current_result()

        # initiate employee actors
        for initiator in initiating_employees:
            await self.employees[initiator.id].put_message(INITIATE_SIMULATION_MESSAGE)

        try:
            async with measurement_session.session(
                tag, result_schema
            ) as current_session:
                # analyze each new measurement in the measurement session
                current_session.add_update_callback(self.handle_measurement)

                for agent_id in self.employees.keys():
                    await current_session.add_agent(agent_id)

                # execute employees until the simulation is interrupted or completed
                async with asyncio.TaskGroup() as tg:
                    for employee in self.employees.values():
                        task = tg.create_task(employee.run())
                        self.tasks_to_cancel.append(task)

                # stop agents and wait until they complete executing their current turns
                async with asyncio.TaskGroup() as tg:
                    for agent_id in self.employees.keys():
                        task = tg.create_task(self.agent_pool.kill(agent_id, wait=True))
                        self.tasks_to_cancel.append(task)
        except Exception as exc:
            self.raised_exception = exc
            await self.stop_simulation(reason=FinishSimulationReason.ERROR)

    def print_finish_reason(self) -> None:
        console = Console(highlight=True)
        if not self.finish_reason:
            console.print(
                Markdown("# Simulation has not completed yet\n"),
                style=Style(color="blue", bold=True),
            )
        else:
            match self.finish_reason:
                case FinishSimulationReason.SUCCESS:
                    console.print(
                        Markdown("# Simulation completed successfully\n"),
                        style=Style(color="green", bold=True),
                    )
                case FinishSimulationReason.IDLE:
                    console.print(
                        Markdown("# Simulation failed because of being idle\n"),
                        style=Style(color="red", bold=True),
                    )
                case FinishSimulationReason.ERROR:
                    console.print(
                        Markdown("# Simulation failed with an exception\n"),
                        style=Style(color="red", bold=True),
                    )
                case FinishSimulationReason.DEVIATION:
                    console.print(
                        Markdown("# Simulation failed because of deviations\n"),
                        style=Style(color="red", bold=True),
                    )
                case _:
                    raise ValueError("invalid finish reason")
