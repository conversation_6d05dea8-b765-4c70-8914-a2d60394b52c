import asyncio
import logging
from typing import Annotated, Any, Callable, Literal, TypedDict
from uuid import UUID

from langchain.chat_models import init_chat_model
from langchain_core.messages import AnyMessage, HumanMessage, SystemMessage, ToolMessage
from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
)
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolCallId, tool
from langchain_core.tracers import LangChainTracer
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.graph.message import RemoveMessage
from langgraph.prebuilt import ToolNode
from langgraph.types import Command
from langsmith import Client
from pydantic import BaseModel, Field

from information_flows.core.decorators import make_registry, node
from information_flows.core.executor import AgentDiedMessage
from information_flows.core.logger import agent_logger
from information_flows.core.pool import AgentPool
from information_flows.core.types import Message, MessageWithPriority
from information_flows.core.utils import ActivityRecorder, execute_async
from information_flows.settings import settings
from testing.simulation.scenario import ActorScript

KILL_EMPLOYEE_MESSAGE = object()

INITIATE_SIMULATION_MESSAGE = object()

PROMPT_TEMPLATE_PATH = "./testing/simulation/employee_template.jinja"

with open(PROMPT_TEMPLATE_PATH) as file:
    prompt_template = file.read().strip()

memory_blueprint = ChatPromptTemplate.from_messages(
    [
        SystemMessagePromptTemplate.from_template(
            prompt_template,
            template_format="jinja2",
        ),
        MessagesPlaceholder("messages"),
    ]
)

llm = init_chat_model(
    "openai:gpt-4.1",
    api_key=settings.OPENAI_API_KEY,
    temperature=0,
)


class SimulationError(Exception):
    """Base exception class for simulation errors."""


employee_tools, employee_tool = make_registry()


@employee_tool
@tool
def complete_scenario() -> None:
    """Signal that you completed the entire scenario."""
    return None


class Deviation(BaseModel):
    """Explain how the agent deviated from its expected scenario behavior.

    Explain your reasoning and estimate significance of deviation, where:
    - Significance of low means your assistant deviated slightly but visibly, which may cause problems in the future.
    - Significance of medium means your assistant performed a wrong action, misinterpreted you or obtained wrong results.
    - Significance of high means the agent deviated from the main essence of the scenario.
    """

    reasoning: str
    significance: Literal["low", "medium", "high"]


@employee_tool
@tool
def employee_deviated(
    deviation: Deviation, tool_call_id: Annotated[str, InjectedToolCallId]
) -> None:
    """Signal that you consider your assistant deviated from the scenario."""
    return Command(
        update={
            "deviation": deviation,
            "messages": [
                ToolMessage("Your concern was recorded.", tool_call_id=tool_call_id)
            ],
        }
    )


class AgentConfig(TypedDict):
    thread_id: UUID
    name: str
    script: ActorScript


class AgentState(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    completed: bool
    deviation: Deviation


class PartialAgentState(AgentState, total=False):
    pass


def employee_setup(state: AgentState) -> PartialAgentState:
    return {"deviation": None}


@node()
async def simulation_step(state: AgentState, config: AgentConfig) -> PartialAgentState:
    chain = memory_blueprint | llm.bind_tools(employee_tools)
    message = await chain.ainvoke(
        {
            "script": config["script"],
            "messages": state["messages"],
        }
    )
    return {"messages": [message]}


def should_continue(state: AgentState):
    last_message = state["messages"][-1]
    if last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            if tool_call["name"] == "complete_scenario":
                return "simulation_completed"
        return "tools"
    return END


def simulation_completed(state: AgentState) -> PartialAgentState:
    last_message = state["messages"][-1]
    return {"messages": [RemoveMessage(id=last_message.id)], "completed": True}


def create_employee_graph(checkpointer: BaseCheckpointSaver):
    graph_builder = StateGraph(AgentState)
    graph_builder.add_node("employee_setup", employee_setup)
    graph_builder.add_node("simulation_step", simulation_step)
    graph_builder.add_node("tools", ToolNode(employee_tools))
    graph_builder.add_node("simulation_completed", simulation_completed)
    graph_builder.add_edge(START, "employee_setup")
    graph_builder.add_edge("employee_setup", "simulation_step")
    graph_builder.add_conditional_edges(
        "simulation_step", should_continue, ["simulation_completed", "tools", END]
    )
    graph_builder.add_edge("simulation_completed", END)
    graph_builder.add_edge("tools", "simulation_step")
    graph = graph_builder.compile(checkpointer)
    return graph


class BaseEmployeeEvent(BaseModel):
    employee_id: UUID


class CompletedEvent(BaseEmployeeEvent):
    type: Literal["completed"] = "completed"


class DeviatedEvent(BaseEmployeeEvent):
    type: Literal["deviated"] = "deviated"
    deviation: Deviation


type EmployeeActorEvent = Annotated[
    CompletedEvent | DeviatedEvent, Field(discriminator="type")
]


class EmployeeActor:
    state: Literal["idle", "processing"]

    def __init__(
        self,
        id: UUID,
        name: str,
        script: ActorScript,
        *,
        pool: AgentPool,
        langsmith_trace: bool = False,
    ):
        self.id = id
        self.name = name
        self.script = script
        self.pool = pool
        self.langsmith_trace = langsmith_trace
        self.state = "idle"
        self.completed = False
        self.event_callbacks = []
        self.in_queue = asyncio.PriorityQueue[MessageWithPriority]()
        self.pool.add_message_callback(self.id, self.put_message)
        self.activity_recorder = ActivityRecorder()
        self.memory = InMemorySaver()

    def add_event_callback(self, callback: Callable[[], None]) -> None:
        self.event_callbacks.append(callback)

    def remove_event_callback(self, callback: Callable[[], None]) -> None:
        self.event_callbacks.remove(callback)

    def notify_subscribers(self, event: EmployeeActorEvent) -> None:
        """Execute all callbacks subscribed on completion of the scenario."""
        for callback in self.event_callbacks:
            asyncio.create_task(execute_async(callback, event))

    def get_graph_config(self) -> RunnableConfig:
        callbacks = []
        if self.langsmith_trace and settings.LANGSMITH_API_KEY is not None:
            tracer = LangChainTracer(
                project_name="Information Flows",
                client=Client(api_key=settings.LANGSMITH_API_KEY),
            )
            callbacks.append(tracer)
        return {
            "configurable": {
                "thread_id": self.id,
                "name": self.name,
                "script": self.script,
            },
            "callbacks": callbacks,
        }

    def get_state(self) -> AgentState:
        config = self.get_graph_config()
        graph = create_employee_graph(self.memory)
        return graph.get_state(config).values

    async def put_message(self, message: Message) -> None:
        """Receive a new message and put it into the input queue."""
        if message is KILL_EMPLOYEE_MESSAGE:
            message_with_priority = MessageWithPriority(0, message)
        elif isinstance(message, AgentDiedMessage):
            message_with_priority = MessageWithPriority(1, KILL_EMPLOYEE_MESSAGE)
        elif message is INITIATE_SIMULATION_MESSAGE:
            message_with_priority = MessageWithPriority(2, message)
        else:
            message_with_priority = MessageWithPriority(3, message)
        await self.in_queue.put(message_with_priority)

    async def send_message(self, content: Any) -> None:
        """Send a message to an assistant via the agent pool."""
        await self.pool.put_message(
            Message(
                content=content,
                receiver_id=self.id,
                sender_id=self.id,
                sender_role="user",
            )
        )

    async def run(self) -> None:
        """Start an employee actor executing its scenario."""
        try:
            while True:
                message_with_priority = await self.in_queue.get()
                if message_with_priority.message is KILL_EMPLOYEE_MESSAGE:
                    self.in_queue.task_done()
                    break
                self.state = "processing"
                self.activity_recorder.record_start()
                await self.process_message(message_with_priority.message)
                self.state = "idle"
                self.activity_recorder.record_finish()
                self.in_queue.task_done()
        except Exception:
            logging.error("Internal employee error", exc_info=True)
            self.in_queue.task_done()
            raise

    async def process_message(self, message: Message) -> None:
        """Respond to an incoming message from the assistant or complete the simulation."""
        if message is INITIATE_SIMULATION_MESSAGE:
            message = SystemMessage("INITIATE SCENARIO SIMULATION")
        else:
            agent_logger.employee_received(self.id, message.content, self.name)
            message = HumanMessage(f"[Message from your assistant] {message.content}")

        graph = create_employee_graph(self.memory).with_config(self.get_graph_config())

        state: AgentState = await graph.ainvoke({"messages": message})

        if state.get("completed", False):
            # stop the employee agent loop
            self.completed = True
            self.notify_subscribers(CompletedEvent(employee_id=self.id))
            await self.put_message(KILL_EMPLOYEE_MESSAGE)
        else:
            # notify about deviation and continue running
            if deviation := state.get("deviation", None):
                self.notify_subscribers(
                    DeviatedEvent(employee_id=self.id, deviation=deviation)
                )
            await self.send_message(state["messages"][-1].content)
