"""
Shared database cleanup utilities for test suites.

This module provides functionality to track and clean up test data
created during test runs, ensuring no residual data is left in the database.
"""

import os
from typing import Set
from uuid import UUID

from information_flows.settings import settings


class TestDataTracker:
    """Tracks test data identifiers for cleanup."""

    def __init__(self):
        self.integration_ids: Set[str] = set()
        self.employee_ids: Set[UUID] = set()

    def add_integration_id(self, integration_id: str):
        """Track an integration ID for cleanup."""
        self.integration_ids.add(integration_id)

    def add_employee_id(self, employee_id: UUID):
        """Track an employee ID for cleanup."""
        self.employee_ids.add(employee_id)

    def clear(self):
        """Clear all tracked data."""
        self.integration_ids.clear()
        self.employee_ids.clear()


async def find_test_data(connection_pool) -> TestDataTracker:
    """
    Find existing test data in the database.

    SAFETY: Only operates when ALLOW_TEST_CLEANUP environment variable is set.
    This prevents accidental cleanup in production environments.

    Args:
        connection_pool: Database connection pool to use

    Returns:
        TestDataTracker with found test data
    """
    # Safety check - allow cleanup in test/development environments or when explicitly enabled
    allow_cleanup = settings.ALLOW_TEST_CLEANUP or settings.ENVIRONMENT in [
        "development",
        "test",
        "testing",
    ]

    if not allow_cleanup:
        # Return empty tracker if not allowed
        return TestDataTracker()

    tracker = TestDataTracker()

    async with connection_pool.connection() as conn:
        # Find test integrations by test-specific prefixes
        # Only matches IDs that clearly indicate test data
        result = await conn.execute(
            """
            SELECT external_employee_id, local_employee_id 
            FROM integration 
            WHERE external_employee_id LIKE 'test_%'
               OR external_employee_id LIKE 'TEST_%'
               OR external_employee_id LIKE '%_test'
               OR external_employee_id LIKE '%_TEST'
               OR external_employee_id LIKE 'sync_worker%'
               OR external_employee_id LIKE 'par_worker%'
               OR external_employee_id LIKE 'Sync Worker%'
               OR external_employee_id LIKE 'Parallel Worker%'
        """
        )

        for row in await result.fetchall():
            tracker.add_integration_id(row[0])
            tracker.add_employee_id(row[1])

    return tracker


async def cleanup_test_data(connection_pool, tracker: TestDataTracker):
    """
    Clean up test data from the database.

    SAFETY: Only operates when ALLOW_TEST_CLEANUP environment variable is set.

    Args:
        connection_pool: Database connection pool to use
        tracker: TestDataTracker instance with IDs to clean up
    """
    # Safety check - allow cleanup in test/development environments or when explicitly enabled
    allow_cleanup = os.getenv(
        "ALLOW_TEST_CLEANUP", ""
    ).lower() == "true" or settings.ENVIRONMENT in ["development", "test", "testing"]

    if not allow_cleanup:
        print(
            f"WARNING: Test cleanup skipped - ENVIRONMENT is '{settings.ENVIRONMENT}' and ALLOW_TEST_CLEANUP not set to 'true'"
        )
        return

    if not tracker.integration_ids and not tracker.employee_ids:
        return

    async with connection_pool.connection() as conn:
        # Get employee IDs from integration IDs
        employee_ids = list(tracker.employee_ids)

        if tracker.integration_ids:
            result = await conn.execute(
                """
                SELECT local_employee_id FROM integration 
                WHERE external_employee_id = ANY(%s)
            """,
                (list(tracker.integration_ids),),
            )

            employee_ids.extend(row[0] for row in await result.fetchall())

        if employee_ids:
            # Delete in correct order to respect foreign keys
            # First, delete checkpoint-related data
            for employee_id in employee_ids:
                # Get thread_id for this employee
                result = await conn.execute(
                    "SELECT thread_id FROM employee WHERE employee_id = %s",
                    (employee_id,),
                )
                row = await result.fetchone()
                if row and row[0]:
                    thread_id = row[0]
                    # Delete checkpoint data
                    for table in [
                        "checkpoint_writes",
                        "checkpoint_blobs",
                        "checkpoints",
                    ]:
                        await conn.execute(
                            f"DELETE FROM {table} WHERE thread_id = %s", (thread_id,)
                        )

            # Delete chat-related data first (respecting foreign keys)
            # Get all chat IDs for these employees
            result = await conn.execute(
                "SELECT id FROM chat WHERE employee_id = ANY(%s)", (employee_ids,)
            )
            chat_ids = [row[0] for row in await result.fetchall()]
            
            if chat_ids:
                # Delete chat_message associations
                await conn.execute(
                    "DELETE FROM chat_message WHERE chat_id = ANY(%s)", (chat_ids,)
                )
                # Delete tasks
                await conn.execute(
                    "DELETE FROM task WHERE chat_id = ANY(%s)", (chat_ids,)
                )
                # Delete chats
                await conn.execute(
                    "DELETE FROM chat WHERE id = ANY(%s)", (chat_ids,)
                )
            
            # Then delete other related data
            await conn.execute(
                "DELETE FROM event WHERE agent_id = ANY(%s)", (employee_ids,)
            )
            
            # Get message IDs before deleting them (for chat_message cleanup)
            result = await conn.execute(
                """
                SELECT message_id FROM message 
                WHERE sender_id = ANY(%s) OR receiver_id = ANY(%s)
                """,
                (employee_ids, employee_ids),
            )
            message_ids = [row[0] for row in await result.fetchall()]
            
            if message_ids:
                # Delete any remaining chat_message associations
                await conn.execute(
                    "DELETE FROM chat_message WHERE message_id = ANY(%s)", (message_ids,)
                )
            
            await conn.execute(
                """
                DELETE FROM message 
                WHERE sender_id = ANY(%s) OR receiver_id = ANY(%s)
            """,
                (employee_ids, employee_ids),
            )
            await conn.execute(
                """
                DELETE FROM relation 
                WHERE from_employee_id = ANY(%s) OR to_employee_id = ANY(%s)
            """,
                (employee_ids, employee_ids),
            )
            await conn.execute(
                "DELETE FROM integration WHERE local_employee_id = ANY(%s)",
                (employee_ids,),
            )
            await conn.execute(
                "DELETE FROM employee WHERE employee_id = ANY(%s)", (employee_ids,)
            )
