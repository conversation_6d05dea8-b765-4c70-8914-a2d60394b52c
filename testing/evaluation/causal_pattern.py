from dataclasses import dataclass
from typing import Callable, Optional, Self
from uuid import UUID

from information_flows.agents.core_agent.measurement import Measurement, MeasurementType
from testing.evaluation.causal_graph import CausalNode


@dataclass
class CausalPatternMatch:
    position: int
    nodes: list[CausalNode]


class CausalPatternStep:
    def __init__(
        self,
        measurement_type: MeasurementType,
        agent_id: Optional[UUID] = None,
        value_key: Optional[Callable[[Measurement], bool]] = None,
    ):
        self.measurement = measurement_type
        self.agent = agent_id
        self.value_key = value_key

    def match(self, node: CausalNode) -> bool:
        if self.measurement != node.measurement.type:
            return False
        if self.agent and node.agent.agent_id != self.agent:
            return False
        if self.value_key and not self.value_key(node.measurement):
            return False
        return True


class CausalPattern:
    steps: list[CausalPatternStep | None]

    def __init__(self):
        self.steps = []
        self.current_agent = None

    def agent(self, agent_id: UUID | None) -> Self:
        """Specify an agent for following nodes or clean current agent if None."""
        if self.steps and self.steps[-1] is None:
            raise ValueError("`agent` cannot be specified after `any`")
        self.current_agent = agent_id
        return self

    def measurement(self, measurement_type: MeasurementType) -> Self:
        """Specify a type of a measurement."""
        self.steps.append(CausalPatternStep(measurement_type, self.current_agent))
        return self

    def value(self, key: Callable[[Measurement], bool]) -> Self:
        """Specify a measurement validator key function. If the key returns
        True for the measurement, that the pattern matches, otherwise not."""
        if not self.steps or self.steps[-1] is None:
            raise ValueError("a value check must follow a measurement")
        self.steps[-1].value_key = key
        return self

    def any(self) -> Self:
        """A wildcard for arbitrary number of arbitrary measurements."""
        if not self.steps:
            raise ValueError("there must be at least one measurement in a pattern")
        if self.steps[-1] is not None:
            self.steps.append(None)
        return self

    def find(self, nodes: list[CausalNode]) -> Optional[CausalPatternMatch]:
        """Try to find a matching subsequence of nodes in a list of nodes."""
        if not self.steps:
            raise ValueError("there must be at least one measurement in a pattern")
        n_real_steps = len([s for s in self.steps if s])
        for node in range(len(nodes) - n_real_steps + 1):
            matched = self.match(nodes[node:])
            if matched:
                return CausalPatternMatch(position=node, nodes=matched)
        return None

    def match(self, nodes: list[CausalNode]) -> list[CausalNode]:
        """Try match a list of nodes to a pattern, returning the matching nodes."""
        steps = self.steps[::-1]
        matched = []
        i = 0
        for step in range(len(steps)):
            current_step = steps[step]
            if current_step:
                # current step is not `any`
                current_node = nodes[i]
                if not current_step.match(current_node):
                    break
                i += 1
                matched.append(current_node)
            else:
                # current step is `any`, so try to find a node matching the next step
                if step + 1 == len(steps):
                    raise ValueError("`any` must be followed by a measurement")
                next_step = steps[step + 1]
                found_next_node = False
                matched_any = []
                while i < len(nodes):
                    current_node = nodes[i]
                    if next_step.match(current_node):
                        found_next_node = True
                        break
                    i += 1
                    matched_any.append(current_node)
                if not found_next_node:
                    break
                matched.extend(matched_any)
        else:
            return matched[::-1]
        return []


if __name__ == "__main__":
    from information_flows.agents.core_agent.measurement import MeasurementResult
    from testing.evaluation.causal_graph import CausalMultiThreadGraph

    measurement_result = MeasurementResult.load(tag="policy_qa")
    causal_graph = CausalMultiThreadGraph.from_measurement(measurement_result)
    nodes = causal_graph.linearize()

    for node in nodes[:10]:
        print(node.measurement.type)

    pattern = (
        CausalPattern()
        .agent(measurement_result.agent_of("krk").agent_id)
        .measurement("choose_action")
        .value(key=lambda m: m.actions[0].name == "get_company_policy")
        .any()
        .measurement("perceive_result")
    )

    if matched := pattern.match(nodes[2:5][::-1]):
        print(">>> matched")
        for node in matched:
            print(node.measurement.type)
    else:
        print("! Does not match !")
    # if matched := pattern.find(nodes[2:]):
    #     print(">>> matched")
    #     for node in matched.nodes:
    #         print(node.measurement.type)
    # else:
    #     print("! Does not match !")
