import asyncio
import logging
from collections import defaultdict
from typing import Literal, Optional, TypedDict, override
from uuid import UUID, uuid4

from langchain.chat_models import init_chat_model
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_core.tracers import LangChainTracer
from langgraph.graph import StateGraph
from langsmith import Client
from pydantic import BaseModel, Field, field_validator

from information_flows.agents.core_agent.measurement import (
    Measurement,
    MeasurementResult,
    MeasurementType,
)
from information_flows.agents.core_agent.utils import compress
from information_flows.settings import settings
from testing.evaluation.causal_graph import (
    CausalMultiThreadGraph,
    CausalNode,
    CausalSingleThreadGraph,
)
from testing.evaluation.causal_pattern import CausalPattern

logger = logging.getLogger("aspect_evaluator")
logger.setLevel(logging.INFO)

DETECTION_CONFIDENCE_THRESHOLD = 4

PROMPT_TEMPLATE_PATH = "./testing/evaluation/detection_template.jinja"

llm = init_chat_model(
    "openai:gpt-4.1-mini",
    api_key=settings.OPENAI_API_KEY,
    temperature=0,
)

with open(PROMPT_TEMPLATE_PATH) as file:
    prompt_template = file.read().strip()

detection_prompt = ChatPromptTemplate.from_messages(
    [
        SystemMessagePromptTemplate.from_template(
            prompt_template,
            template_format="jinja2",
        ),
    ]
)


class DetectBehavior(BaseModel):
    """Determine whether the suspected behavior is the expected behavior.

    Explain your reasoning and determine similarity as a score from 1 to 5, where:
    - Score of 1 means the suspected behavior barely looks like the expected one.
    - Score of 2 means there are some common aspects but predominantly they are diffetent.
    - Score of 3 means the expected behavior is reflected in the suspected for at least 50%.
    - Score of 4 means the suspected behavior almost completely overlaps with the expected one.
    - Score of 5 means the agent behaves as expected, even if there are some irrelevant aspects.
    """

    reasoning: str = Field(
        description="Explain your reasoning and the resulting conclusion."
    )
    similarity: Literal["1", "2", "3", "4", "5"] = Field(
        description="How confident you are that this is the behavior of interest indeed?"
    )


class DetectionState(TypedDict):
    suspected_behavior: list[str]
    expected_behavior: "BaseBehavioralAspect"
    result: DetectBehavior


async def analyze_behavior(state: DetectionState):
    chain = detection_prompt | llm.with_structured_output(
        DetectBehavior, method="json_schema"
    )

    result: DetectBehavior = await chain.ainvoke(
        {
            "suspected_behavior": state["suspected_behavior"],
            "expected_behavior": state["expected_behavior"].behavior,
            "expected_behavior_reason": state["expected_behavior"].reason,
            "expected_behavior_effect": state["expected_behavior"].effect,
        },
    )

    return {"result": result}


def create_evaluation_graph():
    graph_builder = StateGraph(DetectionState)
    graph_builder.add_node("analyze_behavior", analyze_behavior)
    graph_builder.set_entry_point("analyze_behavior")
    graph_builder.set_finish_point("analyze_behavior")
    graph = graph_builder.compile()
    return graph


class AspectOccurrence(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    aspect_id: UUID
    explanation: str
    similarity: int
    location: list[Measurement]


DEFAULT_MEASUREMENT_TYPES: list[MeasurementType] = [
    "perceive_message",
    "focus_attention",
    "choose_action",
    "self_explain",
    "perceive_result",
    "self_reflect",
    "send_message",
]


class BaseBehavioralAspect(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    agent_id: UUID
    """A target agent behavior of which to analyze."""
    behavior: str
    """Short description of the behavior to expect."""
    reason: str
    """Explaining why this behavior should be performed by an agent."""
    effect: str
    """What is the expected result of this behavior, e.g. a result of a tool call."""

    @field_validator("behavior", "reason", "effect", mode="before")
    @classmethod
    def preprocess_prompt_field(cls, value: str) -> str:
        return compress(value)

    def heuristic_trigger(
        self, causal_graph: CausalSingleThreadGraph
    ) -> list[CausalNode]:
        """Analyze the causal graph and return a list of nodes suspected of the
        behavior. Must be implemented by subclasses, e.g. using CausalPattern."""
        raise NotImplementedError

    def filter_chronology(self, nodes: list[CausalNode]) -> list[CausalNode]:
        """Return a chronologically sorted list of relevant measurements, that
        is measurements of types included in `relevant_measurement_types`."""
        filtered_nodes = []
        for node in nodes:
            if node.measurement.type in set(DEFAULT_MEASUREMENT_TYPES):
                filtered_nodes.append(node)
        return filtered_nodes

    def get_graph_config(self) -> RunnableConfig:
        callbacks = []
        if settings.LANGSMITH_API_KEY:
            tracer = LangChainTracer(
                project_name="Information Flows",
                client=Client(api_key=settings.LANGSMITH_API_KEY),
            )
            callbacks.append(tracer)
        return {"callbacks": callbacks}

    async def semantic_trigger(
        self,
        suspected_nodes: list[CausalNode],
        causal_graph: CausalSingleThreadGraph,
    ) -> Optional[DetectBehavior]:
        """Evaluate LLM-based detection of the aspect agains suspected nodes."""
        graph = create_evaluation_graph().with_config(self.get_graph_config())

        # TODO: add radius of considered causal nodes
        # group measurements into groups of episodes by agents
        # trajectory_nodes = defaultdict(list)
        # for node in self.filter_chronology(causal_graph.linearize()[::-1]):
        #     trajectory_nodes[node.trajectory].append(node)
        # agent_chronology = []
        # for nodes in trajectory_nodes.values():
        #     employee_name = nodes[0].agent.external_employee_name
        #     formatted_nodes = [node.format() for node in nodes]
        #     agent_chronology.append((employee_name, formatted_nodes))

        suspected_behavior = [node.format() for node in suspected_nodes]

        state: DetectionState = await graph.ainvoke(
            {
                "suspected_behavior": suspected_behavior,
                "expected_behavior": self,
            }
        )

        similarity = int(state["result"].similarity)
        if similarity >= DETECTION_CONFIDENCE_THRESHOLD:
            return state["result"]
        return None

    async def detect(
        self, causal_graph: CausalSingleThreadGraph
    ) -> Optional[AspectOccurrence]:
        """Try to detect the aspect in the causal graph."""
        suspected_nodes = self.heuristic_trigger(causal_graph)
        if not suspected_nodes:
            return
        detection_result = await self.semantic_trigger(suspected_nodes, causal_graph)
        if not detection_result:
            return
        logger.info(f"Detect behavior: {self.behavior}")
        return AspectOccurrence(
            aspect_id=self.id,
            explanation=detection_result.reasoning,
            similarity=int(detection_result.similarity),
            location=[node.measurement for node in suspected_nodes],
        )


class EvaluatedAspect(BaseModel):
    aspect: "BehavioralAspect"
    occurrences: list[AspectOccurrence]


class AspectEvaluationResult(BaseModel):
    detected_aspects: list[EvaluatedAspect]
    missed_aspects: list["BehavioralAspect"]


class AspectEvaluator:
    expected_aspects: list[BaseBehavioralAspect]
    aspect_occurrences: dict[UUID, list[AspectOccurrence]]

    def __init__(self):
        self.expected_aspects = []
        self.aspect_occurrences = defaultdict(list)

    def expect[T: BaseBehavioralAspect](self, aspect: T) -> T:
        """Recorn an aspect of behavior to expect and try to detect."""
        self.expected_aspects.append(aspect)
        return aspect

    async def detect_aspect(
        self, aspect: BaseBehavioralAspect, causal_subgraph: CausalSingleThreadGraph
    ) -> Optional[tuple[BaseBehavioralAspect, AspectOccurrence]]:
        """Try to detect the aspect on the causal graph and return its occurrence."""
        occurrence = await aspect.detect(causal_subgraph)
        if occurrence:
            self.aspect_occurrences[aspect.id].append(occurrence)
            return aspect, occurrence
        return None

    async def evaluate_measurement(
        self,
        measurement: Measurement,
        result: MeasurementResult,
    ) -> list[tuple[BaseBehavioralAspect, AspectOccurrence]]:
        """Analyze the measurement to detect any expected aspects of behavior."""
        causal_graph = CausalMultiThreadGraph.from_measurement(result)
        target_node = causal_graph.measurement_to_node[measurement]
        causal_subgraph = causal_graph.causal_subgraph(target_node)

        tasks = []
        for aspect in self.expected_aspects:
            tasks.append(self.detect_aspect(aspect, causal_subgraph))
        detected = []
        for task in asyncio.as_completed(tasks):
            task_result = await task
            if task_result:
                detected.append(task_result)
        return detected

    def evaluation_result(self) -> AspectEvaluationResult:
        """Return current behavioral aspects evaluation result."""
        occurred_aspects = []
        missed_aspects = []
        for aspect in self.expected_aspects:
            if aspect.id in self.aspect_occurrences:
                occurred_aspects.append(
                    EvaluatedAspect(
                        aspect=aspect,
                        occurrences=self.aspect_occurrences[aspect.id],
                    )
                )
            else:
                missed_aspects.append(aspect)
        return AspectEvaluationResult(
            detected_aspects=occurred_aspects,
            missed_aspects=missed_aspects,
        )


class ToolCallAspect(BaseBehavioralAspect):
    tool_name: str

    @override
    def heuristic_trigger(
        self, causal_graph: CausalSingleThreadGraph
    ) -> list[CausalNode]:
        return (
            CausalPattern()
            .agent(self.agent_id)
            .measurement("choose_action")
            .value(key=lambda m: m.actions[0].name == self.tool_name)
            .measurement("perceive_result")
            .match(causal_graph.linearize())
        )


class CommunicateWithChiefAspect(BaseBehavioralAspect):
    @override
    def heuristic_trigger(
        self, causal_graph: CausalSingleThreadGraph
    ) -> list[CausalNode]:
        return (
            CausalPattern()
            .agent(self.agent_id)
            .measurement("choose_action")
            .value(key=lambda m: m.actions[0].name == "communicate_with_chief")
            .any()
            .measurement("send_message")
            .value(key=lambda m: m.message.receiver_id == self.agent_id)
            .match(causal_graph.linearize())
        )


class CommunicateWithAssistantAspect(BaseBehavioralAspect):
    receiver_id: UUID

    @override
    def heuristic_trigger(
        self, causal_graph: CausalSingleThreadGraph
    ) -> list[CausalNode]:
        return (
            CausalPattern()
            .agent(self.agent_id)
            .measurement("choose_action")
            .value(key=lambda m: m.actions[0].name == "communicate_with_assistant")
            .any()
            .measurement("send_message")
            .value(key=lambda m: m.message.receiver_id == self.receiver_id)
            .match(causal_graph.linearize())
        )


type BehavioralAspect = ToolCallAspect | CommunicateWithChiefAspect | CommunicateWithAssistantAspect
