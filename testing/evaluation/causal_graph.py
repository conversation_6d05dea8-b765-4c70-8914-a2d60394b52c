import functools
import itertools
from collections import defaultdict
from uuid import UUID

import networkx as nx

from information_flows.agents.core_agent.measurement import (
    AgentTrajectory,
    MeasuredAgent,
    Measurement,
    MeasurementResult,
)


class CausalNode:
    def __init__(
        self,
        measurement: Measurement,
        trajectory: AgentTrajectory,
        agent: MeasuredAgent,
    ):
        self.measurement = measurement
        self.trajectory = trajectory
        self.agent = agent

    def __hash__(self):
        return hash(self.measurement.id)

    def __eq__(self, other: "CausalNode"):
        return self.measurement == other.measurement

    def __repr__(self):
        cls = type(self)
        return f"{cls.__name__}({self.measurement!r})"

    def __str__(self):
        cls = type(self)
        return f"{cls.__name__}({self.measurement.type})"

    def format(self) -> str:
        match self.measurement.type:
            case "perceive_message":
                message = self.measurement.message
                formatted = f"Receive a message:\n{message.content}"
            case "focus_attention":
                analysis = self.measurement.analysis
                formatted = f"Analyze the message:\n{analysis}"
            case "choose_action":
                actions = self.measurement.actions
                formatted = f"Perform the next action:\n{actions}"
            case "perceive_result":
                result = self.measurement.result
                formatted = f"Observe result of the action:\n{result}"
            case "send_message":
                message = self.measurement.message
                formatted = f"Send a message:\n{message.content}"
            case _:
                raise ValueError("unsupported measurement type")
        return formatted


class CausalGraph:
    """A directed asyclic graph (DAG) of causes with a multiple root cause nodes."""

    trajectory_index: dict[AgentTrajectory, int]
    """An index indicating an order (sequence) of trajectories."""
    agent_nodes: dict[MeasuredAgent, list[CausalNode]]
    """A mapping of nodes to corresponding agents in a topological order."""
    measurement_to_node: dict[Measurement, CausalNode]

    def __init__(self, graph: nx.DiGraph):
        self.graph = graph

        self.trajectory_index = {}
        self.agent_nodes = defaultdict(list)
        self.measurement_to_node = {}

        for node in nx.topological_sort(self.graph):
            if node.trajectory not in self.trajectory_index:
                self.trajectory_index[node.trajectory] = len(
                    self.trajectory_index.keys()
                )
            self.agent_nodes[node.agent].append(node)
            self.measurement_to_node[node.measurement] = node

    def causal_subgraph(self, target: CausalNode) -> "CausalSingleThreadGraph":
        """Return an DAG of causes of the `target` by traversing the graph backwards
        starting from it. The `target` will be at the top with children as causes."""
        graph = nx.DiGraph()
        edges = nx.edge_bfs(self.graph.reverse(), target)
        graph.add_edges_from(edges)
        graph.add_nodes_from([target])
        return CausalSingleThreadGraph(graph, target)

    def effect_subgraph(self, target: CausalNode) -> "CausalSingleThreadGraph":
        """Return a DAG of nodes affected by the `target` by traversing the graph
        forwards starting from it. The `target` will be a single root at the top."""
        graph = nx.DiGraph()
        edges = nx.edge_bfs(self.graph, target)
        graph.add_edges_from(edges)
        graph.add_nodes_from([target])
        return CausalSingleThreadGraph(graph, target)

    def linearize(self) -> list[CausalNode]:
        """Return a list of nodes of trajectories in causal order of trajectories."""
        trajectory_nodes = defaultdict(list)
        for node in nx.topological_sort(self.graph):
            trajectory_nodes[node.trajectory].append(node)
        return list(itertools.chain.from_iterable(trajectory_nodes.values()))


class CausalMultiThreadGraph(CausalGraph):
    """A causal graph that can be constructed from all trajectories of all agents
    in the system, even from independent threads of events, where multiple agents
    do not interact with each other, thus tracing independent causal graphs."""

    def __init__(self, graph: nx.DiGraph, roots: list[CausalNode]):
        super().__init__(graph)
        self.roots = roots

    @classmethod
    def from_measurement(cls, result: MeasurementResult) -> "CausalMultiThreadGraph":
        """Create a causal graph of events by traversing trajectories of agents in the measurement."""
        input_message_endpoints = {}
        output_message_endpoints = {}
        end_turn_endpoints = {}

        # add edges between measurements inside each trajectory of an agent
        nodes = []
        edges = []
        for trajectory in sorted(result.trajectories, key=lambda t: t.started_at):
            agent = result.select_agent(trajectory.agent_id)
            previous = None
            if agent in end_turn_endpoints:
                # link a previous trajectory of the same agent to the current one
                previous = end_turn_endpoints[agent]
            for i, measurement in enumerate(trajectory.measurements):
                current = CausalNode(measurement, trajectory, agent)
                nodes.append(current)
                if previous:
                    edges.append((previous, current))
                if measurement.type == "perceive_message":
                    input_message_endpoints[measurement.message.id] = current
                elif measurement.type == "send_message":
                    output_message_endpoints[measurement.message.id] = current
                elif i == len(trajectory.measurements) - 1:
                    end_turn_endpoints[agent] = current
                previous = current

        # add edges of type "communicate_with_assistant" -> "perceive_message"
        for message_id, output_message in output_message_endpoints.items():
            if message_id in input_message_endpoints:
                # if the message is between agents
                input_message = input_message_endpoints[message_id]
                edges.append((output_message, input_message))

        graph = nx.DiGraph()
        graph.add_nodes_from(nodes)
        graph.add_edges_from(edges)

        # find root cause nodes
        root_nodes = []
        for node, in_degree in graph.in_degree():
            if in_degree == 0:
                root_nodes.append(node)
        if len(root_nodes) == 0:
            raise ValueError(
                f"there must be at least one root cause, found: {len(root_nodes)}"
            )

        return cls(graph=graph, roots=root_nodes)

    @functools.cache
    def node_coordinates(
        self,
        *,
        agent_order: tuple[UUID] | None = None,
        y_downwards: bool = True,
    ) -> dict[CausalNode, tuple[int, int]]:
        """Return a mapping from graph nodes to pairs (x, y) of coordinates on the plane.

        x-coordinate of a node is the index of an agent in a default list of agents or
        in `agent_order` list, if specified. E.g. x-coordinate of all nodes of agent 1
        in a list [agent_0, agent_1, agent_3] is 1.

        y-coordinate of a node is a depth at which this node is located if traversing
        a graph starting from the root node. When `y_downwards` is True then larger
        values mean lower located nodes, otherwise the larger the y the higher a node.
        """
        depths = {}
        max_depth = -1
        max_depth_root = None
        for root in self.roots:
            depths[root] = nx.shortest_path_length(self.graph, root)
            if max(depths[root].values()) > max_depth:
                max_depth_root = root
        depths = depths[max_depth_root]

        coordinates = {}
        agents = list(self.agent_nodes.keys())
        if agent_order:
            try:
                agents.sort(key=lambda a: agent_order.index(a))
            except IndexError:
                raise ValueError(f"not all agents are listed in `agent_order`")
        max_depth = 0
        for node, depth in depths.items():
            coordinates[node] = (agents.index(node.agent), depth)
            if depth > max_depth:
                max_depth = depth
        # reverse y-axis coordinate: larger value is higher
        if not y_downwards:
            for node, (x, y) in coordinates.items():
                coordinates[node] = (x, max_depth - y)
        return coordinates

    def draw(
        self,
        *,
        agent_order: list[UUID] | None = None,
        nodelist: list[CausalNode] | None = None,
        color: str = "skyblue",
        invert: bool = False,
    ) -> None:
        nodelist = nodelist or list(self.graph.nodes)
        pos = self.node_coordinates(agent_order=agent_order, y_downwards=invert)
        nx.draw_networkx_nodes(self.graph, pos=pos, nodelist=nodelist)
        nx.draw_networkx_edges(self.graph, pos=pos)
        nx.draw_networkx_labels(
            self.graph,
            pos=pos,
            labels={node: node.measurement.type for node in nodelist},
            bbox=dict(facecolor=color, edgecolor="black", boxstyle="round,pad=0.2"),
        )


class CausalSingleThreadGraph(CausalGraph):
    """A causal graph that can be produced by sampling a multi-threaded causal
    graph by choosing a target root cause node. This graph represents either a
    trajectory of a system starting from a single agent and its root cause node,
    or a cause or effect DAGs starting from an arbitrary node in the underlying
    causal multi-threaded or single-threaded graph."""

    def __init__(self, graph: nx.DiGraph, root: CausalNode):
        super().__init__(graph)
        self.graph = graph
        self.root = root


if __name__ == "__main__":
    import matplotlib.pyplot as plt

    measurement_result = MeasurementResult.load(tag="duo_vacation")
    # measurement_result = MeasurementResult.load(tag="triple_vacation_delegation")

    agent_order = tuple(a for a in measurement_result.agents)

    causal_graph = CausalMultiThreadGraph.from_measurement(measurement_result)
    causal_graph.draw(agent_order=agent_order)
    sorted_nodes = list(nx.topological_sort(causal_graph.graph))
    target_node = sorted_nodes[50]

    graph = causal_graph.causal_subgraph(target_node)
    causal_graph.draw(
        agent_order=agent_order,
        nodelist=list(graph.graph.nodes),
        color="red",
    )

    graph = causal_graph.effect_subgraph(target_node)
    causal_graph.draw(
        agent_order=agent_order,
        nodelist=list(graph.graph.nodes),
        color="green",
    )

    causal_graph.draw(
        agent_order=agent_order,
        nodelist=[target_node],
        color="yellow",
    )

    plt.tight_layout()
    plt.show()
