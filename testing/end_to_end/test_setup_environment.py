import time
from uuid import uuid4

import pytest

from testing.end_to_end import APIRequestContext, api_request_context
from testing.end_to_end.company import Company


@pytest.mark.skip()
def test_initialize_assistant(api_request_context: APIRequestContext) -> None:
    alice = api_request_context.post(f"/employee").json()
    bob = api_request_context.post(f"/employee").json()

    print(alice, bob)

    api_request_context.post(
        f"/employee/relation",
        data={
            "from_employee_id": alice["employee_id"],
            "to_employee_id": bob["employee_id"],
        },
    ).json()

    time.sleep(1)

    api_request_context.delete(
        f"/employee/relation",
        data={
            "from_employee_id": alice["employee_id"],
            "to_employee_id": bob["employee_id"],
        },
    ).json()

    api_request_context.delete(f"/employee/{alice["employee_id"]}")
    api_request_context.delete(f"/employee/{bob["employee_id"]}")


@pytest.mark.skip()
def test_employee_integration(api_request_context: APIRequestContext) -> None:
    alice = api_request_context.post(f"/employee").json()

    api_request_context.post(
        f"/employee/{alice["employee_id"]}/integration",
        data={
            "external_employee_id": "alc",
        },
    )

    response = api_request_context.post(
        f"/employee/{str(uuid4())}/integration",
        data={
            "external_employee_id": "alc",
        },
    )
    assert response.status == 400

    api_request_context.delete(f"/employee/{alice["employee_id"]}/integration")

    response = api_request_context.delete(
        f"/employee/{alice["employee_id"]}/integration"
    )
    assert response.status == 400

    api_request_context.delete(f"/employee/{alice["employee_id"]}")


def test_setup_websocket(api_request_context: APIRequestContext) -> None:
    company = Company.from_file("./testing/end_to_end/company.yaml")

    employees = []
    for team in company.teams:
        for member in team.members:
            employee = api_request_context.post(f"/employee").json()
            api_request_context.post(
                f"/employee/{employee["employee_id"]}/integration",
                data={
                    "external_employee_id": member.id,
                    "external_employee_name": member.name,
                },
            )
            employees.append(employee)

    for employee_i in range(len(employees)):
        for employee_j in range(employee_i + 1, len(employees)):
            api_request_context.post(
                f"/employee/relation",
                data={
                    "from_employee_id": employees[employee_i]["employee_id"],
                    "to_employee_id": employees[employee_j]["employee_id"],
                },
            ).json()

    # for employee in employees:
    #     api_request_context.delete(f"/employee/{employee["employee_id"]}")
