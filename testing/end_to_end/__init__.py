from typing import Generator

import pytest
from playwright.sync_api import APIRequestContext, Playwright

from information_flows.settings import settings


@pytest.fixture(scope="session")
def api_request_context(
    playwright: Playwright,
) -> Generator[APIRequestContext, None, None]:
    request_context = playwright.request.new_context(
        base_url=f"http://localhost:{settings.PORT}",
    )
    yield request_context
    request_context.dispose()
