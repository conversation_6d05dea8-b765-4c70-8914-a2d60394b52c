# Testing

The folder contains testing utilities and a set of test scenarios. Refer to our [wiki](https://newgit.anadea.co/anadea/oyster/information-flows/-/wikis/testing-and-evaluation) for more details on testing and evaluation approaches we use in this project and the description of the testing and evaluation mechanisms. Each test scenario is either a rigid test, a simulation, or solely an evaluation, if a test case is manual.

## Conventions

A test has a unique identifier, a name in the form of `duo_vacation`. You must use this name consistently throughout the system. A folder of a test case must be named with this identifier. A measurement should have the same name. When you are testing the system using UI measurement utilities, you should name the test in the same way. And so on...

Each test case folder has a special structure. It contains:

1. `description.md` - a natural language description in free form of the purpose of the test (what are we testing?) and how to reproduce the test.
2. `test.py` - a test script implementing a rigid or simulation test scenario. The script will perform a measurement and generate its result in the `measurement` folder.
3. `scenario.yaml` - (optional) if a test is a simulation then this is a scenario for the simulation.

If you are manually testing the system, the `test.py` can be missing, but you should download your measurement result JSON file into the `measurements` folder and reference it in an evaluation script. If your test is not a simulation, then `scenario.yaml` will be missing.

## Run

To run a rigid test or a simulation you can use standard Python testing mechanism (don't forget to make it verbose `-v` to see all of the outputs):

```shell
python -m unittest -v testing.core_agent.rigid_scenarios.greeting.test
```

## Measurements

To evaluate the system you have to measure it. A measurement session can be initialized in a test script or in the UI. Measurement results are stored in a temporary folder `measurements` in the root, which is automatically created on the first measurement. Measurements results are JSON files with recorded trajectories of agents during a measurement. Refer to the agent implementation for more details on the structure of trajectories. If you are performing the measurement in the UI, then download and paste the file into the `measurements` folder.

## Test Dataset

A list of test scenarios implemented and evaluated in this folder. Refer to folders in `rigid_scenarios` or `simulation_scenarios` folders to find tests with names corresponing to names of tests in the table below

| #   | Name                        | Description                                                                                                                                                                                      |
| --- | --------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1   | greeting                    | Just greet an agent to check that the system is operational.                                                                                                                                     |
| 2   | policy_qa                   | Ask a simple question on a topic from the company's policy.                                                                                                                                      |
| 3   | database_qa                 | Ask a question implying information retrieval from the database.                                                                                                                                 |
| 4   | duo_vacation                | A straightforward vacation request scenario with two participants: an engineer and a teamlead.                                                                                                   |
| 5   | clickup_api                 | Similar to the previous scenario, but where an agent has to perform multiple interactions with the real ClickUp API.                                                                             |
| 6   | change_dates_vacation       | A standard vacation request, but at some point an employee decides to change its mind on the selected dates of vacation.                                                                         |
| 7   | decline_vacation            | A standard vacation request, but at some point an employee decides to decline the vacation request procedure.                                                                                    |
| 8   | correct_teamlead_vacation   | A standard vacation request, but an employee says that an agent has identified a wrong teamlead.                                                                                                 |
| 9   | triple_vacation_delegation  | A vacation request with three people, an engineer, a teamlead, and an employee to which the teamlead delegates the vacation requests review.                                                     |
| 10  | triple_vacation_distraction | A vacation request with three people, an engineer, a teamlead, and an employee that has nothing to do with the vacation request, but distracts the engineer's agent with an irrelevant question. |

## Employees

A list of employees available in the test environment and which can participate in the test cases.

| Team       | Employee             | Code | Role             |
| ---------- | -------------------- | ---- | ---------------- |
| DL Team    | Dmytro Tymofiiev     | tyd  | Team Lead        |
|            | Danila Orlov         | odv  | DL Engineer      |
|            | Sergey Kachan        | sk   | DL Engineer      |
|            | Valentina Zhmura     | vzh  | Project Manager  |
|            | Sylvestr Semeshko    | ssy  | DL Engineer      |
|            | Vladyslav Churkin    | vlc  | DL Engineer      |
|            | Evelina Aleksiutenko | ale  | DL Engineer      |
|            | Yermukhamet Medetov  | erm  | DL Engineer      |
|            | Heorhii Zaborei      | zah  | DL Engineer      |
| Pink Goose | Artem Strzhyhotsky   | asr  | Team Lead        |
|            | Vasil Talkachou      | vt   | Full Stack Dev   |
|            | Olga Maslionak       | onm  | Team Coordinator |
|            | Evgeniy Khmelnikov   | ekh  | Full Stack Dev   |
|            | Kristina Kurhanska   | krk  | Python Dev       |
|            | Anton Zakharov       | zaa  | Full Stack Dev   |
