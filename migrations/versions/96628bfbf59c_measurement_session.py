"""measurement session

Revision ID: 96628bfbf59c
Revises: f8d1201e8e05
Create Date: 2025-06-26 11:09:46.317033

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "96628bfbf59c"
down_revision: Union[str, None] = "f8d1201e8e05"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "measurement",
        sa.Column("measurement_id", sa.Uuid(), nullable=False),
        sa.Column("tag", sa.String(), nullable=False),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("snapshot", sa.Text(), nullable=False),
        sa.PrimaryKeyConstraint("measurement_id"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("measurement")
