import yaml
from pydantic import BaseModel, model_validator
from rich.pretty import pprint


class Employee(BaseModel):
    id: str
    name: str
    position: str


class Team(BaseModel):
    id: str
    name: str
    manager: str
    members: list[Employee]


class Company(BaseModel):
    teams: list[Team]

    @model_validator(mode="after")
    def validate(self) -> "Company":
        for team in self.teams:
            if not any(member.id == team.manager for member in team.members):
                raise ValueError(
                    f"a manager `{team.manager}` of a team must be one of its members"
                )
        return self

    @classmethod
    def from_file(cls, filepath: str) -> "Company":
        with open(filepath) as file:
            content = yaml.safe_load(file.read())
        return cls(**content)


if __name__ == "__main__":
    with open("./company.yaml") as file:
        content = yaml.safe_load(file.read())
    pprint(Company(**content))
