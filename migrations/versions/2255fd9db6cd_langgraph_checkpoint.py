"""langgraph checkpoint

Revision ID: 2255fd9db6cd
Revises: 82cc74955d5d
Create Date: 2025-05-19 16:08:24.847220

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from langgraph.checkpoint.postgres import PostgresSaver
from psycopg_pool import ConnectionPool

from information_flows.settings import settings

# revision identifiers, used by Alembic.
revision: str = "2255fd9db6cd"
down_revision: Union[str, None] = "82cc74955d5d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    with ConnectionPool(
        settings.database_url,
        kwargs={
            "autocommit": True,
            "prepare_threshold": 0,
        },
    ) as connection_pool:
        checkpointer = PostgresSaver(connection_pool)
        checkpointer.setup()

    for table in ["checkpoints", "checkpoint_blobs", "checkpoint_writes"]:
        op.create_foreign_key(
            f"{table}_employee_fkey",
            source_table=table,
            referent_table="employee",
            local_cols=["thread_id"],
            remote_cols=["thread_id"],
            ondelete="CASCADE",
            onupdate="CASCADE",
        )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("checkpoints")
    op.drop_table("checkpoint_blobs")
    op.drop_table("checkpoint_writes")
    op.drop_table("checkpoint_migrations")
