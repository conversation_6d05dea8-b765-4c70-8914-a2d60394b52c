"""seed test environment

Revision ID: 524265638cf3
Revises: 2255fd9db6cd
Create Date: 2025-06-03 09:14:28.253358

"""

from typing import Sequence, Union
from uuid import uuid4

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import bindparam

from migrations.versions.data.company import Company

# revision identifiers, used by Alembic.
revision: str = "524265638cf3"
down_revision: Union[str, None] = "2255fd9db6cd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    company = Company.from_file("./migrations/versions/data/company.yaml")

    employees = []
    for team in company.teams:
        for member in team.members:
            employee_id = uuid4()
            op.execute(
                sa.text(
                    """
                    INSERT INTO employee(employee_id) VALUES (:employee_id)
                    """
                ).bindparams(employee_id=employee_id)
            )
            op.execute(
                sa.text(
                    """
                    INSERT INTO integration(
                        local_employee_id,
                        external_employee_id,
                        external_employee_name
                    )
                    VALUES (
                        :local_employee_id,
                        :external_employee_id,
                        :external_employee_name
                    )
                    """
                ).bindparams(
                    local_employee_id=employee_id,
                    external_employee_id=member.id,
                    external_employee_name=member.name,
                )
            )
            employees.append(employee_id)

    for employee_i in range(len(employees)):
        for employee_j in range(len(employees)):
            if employee_i != employee_j:
                op.execute(
                    sa.text(
                        """
                        INSERT INTO relation(from_employee_id, to_employee_id)
                        VALUES(:from_employee_id, :to_employee_id)
                        """
                    ).bindparams(
                        from_employee_id=employees[employee_i],
                        to_employee_id=employees[employee_j],
                    )
                )


def downgrade() -> None:
    """Downgrade schema."""
    company = Company.from_file("./migrations/versions/data/company.yaml")

    external_employee_ids = []
    for team in company.teams:
        for member in team.members:
            external_employee_ids.append(member.id)

    engine = op.get_bind()

    employee_ids = engine.scalars(
        sa.text(
            """
            SELECT local_employee_id
            FROM integration
            WHERE external_employee_id IN :external_employee_ids
            """
        ).bindparams(
            bindparam(
                "external_employee_ids",
                value=external_employee_ids,
                expanding=True,
            )
        )
    ).all()

    if employee_ids:
        op.execute(
            sa.text(
                """
                DELETE FROM employee
                WHERE employee.employee_id IN :employee_ids
                """
            ).bindparams(
                bindparam(
                    "employee_ids",
                    employee_ids,
                    expanding=True,
                )
            )
        )
