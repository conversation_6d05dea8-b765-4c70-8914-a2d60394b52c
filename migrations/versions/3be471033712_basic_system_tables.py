"""basic system tables

Revision ID: 3be471033712
Revises:
Create Date: 2025-05-07 10:27:57.416403

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3be471033712"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "employee",
        sa.Column("employee_id", sa.Uuid(), nullable=False),
        sa.Column(
            "thread_id",
            sa.String(length=256),
            sa.Computed("employee_id::varchar", persisted=True),
            nullable=False,
            unique=True,
        ),
        sa.PrimaryKeyConstraint("employee_id"),
    )
    op.create_table(
        "relation",
        sa.Column("from_employee_id", sa.Uuid(), nullable=False),
        sa.Column("to_employee_id", sa.Uuid(), nullable=False),
        sa.ForeignKeyConstraint(
            ["from_employee_id"],
            ["employee.employee_id"],
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["to_employee_id"],
            ["employee.employee_id"],
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("from_employee_id", "to_employee_id"),
    )
    op.create_table(
        "message",
        sa.Column("message_id", sa.Uuid(), nullable=False),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column("receiver_id", sa.Uuid(), nullable=False),
        sa.Column("sender_id", sa.Uuid(), nullable=False),
        sa.Column("sender_role", sa.String(length=256), nullable=False),
        sa.Column(
            "timestamp", sa.DateTime, nullable=False, server_default=sa.func.now()
        ),
        sa.ForeignKeyConstraint(
            ["sender_id"],
            ["employee.employee_id"],
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["receiver_id"],
            ["employee.employee_id"],
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("message_id"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("relation")
    op.drop_table("message")
    op.drop_table("employee")
