"""preference specification

Revision ID: 41bd698603cb
Revises: 524265638cf3
Create Date: 2025-06-11 08:32:07.332334

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "41bd698603cb"
down_revision: Union[str, None] = "524265638cf3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.add_column("employee", sa.Column("static_preferences", sa.Text(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_column("employee", "static_preferences")
