"""llm cache

Revision ID: f8d1201e8e05
Revises: 001e6116f852
Create Date: 2025-06-24 16:43:34.424577

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f8d1201e8e05"
down_revision: Union[str, None] = "001e6116f852"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "llm_cache",
        sa.Column("cache_id", sa.Uuid(), nullable=False),
        sa.Column("prompt", sa.String(), nullable=False),
        sa.Column("prompt_hash", sa.String(), nullable=False, index=True),
        sa.Column("llm", sa.String(), nullable=False),
        sa.Column("index", sa.Integer(), nullable=False),
        sa.Column("response", sa.LargeBinary(), nullable=False),
        sa.Column(
            "created_at", sa.DateTime, nullable=False, server_default=sa.func.now()
        ),
        sa.PrimaryKeyConstraint("cache_id"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("llm_cache")
