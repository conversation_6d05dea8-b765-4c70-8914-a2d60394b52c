"""external_database_integration

Revision ID: 82cc74955d5d
Revises: 3be471033712
Create Date: 2025-05-07 13:56:19.214649

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "82cc74955d5d"
down_revision: Union[str, None] = "3be471033712"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "integration",
        sa.Column("local_employee_id", sa.Uuid(), nullable=False),
        sa.Column("external_employee_id", sa.String(length=256), nullable=False),
        sa.Column("external_employee_name", sa.Text, nullable=False),
        sa.ForeignKeyConstraint(
            ["local_employee_id"],
            ["employee.employee_id"],
            onupdate="CASCADE",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("local_employee_id"),
        sa.UniqueConstraint("external_employee_id"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("integration")
