"""Add chat and task tables

Revision ID: 8f2c4d3e9a1b
Revises: 96628bfbf59c
Create Date: 2025-08-05 15:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '8f2c4d3e9a1b'
down_revision: Union[str, None] = '96628bfbf59c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop task_status enum if it exists (from previous failed migration)
    op.execute("DROP TYPE IF EXISTS task_status")
    
    # Create chat table
    op.create_table(
        'chat',
        sa.Column('id', sa.Uuid, nullable=False),
        sa.Column('employee_id', sa.Uuid, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['employee_id'], ['employee.employee_id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create task table (enum will be created automatically)
    op.create_table(
        'task',
        sa.Column('id', sa.Uuid, nullable=False),
        sa.Column('chat_id', sa.Uuid, nullable=False),
        sa.Column('task_identifier', sa.String(length=255), nullable=False),
        sa.Column('status', sa.Enum('active', 'completed', 'queued', 'failed', name='task_status'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['chat_id'], ['chat.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create chat_message association table
    op.create_table(
        'chat_message',
        sa.Column('chat_id', sa.Uuid, nullable=False),
        sa.Column('message_id', sa.Uuid, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['chat_id'], ['chat.id'], ),
        sa.ForeignKeyConstraint(['message_id'], ['message.message_id'], ),
        sa.PrimaryKeyConstraint('chat_id', 'message_id')
    )

    # Create indexes
    op.create_index(op.f('ix_chat_employee_id'), 'chat', ['employee_id'], unique=False)
    op.create_index(op.f('ix_task_chat_id'), 'task', ['chat_id'], unique=False)
    op.create_index(op.f('ix_task_status'), 'task', ['status'], unique=False)
    op.create_index(op.f('ix_task_task_identifier'), 'task', ['task_identifier'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_task_task_identifier'), table_name='task')
    op.drop_index(op.f('ix_task_status'), table_name='task')
    op.drop_index(op.f('ix_task_chat_id'), table_name='task')
    op.drop_index(op.f('ix_chat_employee_id'), table_name='chat')

    # Drop tables
    op.drop_table('chat_message')
    op.drop_table('task')
    op.drop_table('chat')

    # Drop enum if exists
    op.execute("DROP TYPE IF EXISTS task_status")
