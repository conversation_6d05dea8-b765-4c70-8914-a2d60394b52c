#%%
from IPython.display import display, Image
from uuid import uuid4
import json
import asyncio
import pathlib
import tempfile
from pydantic.alias_generators import to_pascal

from typing import Literal, Annotated, Union, TypedDict, Any, Optional
from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
)
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.tools import (
    tool,
    BaseTool,
    render_text_description_and_args,
)
from langchain_core.messages import (
    HumanMessage,
    AIMessage,
    SystemMessage,
    ToolCall,
)
from langchain.chat_models import init_chat_model
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver

from information_flows.settings import settings
from information_flows.agents.core_agent.graph import create_agent_graph
from information_flows.agents.core_agent.observations import (
    BaseObservation,
    Observation,
    ChiefMessage,
    AssistantMessage,
    ToolMessage,
    ActionChoiceMessage
)
from langgraph.checkpoint.memory import MemorySaver
import numpy as np
import pandas as pd

from langchain_core.prompts.string import DEFAULT_FORMATTER_MAPPING
from pydantic import BaseModel, Field, create_model
from pydantic.fields import FieldInfo
from rich.pretty import pprint

import nest_asyncio

nest_asyncio.apply()
#%%
@tool
def get_books(category: Literal["science", "fiction"]) -> str:
    """Return books by category."""
    return "No books found!"


@tool
def get_journals(category: Literal["science", "fiction"]) -> str:
    """Return journals by category."""
    return "No journals found!"


tools = [get_books, get_journals]


def format_tools(tools: list[BaseTool]):
    for tool in tools:
        print(tool.name, tool.description, tool.args)


print(render_text_description_and_args(tools))
#%%
llm = ChatOpenAI(
    model="gpt-4.1-mini",
    api_key=settings.OPENAI_API_KEY,
    temperature=0,
)
#%%
memory = MemorySaver()
memory.aget({"configurable": {"thread_id": '_'}})
#%%
{
    "properties": {"question": {"title": "Question", "type": "string"}},
    "required": ["question"],
    "title": "get_company_policyArguments",
    "type": "object",
}, {
    "$defs": {
        "Date": {
            "properties": {
                "day": {
                    "description": "Required format: 1-31",
                    "title": "Day",
                    "type": "integer",
                },
                "month": {
                    "description": "Required format: 1-12",
                    "title": "Month",
                    "type": "integer",
                },
                "year": {
                    "description": "Required format (4-digit): YYYY",
                    "title": "Year",
                    "type": "integer",
                },
            },
            "required": ["day", "month", "year"],
            "title": "Date",
            "type": "object",
        }
    },
    "properties": {
        "requester_id": {"title": "Requester Id", "type": "string"},
        "assignee_id": {"title": "Assignee Id", "type": "string"},
        "from_date": {"$ref": "#/$defs/Date"},
        "to_date": {"$ref": "#/$defs/Date"},
    },
    "required": ["requester_id", "assignee_id", "from_date", "to_date"],
    "title": "clickup_vacation_requestArguments",
    "type": "object",
}, {
    "properties": {},
    "title": "list_vacation_requestsArguments",
    "type": "object",
}, {
    "properties": {"question": {"title": "Question", "type": "string"}},
    "required": ["question"],
    "title": "ask_company_databaseArguments",
    "type": "object",
}, {
    "properties": {"problem": {"title": "Problem", "type": "string"}},
    "required": ["problem"],
    "title": "ask_coding_agentArguments",
    "type": "object",
}
#%%
tools[0].args_schema.model_json_schema()
#%%
llm = ChatOpenAI(
    model="gpt-4.1-mini",
    api_key=settings.OPENAI_API_KEY,
    temperature=0,
    logprobs=True,
    top_logprobs=5,
)
#%%
response = llm.invoke(
    "What is the iconic building of the urban-type settlement of Voronovytsia in Ukraine?"
)
# response = llm.invoke("Question: What is the iconic building of the urban-type settlement of Voronovytsia in Ukraine? Answer: Voronovytsia School. (Output a single number for score in range 1-5 for correctness of the answer)")
#%%
float(
    (np.exp(-0.57) * 2 + np.exp(-0.82) * 1 + np.exp(-14.32) * 3)
    / (np.exp(-0.57) + np.exp(-0.82) + np.exp(-14.32))
)
#%%
for token in response.response_metadata["logprobs"]["content"]:
    print(f"'{token["token"]}' ~ {np.exp(token["top_logprobs"][0]["logprob"]):.3f}")
#%%
tokens = []
for token in response.response_metadata["logprobs"]["content"]:
    tokens.append(
        {"token": f"'{token["token"]}'", "probability": np.exp(token["logprob"])}
    )
pd.DataFrame(tokens)
#%%
import langchain

langchain.verbose = False
langchain.debug = False
#%%
prompt = ChatPromptTemplate.from_messages([MessagesPlaceholder("messages")])


@tool
def get_books(category: Literal["science", "fiction"]) -> str:
    """Return books by category."""
    return "No books found!"


messages = [HumanMessage("What science books are there?")]

chain = prompt | llm.bind_tools([get_books])

response: AIMessage = chain.invoke({"messages": messages})
messages.append(response)

tool_call = response.tool_calls[0]
tool_output = get_books.invoke(tool_call["args"])
messages.append(ToolMessage(content=tool_output, tool_call_id=tool_call["id"]))

response = chain.invoke({"messages": messages})

print(response.content)
#%%
class ChiefMessage(HumanMessage):
    def __init__(self, content: str, **kwargs):
        super().__init__(content=f"{content}", **kwargs)


class AssistantMessage(AIMessage):
    def __init__(self, content: str, **kwargs):
        super().__init__(content=f"{content}", **kwargs)


prompt = ChatPromptTemplate.from_messages(
    [
        ChiefMessage("Hello! How many planets there are?"),
        AssistantMessage("Hey, I can name the number of planets! It is..."),
    ]
).invoke({})
#%%
llm.invoke("Hello!")
#%%
display(
    Image(
        create_agent_graph(MemorySaver(), [])
        .get_graph()
        .draw_mermaid_png(draw_method=MermaidDrawMethod.PYPPETEER)
    )
)